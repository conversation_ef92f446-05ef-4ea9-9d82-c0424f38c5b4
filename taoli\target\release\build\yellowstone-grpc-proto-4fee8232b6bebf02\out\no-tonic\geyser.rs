// This file is @generated by prost-build.
#[derive(<PERSON><PERSON>, PartialEq, ::prost::Message)]
pub struct SubscribeRequest {
    #[prost(map = "string, message", tag = "1")]
    pub accounts: ::std::collections::HashMap<
        ::prost::alloc::string::String,
        SubscribeRequestFilterAccounts,
    >,
    #[prost(map = "string, message", tag = "2")]
    pub slots: ::std::collections::HashMap<
        ::prost::alloc::string::String,
        SubscribeRequestFilterSlots,
    >,
    #[prost(map = "string, message", tag = "3")]
    pub transactions: ::std::collections::HashMap<
        ::prost::alloc::string::String,
        SubscribeRequestFilterTransactions,
    >,
    #[prost(map = "string, message", tag = "10")]
    pub transactions_status: ::std::collections::HashMap<
        ::prost::alloc::string::String,
        SubscribeRequestFilterTransactions,
    >,
    #[prost(map = "string, message", tag = "4")]
    pub blocks: ::std::collections::HashMap<
        ::prost::alloc::string::String,
        SubscribeRequestFilterBlocks,
    >,
    #[prost(map = "string, message", tag = "5")]
    pub blocks_meta: ::std::collections::HashMap<
        ::prost::alloc::string::String,
        SubscribeRequestFilterBlocksMeta,
    >,
    #[prost(map = "string, message", tag = "8")]
    pub entry: ::std::collections::HashMap<
        ::prost::alloc::string::String,
        SubscribeRequestFilterEntry,
    >,
    #[prost(enumeration = "CommitmentLevel", optional, tag = "6")]
    pub commitment: ::core::option::Option<i32>,
    #[prost(message, repeated, tag = "7")]
    pub accounts_data_slice: ::prost::alloc::vec::Vec<SubscribeRequestAccountsDataSlice>,
    #[prost(message, optional, tag = "9")]
    pub ping: ::core::option::Option<SubscribeRequestPing>,
    #[prost(uint64, optional, tag = "11")]
    pub from_slot: ::core::option::Option<u64>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeRequestFilterAccounts {
    #[prost(string, repeated, tag = "2")]
    pub account: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    #[prost(string, repeated, tag = "3")]
    pub owner: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    #[prost(message, repeated, tag = "4")]
    pub filters: ::prost::alloc::vec::Vec<SubscribeRequestFilterAccountsFilter>,
    #[prost(bool, optional, tag = "5")]
    pub nonempty_txn_signature: ::core::option::Option<bool>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeRequestFilterAccountsFilter {
    #[prost(
        oneof = "subscribe_request_filter_accounts_filter::Filter",
        tags = "1, 2, 3, 4"
    )]
    pub filter: ::core::option::Option<subscribe_request_filter_accounts_filter::Filter>,
}
/// Nested message and enum types in `SubscribeRequestFilterAccountsFilter`.
pub mod subscribe_request_filter_accounts_filter {
    #[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Filter {
        #[prost(message, tag = "1")]
        Memcmp(super::SubscribeRequestFilterAccountsFilterMemcmp),
        #[prost(uint64, tag = "2")]
        Datasize(u64),
        #[prost(bool, tag = "3")]
        TokenAccountState(bool),
        #[prost(message, tag = "4")]
        Lamports(super::SubscribeRequestFilterAccountsFilterLamports),
    }
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeRequestFilterAccountsFilterMemcmp {
    #[prost(uint64, tag = "1")]
    pub offset: u64,
    #[prost(
        oneof = "subscribe_request_filter_accounts_filter_memcmp::Data",
        tags = "2, 3, 4"
    )]
    pub data: ::core::option::Option<
        subscribe_request_filter_accounts_filter_memcmp::Data,
    >,
}
/// Nested message and enum types in `SubscribeRequestFilterAccountsFilterMemcmp`.
pub mod subscribe_request_filter_accounts_filter_memcmp {
    #[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Data {
        #[prost(bytes, tag = "2")]
        Bytes(::prost::alloc::vec::Vec<u8>),
        #[prost(string, tag = "3")]
        Base58(::prost::alloc::string::String),
        #[prost(string, tag = "4")]
        Base64(::prost::alloc::string::String),
    }
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct SubscribeRequestFilterAccountsFilterLamports {
    #[prost(
        oneof = "subscribe_request_filter_accounts_filter_lamports::Cmp",
        tags = "1, 2, 3, 4"
    )]
    pub cmp: ::core::option::Option<
        subscribe_request_filter_accounts_filter_lamports::Cmp,
    >,
}
/// Nested message and enum types in `SubscribeRequestFilterAccountsFilterLamports`.
pub mod subscribe_request_filter_accounts_filter_lamports {
    #[derive(Clone, Copy, PartialEq, ::prost::Oneof)]
    pub enum Cmp {
        #[prost(uint64, tag = "1")]
        Eq(u64),
        #[prost(uint64, tag = "2")]
        Ne(u64),
        #[prost(uint64, tag = "3")]
        Lt(u64),
        #[prost(uint64, tag = "4")]
        Gt(u64),
    }
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct SubscribeRequestFilterSlots {
    #[prost(bool, optional, tag = "1")]
    pub filter_by_commitment: ::core::option::Option<bool>,
    #[prost(bool, optional, tag = "2")]
    pub interslot_updates: ::core::option::Option<bool>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeRequestFilterTransactions {
    #[prost(bool, optional, tag = "1")]
    pub vote: ::core::option::Option<bool>,
    #[prost(bool, optional, tag = "2")]
    pub failed: ::core::option::Option<bool>,
    #[prost(string, optional, tag = "5")]
    pub signature: ::core::option::Option<::prost::alloc::string::String>,
    #[prost(string, repeated, tag = "3")]
    pub account_include: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    #[prost(string, repeated, tag = "4")]
    pub account_exclude: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    #[prost(string, repeated, tag = "6")]
    pub account_required: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeRequestFilterBlocks {
    #[prost(string, repeated, tag = "1")]
    pub account_include: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    #[prost(bool, optional, tag = "2")]
    pub include_transactions: ::core::option::Option<bool>,
    #[prost(bool, optional, tag = "3")]
    pub include_accounts: ::core::option::Option<bool>,
    #[prost(bool, optional, tag = "4")]
    pub include_entries: ::core::option::Option<bool>,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct SubscribeRequestFilterBlocksMeta {}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct SubscribeRequestFilterEntry {}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct SubscribeRequestAccountsDataSlice {
    #[prost(uint64, tag = "1")]
    pub offset: u64,
    #[prost(uint64, tag = "2")]
    pub length: u64,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct SubscribeRequestPing {
    #[prost(int32, tag = "1")]
    pub id: i32,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeUpdate {
    #[prost(string, repeated, tag = "1")]
    pub filters: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    #[prost(message, optional, tag = "11")]
    pub created_at: ::core::option::Option<::prost_types::Timestamp>,
    #[prost(
        oneof = "subscribe_update::UpdateOneof",
        tags = "2, 3, 4, 10, 5, 6, 9, 7, 8"
    )]
    pub update_oneof: ::core::option::Option<subscribe_update::UpdateOneof>,
}
/// Nested message and enum types in `SubscribeUpdate`.
pub mod subscribe_update {
    #[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum UpdateOneof {
        #[prost(message, tag = "2")]
        Account(super::SubscribeUpdateAccount),
        #[prost(message, tag = "3")]
        Slot(super::SubscribeUpdateSlot),
        #[prost(message, tag = "4")]
        Transaction(super::SubscribeUpdateTransaction),
        #[prost(message, tag = "10")]
        TransactionStatus(super::SubscribeUpdateTransactionStatus),
        #[prost(message, tag = "5")]
        Block(super::SubscribeUpdateBlock),
        #[prost(message, tag = "6")]
        Ping(super::SubscribeUpdatePing),
        #[prost(message, tag = "9")]
        Pong(super::SubscribeUpdatePong),
        #[prost(message, tag = "7")]
        BlockMeta(super::SubscribeUpdateBlockMeta),
        #[prost(message, tag = "8")]
        Entry(super::SubscribeUpdateEntry),
    }
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeUpdateAccount {
    #[prost(message, optional, tag = "1")]
    pub account: ::core::option::Option<SubscribeUpdateAccountInfo>,
    #[prost(uint64, tag = "2")]
    pub slot: u64,
    #[prost(bool, tag = "3")]
    pub is_startup: bool,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeUpdateAccountInfo {
    #[prost(bytes = "vec", tag = "1")]
    pub pubkey: ::prost::alloc::vec::Vec<u8>,
    #[prost(uint64, tag = "2")]
    pub lamports: u64,
    #[prost(bytes = "vec", tag = "3")]
    pub owner: ::prost::alloc::vec::Vec<u8>,
    #[prost(bool, tag = "4")]
    pub executable: bool,
    #[prost(uint64, tag = "5")]
    pub rent_epoch: u64,
    #[prost(bytes = "vec", tag = "6")]
    pub data: ::prost::alloc::vec::Vec<u8>,
    #[prost(uint64, tag = "7")]
    pub write_version: u64,
    #[prost(bytes = "vec", optional, tag = "8")]
    pub txn_signature: ::core::option::Option<::prost::alloc::vec::Vec<u8>>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeUpdateSlot {
    #[prost(uint64, tag = "1")]
    pub slot: u64,
    #[prost(uint64, optional, tag = "2")]
    pub parent: ::core::option::Option<u64>,
    #[prost(enumeration = "CommitmentLevel", tag = "3")]
    pub status: i32,
    #[prost(string, optional, tag = "4")]
    pub dead_error: ::core::option::Option<::prost::alloc::string::String>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeUpdateTransaction {
    #[prost(message, optional, tag = "1")]
    pub transaction: ::core::option::Option<SubscribeUpdateTransactionInfo>,
    #[prost(uint64, tag = "2")]
    pub slot: u64,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeUpdateTransactionInfo {
    #[prost(bytes = "vec", tag = "1")]
    pub signature: ::prost::alloc::vec::Vec<u8>,
    #[prost(bool, tag = "2")]
    pub is_vote: bool,
    #[prost(message, optional, tag = "3")]
    pub transaction: ::core::option::Option<
        super::solana::storage::confirmed_block::Transaction,
    >,
    #[prost(message, optional, tag = "4")]
    pub meta: ::core::option::Option<
        super::solana::storage::confirmed_block::TransactionStatusMeta,
    >,
    #[prost(uint64, tag = "5")]
    pub index: u64,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeUpdateTransactionStatus {
    #[prost(uint64, tag = "1")]
    pub slot: u64,
    #[prost(bytes = "vec", tag = "2")]
    pub signature: ::prost::alloc::vec::Vec<u8>,
    #[prost(bool, tag = "3")]
    pub is_vote: bool,
    #[prost(uint64, tag = "4")]
    pub index: u64,
    #[prost(message, optional, tag = "5")]
    pub err: ::core::option::Option<
        super::solana::storage::confirmed_block::TransactionError,
    >,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeUpdateBlock {
    #[prost(uint64, tag = "1")]
    pub slot: u64,
    #[prost(string, tag = "2")]
    pub blockhash: ::prost::alloc::string::String,
    #[prost(message, optional, tag = "3")]
    pub rewards: ::core::option::Option<
        super::solana::storage::confirmed_block::Rewards,
    >,
    #[prost(message, optional, tag = "4")]
    pub block_time: ::core::option::Option<
        super::solana::storage::confirmed_block::UnixTimestamp,
    >,
    #[prost(message, optional, tag = "5")]
    pub block_height: ::core::option::Option<
        super::solana::storage::confirmed_block::BlockHeight,
    >,
    #[prost(uint64, tag = "7")]
    pub parent_slot: u64,
    #[prost(string, tag = "8")]
    pub parent_blockhash: ::prost::alloc::string::String,
    #[prost(uint64, tag = "9")]
    pub executed_transaction_count: u64,
    #[prost(message, repeated, tag = "6")]
    pub transactions: ::prost::alloc::vec::Vec<SubscribeUpdateTransactionInfo>,
    #[prost(uint64, tag = "10")]
    pub updated_account_count: u64,
    #[prost(message, repeated, tag = "11")]
    pub accounts: ::prost::alloc::vec::Vec<SubscribeUpdateAccountInfo>,
    #[prost(uint64, tag = "12")]
    pub entries_count: u64,
    #[prost(message, repeated, tag = "13")]
    pub entries: ::prost::alloc::vec::Vec<SubscribeUpdateEntry>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeUpdateBlockMeta {
    #[prost(uint64, tag = "1")]
    pub slot: u64,
    #[prost(string, tag = "2")]
    pub blockhash: ::prost::alloc::string::String,
    #[prost(message, optional, tag = "3")]
    pub rewards: ::core::option::Option<
        super::solana::storage::confirmed_block::Rewards,
    >,
    #[prost(message, optional, tag = "4")]
    pub block_time: ::core::option::Option<
        super::solana::storage::confirmed_block::UnixTimestamp,
    >,
    #[prost(message, optional, tag = "5")]
    pub block_height: ::core::option::Option<
        super::solana::storage::confirmed_block::BlockHeight,
    >,
    #[prost(uint64, tag = "6")]
    pub parent_slot: u64,
    #[prost(string, tag = "7")]
    pub parent_blockhash: ::prost::alloc::string::String,
    #[prost(uint64, tag = "8")]
    pub executed_transaction_count: u64,
    #[prost(uint64, tag = "9")]
    pub entries_count: u64,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeUpdateEntry {
    #[prost(uint64, tag = "1")]
    pub slot: u64,
    #[prost(uint64, tag = "2")]
    pub index: u64,
    #[prost(uint64, tag = "3")]
    pub num_hashes: u64,
    #[prost(bytes = "vec", tag = "4")]
    pub hash: ::prost::alloc::vec::Vec<u8>,
    #[prost(uint64, tag = "5")]
    pub executed_transaction_count: u64,
    /// added in v1.18, for solana 1.17 value is always 0
    #[prost(uint64, tag = "6")]
    pub starting_transaction_index: u64,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct SubscribeUpdatePing {}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct SubscribeUpdatePong {
    #[prost(int32, tag = "1")]
    pub id: i32,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct PingRequest {
    #[prost(int32, tag = "1")]
    pub count: i32,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct PongResponse {
    #[prost(int32, tag = "1")]
    pub count: i32,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct GetLatestBlockhashRequest {
    #[prost(enumeration = "CommitmentLevel", optional, tag = "1")]
    pub commitment: ::core::option::Option<i32>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct GetLatestBlockhashResponse {
    #[prost(uint64, tag = "1")]
    pub slot: u64,
    #[prost(string, tag = "2")]
    pub blockhash: ::prost::alloc::string::String,
    #[prost(uint64, tag = "3")]
    pub last_valid_block_height: u64,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct GetBlockHeightRequest {
    #[prost(enumeration = "CommitmentLevel", optional, tag = "1")]
    pub commitment: ::core::option::Option<i32>,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct GetBlockHeightResponse {
    #[prost(uint64, tag = "1")]
    pub block_height: u64,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct GetSlotRequest {
    #[prost(enumeration = "CommitmentLevel", optional, tag = "1")]
    pub commitment: ::core::option::Option<i32>,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct GetSlotResponse {
    #[prost(uint64, tag = "1")]
    pub slot: u64,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct GetVersionRequest {}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct GetVersionResponse {
    #[prost(string, tag = "1")]
    pub version: ::prost::alloc::string::String,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct IsBlockhashValidRequest {
    #[prost(string, tag = "1")]
    pub blockhash: ::prost::alloc::string::String,
    #[prost(enumeration = "CommitmentLevel", optional, tag = "2")]
    pub commitment: ::core::option::Option<i32>,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct IsBlockhashValidResponse {
    #[prost(uint64, tag = "1")]
    pub slot: u64,
    #[prost(bool, tag = "2")]
    pub valid: bool,
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum CommitmentLevel {
    Processed = 0,
    Confirmed = 1,
    Finalized = 2,
    FirstShredReceived = 3,
    Completed = 4,
    CreatedBank = 5,
    Dead = 6,
}
impl CommitmentLevel {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            Self::Processed => "PROCESSED",
            Self::Confirmed => "CONFIRMED",
            Self::Finalized => "FINALIZED",
            Self::FirstShredReceived => "FIRST_SHRED_RECEIVED",
            Self::Completed => "COMPLETED",
            Self::CreatedBank => "CREATED_BANK",
            Self::Dead => "DEAD",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "PROCESSED" => Some(Self::Processed),
            "CONFIRMED" => Some(Self::Confirmed),
            "FINALIZED" => Some(Self::Finalized),
            "FIRST_SHRED_RECEIVED" => Some(Self::FirstShredReceived),
            "COMPLETED" => Some(Self::Completed),
            "CREATED_BANK" => Some(Self::CreatedBank),
            "DEAD" => Some(Self::Dead),
            _ => None,
        }
    }
}
