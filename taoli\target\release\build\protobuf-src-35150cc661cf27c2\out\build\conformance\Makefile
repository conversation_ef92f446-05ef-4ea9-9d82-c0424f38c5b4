# Makefile.in generated by automake 1.16.5 from Makefile.am.
# conformance/Makefile.  Generated from Makefile.in by configure.

# Copyright (C) 1994-2021 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.



VPATH = /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/conformance
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/protobuf
pkgincludedir = $(includedir)/protobuf
pkglibdir = $(libdir)/protobuf
pkglibexecdir = $(libexecdir)/protobuf
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = x86_64-pc-mingw64
host_triplet = x86_64-pc-mingw64
target_triplet = x86_64-pc-mingw64
bin_PROGRAMS = conformance-test-runner$(EXEEXT) \
	conformance-cpp$(EXEEXT) $(am__EXEEXT_1)
#am__append_1 = conformance-objc
subdir = conformance
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/ac_system_extensions.m4 \
	$(top_srcdir)/m4/acx_check_suncc.m4 \
	$(top_srcdir)/m4/ax_cxx_compile_stdcxx.m4 \
	$(top_srcdir)/m4/ax_prog_cc_for_build.m4 \
	$(top_srcdir)/m4/ax_prog_cxx_for_build.m4 \
	$(top_srcdir)/m4/ax_pthread.m4 $(top_srcdir)/m4/libtool.m4 \
	$(top_srcdir)/m4/ltoptions.m4 $(top_srcdir)/m4/ltsugar.m4 \
	$(top_srcdir)/m4/ltversion.m4 $(top_srcdir)/m4/lt~obsolete.m4 \
	$(top_srcdir)/m4/stl_hash.m4 $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
#am__EXEEXT_1 = conformance-objc$(EXEEXT)
am__installdirs = "$(DESTDIR)$(bindir)"
PROGRAMS = $(bin_PROGRAMS)
am_conformance_cpp_OBJECTS =  \
	conformance_cpp-conformance_cpp.$(OBJEXT)
am__dirstamp = $(am__leading_dot)dirstamp
nodist_conformance_cpp_OBJECTS =  \
	conformance_cpp-conformance.pb.$(OBJEXT) \
	google/protobuf/conformance_cpp-test_messages_proto3.pb.$(OBJEXT) \
	google/protobuf/conformance_cpp-test_messages_proto2.pb.$(OBJEXT)
conformance_cpp_OBJECTS = $(am_conformance_cpp_OBJECTS) \
	$(nodist_conformance_cpp_OBJECTS)
conformance_cpp_DEPENDENCIES = $(top_srcdir)/src/libprotobuf.la
AM_V_lt = $(am__v_lt_$(V))
am__v_lt_ = $(am__v_lt_$(AM_DEFAULT_VERBOSITY))
am__v_lt_0 = --silent
am__v_lt_1 = 
am__conformance_objc_SOURCES_DIST = conformance_objc.m \
	../objectivec/GPBProtocolBuffers.m
#am_conformance_objc_OBJECTS = conformance_objc-conformance_objc.$(OBJEXT) \
#	../objectivec/conformance_objc-GPBProtocolBuffers.$(OBJEXT)
#nodist_conformance_objc_OBJECTS = conformance_objc-Conformance.pbobjc.$(OBJEXT) \
#	google/protobuf/conformance_objc-TestMessagesProto2.pbobjc.$(OBJEXT) \
#	google/protobuf/conformance_objc-TestMessagesProto3.pbobjc.$(OBJEXT)
conformance_objc_OBJECTS = $(am_conformance_objc_OBJECTS) \
	$(nodist_conformance_objc_OBJECTS)
conformance_objc_LDADD = $(LDADD)
conformance_objc_LINK = $(LIBTOOL) $(AM_V_lt) $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(OBJCLD) $(AM_OBJCFLAGS) \
	$(OBJCFLAGS) $(conformance_objc_LDFLAGS) $(LDFLAGS) -o $@
am_conformance_test_runner_OBJECTS =  \
	conformance_test_runner-conformance_test.$(OBJEXT) \
	conformance_test_runner-conformance_test_main.$(OBJEXT) \
	conformance_test_runner-binary_json_conformance_suite.$(OBJEXT) \
	conformance_test_runner-text_format_conformance_suite.$(OBJEXT) \
	conformance_test_runner-conformance_test_runner.$(OBJEXT) \
	third_party/jsoncpp/conformance_test_runner-jsoncpp.$(OBJEXT)
nodist_conformance_test_runner_OBJECTS =  \
	conformance_test_runner-conformance.pb.$(OBJEXT) \
	google/protobuf/conformance_test_runner-test_messages_proto3.pb.$(OBJEXT) \
	google/protobuf/conformance_test_runner-test_messages_proto2.pb.$(OBJEXT)
conformance_test_runner_OBJECTS =  \
	$(am_conformance_test_runner_OBJECTS) \
	$(nodist_conformance_test_runner_OBJECTS)
conformance_test_runner_DEPENDENCIES =  \
	$(top_srcdir)/src/libprotobuf.la
conformance_test_runner_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CXXLD) \
	$(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) $(AM_LDFLAGS) \
	$(LDFLAGS) -o $@
AM_V_P = $(am__v_P_$(V))
am__v_P_ = $(am__v_P_$(AM_DEFAULT_VERBOSITY))
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_$(V))
am__v_GEN_ = $(am__v_GEN_$(AM_DEFAULT_VERBOSITY))
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_$(V))
am__v_at_ = $(am__v_at_$(AM_DEFAULT_VERBOSITY))
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = -I. -I$(srcdir) -I$(top_builddir)
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade = ../objectivec/$(DEPDIR)/conformance_objc-GPBProtocolBuffers.Po \
	./$(DEPDIR)/conformance_cpp-conformance.pb.Po \
	./$(DEPDIR)/conformance_cpp-conformance_cpp.Po \
	./$(DEPDIR)/conformance_objc-Conformance.pbobjc.Po \
	./$(DEPDIR)/conformance_objc-conformance_objc.Po \
	./$(DEPDIR)/conformance_test_runner-binary_json_conformance_suite.Po \
	./$(DEPDIR)/conformance_test_runner-conformance.pb.Po \
	./$(DEPDIR)/conformance_test_runner-conformance_test.Po \
	./$(DEPDIR)/conformance_test_runner-conformance_test_main.Po \
	./$(DEPDIR)/conformance_test_runner-conformance_test_runner.Po \
	./$(DEPDIR)/conformance_test_runner-text_format_conformance_suite.Po \
	google/protobuf/$(DEPDIR)/conformance_cpp-test_messages_proto2.pb.Po \
	google/protobuf/$(DEPDIR)/conformance_cpp-test_messages_proto3.pb.Po \
	google/protobuf/$(DEPDIR)/conformance_objc-TestMessagesProto2.pbobjc.Po \
	google/protobuf/$(DEPDIR)/conformance_objc-TestMessagesProto3.pbobjc.Po \
	google/protobuf/$(DEPDIR)/conformance_test_runner-test_messages_proto2.pb.Po \
	google/protobuf/$(DEPDIR)/conformance_test_runner-test_messages_proto3.pb.Po \
	third_party/jsoncpp/$(DEPDIR)/conformance_test_runner-jsoncpp.Po
am__mv = mv -f
CXXCOMPILE = $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) \
	$(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS)
LTCXXCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CXXFLAGS) $(CXXFLAGS)
AM_V_CXX = $(am__v_CXX_$(V))
am__v_CXX_ = $(am__v_CXX_$(AM_DEFAULT_VERBOSITY))
am__v_CXX_0 = @echo "  CXX     " $@;
am__v_CXX_1 = 
CXXLD = $(CXX)
CXXLINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CXXLD) $(AM_CXXFLAGS) \
	$(CXXFLAGS) $(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CXXLD = $(am__v_CXXLD_$(V))
am__v_CXXLD_ = $(am__v_CXXLD_$(AM_DEFAULT_VERBOSITY))
am__v_CXXLD_0 = @echo "  CXXLD   " $@;
am__v_CXXLD_1 = 
OBJCCOMPILE = $(OBJC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) \
	$(AM_CPPFLAGS) $(CPPFLAGS) $(AM_OBJCFLAGS) $(OBJCFLAGS)
LTOBJCCOMPILE = $(LIBTOOL) $(AM_V_lt) $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(OBJC) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_OBJCFLAGS) $(OBJCFLAGS)
AM_V_OBJC = $(am__v_OBJC_$(V))
am__v_OBJC_ = $(am__v_OBJC_$(AM_DEFAULT_VERBOSITY))
am__v_OBJC_0 = @echo "  OBJC    " $@;
am__v_OBJC_1 = 
OBJCLD = $(OBJC)
OBJCLINK = $(LIBTOOL) $(AM_V_lt) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) \
	--mode=link $(OBJCLD) $(AM_OBJCFLAGS) $(OBJCFLAGS) \
	$(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_OBJCLD = $(am__v_OBJCLD_$(V))
am__v_OBJCLD_ = $(am__v_OBJCLD_$(AM_DEFAULT_VERBOSITY))
am__v_OBJCLD_0 = @echo "  OBJCLD  " $@;
am__v_OBJCLD_1 = 
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
LTCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_$(V))
am__v_CC_ = $(am__v_CC_$(AM_DEFAULT_VERBOSITY))
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_$(V))
am__v_CCLD_ = $(am__v_CCLD_$(AM_DEFAULT_VERBOSITY))
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
SOURCES = $(conformance_cpp_SOURCES) $(nodist_conformance_cpp_SOURCES) \
	$(conformance_objc_SOURCES) $(nodist_conformance_objc_SOURCES) \
	$(conformance_test_runner_SOURCES) \
	$(nodist_conformance_test_runner_SOURCES)
DIST_SOURCES = $(conformance_cpp_SOURCES) \
	$(am__conformance_objc_SOURCES_DIST) \
	$(conformance_test_runner_SOURCES)
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
am__DIST_COMMON = $(srcdir)/Makefile.in $(top_srcdir)/depcomp \
	README.md
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = ${SHELL} '/c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/missing' aclocal-1.16
AMTAR = $${TAR-tar}
AM_DEFAULT_VERBOSITY = 0
AR = ar
AUTOCONF = ${SHELL} '/c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/missing' autoconf
AUTOHEADER = ${SHELL} '/c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/missing' autoheader
AUTOMAKE = ${SHELL} '/c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/missing' automake-1.16
AWK = gawk
BUILD_EXEEXT = 
BUILD_OBJEXT = 
CC = gcc.exe
CCDEPMODE = depmode=gcc3
CC_FOR_BUILD = gcc
CFLAGS = -O0 -ffunction-sections -fdata-sections -m64
CFLAGS_FOR_BUILD = -g -O2
CPP = gcc.exe -E
CPPFLAGS = 
CPPFLAGS_FOR_BUILD = 
CPP_FOR_BUILD = gcc -E
CSCOPE = cscope
CTAGS = ctags
CXX = g++.exe
CXXCPP = g++.exe -E
CXXCPPFLAGS_FOR_BUILD = 
CXXCPP_FOR_BUILD = g++ -E
CXXDEPMODE = depmode=gcc3
CXXFLAGS = -O0 -ffunction-sections -fdata-sections -m64
CXXFLAGS_FOR_BUILD = -g -O2
CXX_FOR_BUILD = g++
CYGPATH_W = cygpath -w
DEFS = -DHAVE_CONFIG_H
DEPDIR = .deps
DIST_LANG = all
DLLTOOL = dlltool
DSYMUTIL = 
DUMPBIN = 
ECHO_C = 
ECHO_N = -n
ECHO_T = 
EGREP = /usr/bin/grep -E
ETAGS = etags
EXEEXT = .exe
FGREP = /usr/bin/grep -F
FILECMD = file
GREP = /usr/bin/grep
HAVE_CXX11 = 1
INSTALL = /usr/bin/install -c
INSTALL_DATA = ${INSTALL} -m 644
INSTALL_PROGRAM = ${INSTALL}
INSTALL_SCRIPT = ${INSTALL}
INSTALL_STRIP_PROGRAM = $(install_sh) -c -s
ISAINFO = 
LD = C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe
LDFLAGS = 
LDFLAGS_FOR_BUILD = 
LIBATOMIC_LIBS = 
LIBLOG_LIBS = 
LIBOBJS = 
LIBS = -lz 
LIBTOOL = $(SHELL) $(top_builddir)/libtool
LIPO = 
LN_S = cp -pR
LTLIBOBJS = 
LT_SYS_LIBRARY_PATH = 
MAINT = #
MAKEINFO = ${SHELL} '/c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/missing' makeinfo
MANIFEST_TOOL = :
MKDIR_P = /usr/bin/mkdir -p
NM = /mingw64/bin/nm -B
NMEDIT = 
OBJC = 
OBJCDEPMODE = 
OBJCFLAGS = 
OBJDUMP = objdump
OBJEXT = o
OTOOL = 
OTOOL64 = 
PACKAGE = protobuf
PACKAGE_BUGREPORT = <EMAIL>
PACKAGE_NAME = Protocol Buffers
PACKAGE_STRING = Protocol Buffers 3.21.5
PACKAGE_TARNAME = protobuf
PACKAGE_URL = 
PACKAGE_VERSION = 3.21.5
PATH_SEPARATOR = :
POW_LIB = 
PROTOBUF_OPT_FLAG = 
PROTOC = 
PTHREAD_CC = gcc.exe
PTHREAD_CFLAGS = -pthread
PTHREAD_LIBS = -lpthread
RANLIB = ranlib
SED = /usr/bin/sed
SET_MAKE = 
SHELL = /bin/sh
STRIP = strip
VERSION = 3.21.5
abs_builddir = /d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/build/conformance
abs_srcdir = /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/conformance
abs_top_builddir = /d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/build
abs_top_srcdir = /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf
ac_ct_AR = ar
ac_ct_CC = gcc.exe
ac_ct_CC_FOR_BUILD = gcc
ac_ct_CXX = 
ac_ct_CXX_FOR_BUILD = g++
ac_ct_DUMPBIN = 
ac_ct_OBJC = 
am__include = include
am__leading_dot = .
am__quote = 
am__tar = tar --format=ustar -chf - "$$tardir"
am__untar = tar -xf -
ax_pthread_config = 
bindir = ${exec_prefix}/bin
build = x86_64-pc-mingw64
build_alias = 
build_cpu = x86_64
build_os = mingw64
build_vendor = pc
builddir = .
datadir = ${datarootdir}
datarootdir = ${prefix}/share
docdir = ${datarootdir}/doc/${PACKAGE_TARNAME}
dvidir = ${docdir}
exec_prefix = ${prefix}
host = x86_64-pc-mingw64
host_alias = 
host_cpu = x86_64
host_os = mingw64
host_vendor = pc
htmldir = ${docdir}
includedir = ${prefix}/include
infodir = ${datarootdir}/info
install_sh = ${SHELL} /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/install-sh
libdir = ${exec_prefix}/lib
libexecdir = ${exec_prefix}/libexec
localedir = ${datarootdir}/locale
localstatedir = ${prefix}/var
mandir = ${datarootdir}/man
mkdir_p = $(MKDIR_P)
oldincludedir = /usr/include
pdfdir = ${docdir}
prefix = /d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install
program_transform_name = s,x,x,
psdir = ${docdir}
runstatedir = ${localstatedir}/run
sbindir = ${exec_prefix}/sbin
sharedstatedir = ${prefix}/com
srcdir = /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/conformance
subdirs =  third_party/googletest
sysconfdir = ${prefix}/etc
target = x86_64-pc-mingw64
target_alias = 
target_cpu = x86_64
target_os = mingw64
target_vendor = pc
top_build_prefix = ../
top_builddir = ..
top_srcdir = /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf
conformance_protoc_inputs = \
  conformance.proto                                            \
  $(top_srcdir)/src/google/protobuf/test_messages_proto3.proto


# proto2 input files, should be separated with proto3, as we
# can't generate proto2 files for php.
conformance_proto2_protoc_inputs = \
  $(top_srcdir)/src/google/protobuf/test_messages_proto2.proto

well_known_type_protoc_inputs = \
  $(top_srcdir)/src/google/protobuf/any.proto                  \
  $(top_srcdir)/src/google/protobuf/duration.proto             \
  $(top_srcdir)/src/google/protobuf/field_mask.proto           \
  $(top_srcdir)/src/google/protobuf/struct.proto               \
  $(top_srcdir)/src/google/protobuf/timestamp.proto            \
  $(top_srcdir)/src/google/protobuf/wrappers.proto

protoc_outputs = \
  conformance.pb.cc                                            \
  conformance.pb.h

other_language_protoc_outputs = \
  conformance_pb2.py                                           \
  Conformance.pbobjc.h                                         \
  Conformance.pbobjc.m                                         \
  conformance_pb.js                                            \
  conformance_pb.rb                                            \
  com/google/protobuf/Any.java                                 \
  com/google/protobuf/AnyOrBuilder.java                        \
  com/google/protobuf/AnyProto.java                            \
  com/google/protobuf/BoolValue.java                           \
  com/google/protobuf/BoolValueOrBuilder.java                  \
  com/google/protobuf/BytesValue.java                          \
  com/google/protobuf/BytesValueOrBuilder.java                 \
  com/google/protobuf/conformance/Conformance.java             \
  com/google/protobuf/DoubleValue.java                         \
  com/google/protobuf/DoubleValueOrBuilder.java                \
  com/google/protobuf/Duration.java                            \
  com/google/protobuf/DurationOrBuilder.java                   \
  com/google/protobuf/DurationProto.java                       \
  com/google/protobuf/FieldMask.java                           \
  com/google/protobuf/FieldMaskOrBuilder.java                  \
  com/google/protobuf/FieldMaskProto.java                      \
  com/google/protobuf/FloatValue.java                          \
  com/google/protobuf/FloatValueOrBuilder.java                 \
  com/google/protobuf/Int32Value.java                          \
  com/google/protobuf/Int32ValueOrBuilder.java                 \
  com/google/protobuf/Int64Value.java                          \
  com/google/protobuf/Int64ValueOrBuilder.java                 \
  com/google/protobuf/ListValue.java                           \
  com/google/protobuf/ListValueOrBuilder.java                  \
  com/google/protobuf/NullValue.java                           \
  com/google/protobuf/StringValue.java                         \
  com/google/protobuf/StringValueOrBuilder.java                \
  com/google/protobuf/Struct.java                              \
  com/google/protobuf/StructOrBuilder.java                     \
  com/google/protobuf/StructProto.java                         \
  com/google/protobuf/Timestamp.java                           \
  com/google/protobuf/TimestampOrBuilder.java                  \
  com/google/protobuf/TimestampProto.java                      \
  com/google/protobuf/UInt32Value.java                         \
  com/google/protobuf/UInt32ValueOrBuilder.java                \
  com/google/protobuf/UInt64Value.java                         \
  com/google/protobuf/UInt64ValueOrBuilder.java                \
  com/google/protobuf/Value.java                               \
  com/google/protobuf/ValueOrBuilder.java                      \
  com/google/protobuf/WrappersProto.java                       \
  com/google/protobuf_test_messages/proto3/TestMessagesProto3.java \
  com/google/protobuf_test_messages/proto2/TestMessagesProto2.java \
  google/protobuf/any.pb.cc                                    \
  google/protobuf/any.pb.h                                     \
  google/protobuf/any.rb                                       \
  google/protobuf/any_pb2.py                                   \
  google/protobuf/duration.pb.cc                               \
  google/protobuf/duration.pb.h                                \
  google/protobuf/duration.rb                                  \
  google/protobuf/duration_pb2.py                              \
  google/protobuf/field_mask.pb.cc                             \
  google/protobuf/field_mask.pb.h                              \
  google/protobuf/field_mask.rb                                \
  google/protobuf/field_mask_pb2.py                            \
  google/protobuf/struct.pb.cc                                 \
  google/protobuf/struct.pb.h                                  \
  google/protobuf/struct.rb                                    \
  google/protobuf/struct_pb2.py                                \
  google/protobuf/TestMessagesProto2.pbobjc.h                  \
  google/protobuf/TestMessagesProto2.pbobjc.m                  \
  google/protobuf/TestMessagesProto3.pbobjc.h                  \
  google/protobuf/TestMessagesProto3.pbobjc.m                  \
  google/protobuf/test_messages_proto3.pb.cc                   \
  google/protobuf/test_messages_proto3.pb.h                    \
  google/protobuf/test_messages_proto2.pb.cc                   \
  google/protobuf/test_messages_proto2.pb.h                    \
  google/protobuf/test_messages_proto3_pb.rb                   \
  google/protobuf/test_messages_proto3_pb2.py                  \
  google/protobuf/test_messages_proto2_pb2.py                  \
  google/protobuf/timestamp.pb.cc                              \
  google/protobuf/timestamp.pb.h                               \
  google/protobuf/timestamp.rb                                 \
  google/protobuf/timestamp_pb2.py                             \
  google/protobuf/wrappers.pb.cc                               \
  google/protobuf/wrappers.pb.h                                \
  google/protobuf/wrappers.rb                                  \
  google/protobuf/wrappers_pb2.py                              \
  Conformance/ConformanceRequest.php                           \
  Conformance/ConformanceResponse.php                          \
  Conformance/FailureSet.php                                   \
  Conformance/WireFormat.php                                   \
  GPBMetadata/Conformance.php                                  \
  GPBMetadata/Google/Protobuf/Any.php                          \
  GPBMetadata/Google/Protobuf/Duration.php                     \
  GPBMetadata/Google/Protobuf/FieldMask.php                    \
  GPBMetadata/Google/Protobuf/Struct.php                       \
  GPBMetadata/Google/Protobuf/TestMessagesProto3.php           \
  GPBMetadata/Google/Protobuf/Timestamp.php                    \
  GPBMetadata/Google/Protobuf/Wrappers.php                     \
  Google/Protobuf/Any.php                                      \
  Google/Protobuf/BoolValue.php                                \
  Google/Protobuf/BytesValue.php                               \
  Google/Protobuf/DoubleValue.php                              \
  Google/Protobuf/Duration.php                                 \
  Google/Protobuf/FieldMask.php                                \
  Google/Protobuf/FloatValue.php                               \
  Google/Protobuf/Int32Value.php                               \
  Google/Protobuf/Int64Value.php                               \
  Google/Protobuf/ListValue.php                                \
  Google/Protobuf/NullValue.php                                \
  Google/Protobuf/StringValue.php                              \
  Google/Protobuf/Struct.php                                   \
  Google/Protobuf/Timestamp.php                                \
  Google/Protobuf/UInt32Value.php                              \
  Google/Protobuf/UInt64Value.php                              \
  Google/Protobuf/Value.php                                    \
  Protobuf_test_messages/Proto3/ForeignEnum.php                \
  Protobuf_test_messages/Proto3/ForeignMessage.php             \
  Protobuf_test_messages/Proto3/TestAllTypes_NestedEnum.php    \
  Protobuf_test_messages/Proto3/TestAllTypes_NestedMessage.php \
  Protobuf_test_messages/Proto3/TestAllTypes.php


# All source files excepet C++/Objective-C ones should be explicitly listed
# here because the autoconf tools don't include files of other languages
# automatically.
EXTRA_DIST = \
  ConformanceJava.java        \
  ConformanceJavaLite.java    \
  README.md                   \
  conformance.proto           \
  conformance_python.py       \
  conformance_ruby.rb         \
  conformance_php.php         \
  failure_list_cpp.txt        \
  failure_list_csharp.txt     \
  failure_list_java.txt       \
  failure_list_js.txt         \
  failure_list_objc.txt       \
  failure_list_python.txt     \
  failure_list_python_cpp.txt \
  failure_list_python-post26.txt \
  failure_list_ruby.txt       \
  failure_list_php.txt        \
  failure_list_php_c.txt

conformance_test_runner_LDADD = $(top_srcdir)/src/libprotobuf.la
conformance_test_runner_SOURCES = conformance_test.h conformance_test.cc \
                                  conformance_test_main.cc               \
                                  binary_json_conformance_suite.h        \
                                  binary_json_conformance_suite.cc       \
                                  text_format_conformance_suite.h        \
                                  text_format_conformance_suite.cc       \
                                  conformance_test_runner.cc             \
                                  third_party/jsoncpp/json.h             \
                                  third_party/jsoncpp/jsoncpp.cpp

nodist_conformance_test_runner_SOURCES = conformance.pb.cc google/protobuf/test_messages_proto3.pb.cc google/protobuf/test_messages_proto2.pb.cc
conformance_test_runner_CPPFLAGS = -I$(top_srcdir)/src -I$(srcdir)
conformance_test_runner_CXXFLAGS = -std=c++11
conformance_cpp_LDADD = $(top_srcdir)/src/libprotobuf.la
conformance_cpp_SOURCES = conformance_cpp.cc
nodist_conformance_cpp_SOURCES = conformance.pb.cc google/protobuf/test_messages_proto3.pb.cc google/protobuf/test_messages_proto2.pb.cc
conformance_cpp_CPPFLAGS = -I$(top_srcdir)/src
#conformance_objc_SOURCES = conformance_objc.m ../objectivec/GPBProtocolBuffers.m
#nodist_conformance_objc_SOURCES = Conformance.pbobjc.m google/protobuf/TestMessagesProto2.pbobjc.m google/protobuf/TestMessagesProto3.pbobjc.m
# On travis, the build fails without the isysroot because whatever system
# headers are being found don't include generics support for
# NSArray/NSDictionary, the only guess is their image at one time had an odd
# setup for Xcode and old frameworks are being found.
#conformance_objc_CPPFLAGS = -I$(top_srcdir)/objectivec -isysroot `xcrun --sdk macosx --show-sdk-path`
#conformance_objc_LDFLAGS = -framework Foundation
CLEANFILES = $(protoc_outputs) protoc_middleman javac_middleman conformance-java javac_middleman_lite conformance-java-lite conformance-csharp conformance-php conformance-php-c $(other_language_protoc_outputs)
MAINTAINERCLEANFILES = \
  Makefile.in

all: all-am

.SUFFIXES:
.SUFFIXES: .cc .cpp .lo .m .o .obj
$(srcdir)/Makefile.in: # $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu conformance/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --gnu conformance/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure: # $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4): # $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):
install-binPROGRAMS: $(bin_PROGRAMS)
	@$(NORMAL_INSTALL)
	@list='$(bin_PROGRAMS)'; test -n "$(bindir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(bindir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(bindir)" || exit 1; \
	fi; \
	for p in $$list; do echo "$$p $$p"; done | \
	sed 's/$(EXEEXT)$$//' | \
	while read p p1; do if test -f $$p \
	 || test -f $$p1 \
	  ; then echo "$$p"; echo "$$p"; else :; fi; \
	done | \
	sed -e 'p;s,.*/,,;n;h' \
	    -e 's|.*|.|' \
	    -e 'p;x;s,.*/,,;s/$(EXEEXT)$$//;$(transform);s/$$/$(EXEEXT)/' | \
	sed 'N;N;N;s,\n, ,g' | \
	$(AWK) 'BEGIN { files["."] = ""; dirs["."] = 1 } \
	  { d=$$3; if (dirs[d] != 1) { print "d", d; dirs[d] = 1 } \
	    if ($$2 == $$4) files[d] = files[d] " " $$1; \
	    else { print "f", $$3 "/" $$4, $$1; } } \
	  END { for (d in files) print "f", d, files[d] }' | \
	while read type dir files; do \
	    if test "$$dir" = .; then dir=; else dir=/$$dir; fi; \
	    test -z "$$files" || { \
	    echo " $(INSTALL_PROGRAM_ENV) $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL_PROGRAM) $$files '$(DESTDIR)$(bindir)$$dir'"; \
	    $(INSTALL_PROGRAM_ENV) $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL_PROGRAM) $$files "$(DESTDIR)$(bindir)$$dir" || exit $$?; \
	    } \
	; done

uninstall-binPROGRAMS:
	@$(NORMAL_UNINSTALL)
	@list='$(bin_PROGRAMS)'; test -n "$(bindir)" || list=; \
	files=`for p in $$list; do echo "$$p"; done | \
	  sed -e 'h;s,^.*/,,;s/$(EXEEXT)$$//;$(transform)' \
	      -e 's/$$/$(EXEEXT)/' \
	`; \
	test -n "$$list" || exit 0; \
	echo " ( cd '$(DESTDIR)$(bindir)' && rm -f" $$files ")"; \
	cd "$(DESTDIR)$(bindir)" && rm -f $$files

clean-binPROGRAMS:
	@list='$(bin_PROGRAMS)'; test -n "$$list" || exit 0; \
	echo " rm -f" $$list; \
	rm -f $$list || exit $$?; \
	test -n "$(EXEEXT)" || exit 0; \
	list=`for p in $$list; do echo "$$p"; done | sed 's/$(EXEEXT)$$//'`; \
	echo " rm -f" $$list; \
	rm -f $$list
google/protobuf/$(am__dirstamp):
	@$(MKDIR_P) google/protobuf
	@: > google/protobuf/$(am__dirstamp)
google/protobuf/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) google/protobuf/$(DEPDIR)
	@: > google/protobuf/$(DEPDIR)/$(am__dirstamp)
google/protobuf/conformance_cpp-test_messages_proto3.pb.$(OBJEXT):  \
	google/protobuf/$(am__dirstamp) \
	google/protobuf/$(DEPDIR)/$(am__dirstamp)
google/protobuf/conformance_cpp-test_messages_proto2.pb.$(OBJEXT):  \
	google/protobuf/$(am__dirstamp) \
	google/protobuf/$(DEPDIR)/$(am__dirstamp)

conformance-cpp$(EXEEXT): $(conformance_cpp_OBJECTS) $(conformance_cpp_DEPENDENCIES) $(EXTRA_conformance_cpp_DEPENDENCIES) 
	@rm -f conformance-cpp$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(conformance_cpp_OBJECTS) $(conformance_cpp_LDADD) $(LIBS)
../objectivec/$(am__dirstamp):
	@$(MKDIR_P) ../objectivec
	@: > ../objectivec/$(am__dirstamp)
../objectivec/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) ../objectivec/$(DEPDIR)
	@: > ../objectivec/$(DEPDIR)/$(am__dirstamp)
../objectivec/conformance_objc-GPBProtocolBuffers.$(OBJEXT):  \
	../objectivec/$(am__dirstamp) \
	../objectivec/$(DEPDIR)/$(am__dirstamp)
google/protobuf/conformance_objc-TestMessagesProto2.pbobjc.$(OBJEXT):  \
	google/protobuf/$(am__dirstamp) \
	google/protobuf/$(DEPDIR)/$(am__dirstamp)
google/protobuf/conformance_objc-TestMessagesProto3.pbobjc.$(OBJEXT):  \
	google/protobuf/$(am__dirstamp) \
	google/protobuf/$(DEPDIR)/$(am__dirstamp)

conformance-objc$(EXEEXT): $(conformance_objc_OBJECTS) $(conformance_objc_DEPENDENCIES) $(EXTRA_conformance_objc_DEPENDENCIES) 
	@rm -f conformance-objc$(EXEEXT)
	$(AM_V_OBJCLD)$(conformance_objc_LINK) $(conformance_objc_OBJECTS) $(conformance_objc_LDADD) $(LIBS)
third_party/jsoncpp/$(am__dirstamp):
	@$(MKDIR_P) third_party/jsoncpp
	@: > third_party/jsoncpp/$(am__dirstamp)
third_party/jsoncpp/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) third_party/jsoncpp/$(DEPDIR)
	@: > third_party/jsoncpp/$(DEPDIR)/$(am__dirstamp)
third_party/jsoncpp/conformance_test_runner-jsoncpp.$(OBJEXT):  \
	third_party/jsoncpp/$(am__dirstamp) \
	third_party/jsoncpp/$(DEPDIR)/$(am__dirstamp)
google/protobuf/conformance_test_runner-test_messages_proto3.pb.$(OBJEXT):  \
	google/protobuf/$(am__dirstamp) \
	google/protobuf/$(DEPDIR)/$(am__dirstamp)
google/protobuf/conformance_test_runner-test_messages_proto2.pb.$(OBJEXT):  \
	google/protobuf/$(am__dirstamp) \
	google/protobuf/$(DEPDIR)/$(am__dirstamp)

conformance-test-runner$(EXEEXT): $(conformance_test_runner_OBJECTS) $(conformance_test_runner_DEPENDENCIES) $(EXTRA_conformance_test_runner_DEPENDENCIES) 
	@rm -f conformance-test-runner$(EXEEXT)
	$(AM_V_CXXLD)$(conformance_test_runner_LINK) $(conformance_test_runner_OBJECTS) $(conformance_test_runner_LDADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)
	-rm -f ../objectivec/*.$(OBJEXT)
	-rm -f google/protobuf/*.$(OBJEXT)
	-rm -f third_party/jsoncpp/*.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

include ../objectivec/$(DEPDIR)/conformance_objc-GPBProtocolBuffers.Po # am--include-marker
include ./$(DEPDIR)/conformance_cpp-conformance.pb.Po # am--include-marker
include ./$(DEPDIR)/conformance_cpp-conformance_cpp.Po # am--include-marker
include ./$(DEPDIR)/conformance_objc-Conformance.pbobjc.Po # am--include-marker
include ./$(DEPDIR)/conformance_objc-conformance_objc.Po # am--include-marker
include ./$(DEPDIR)/conformance_test_runner-binary_json_conformance_suite.Po # am--include-marker
include ./$(DEPDIR)/conformance_test_runner-conformance.pb.Po # am--include-marker
include ./$(DEPDIR)/conformance_test_runner-conformance_test.Po # am--include-marker
include ./$(DEPDIR)/conformance_test_runner-conformance_test_main.Po # am--include-marker
include ./$(DEPDIR)/conformance_test_runner-conformance_test_runner.Po # am--include-marker
include ./$(DEPDIR)/conformance_test_runner-text_format_conformance_suite.Po # am--include-marker
include google/protobuf/$(DEPDIR)/conformance_cpp-test_messages_proto2.pb.Po # am--include-marker
include google/protobuf/$(DEPDIR)/conformance_cpp-test_messages_proto3.pb.Po # am--include-marker
include google/protobuf/$(DEPDIR)/conformance_objc-TestMessagesProto2.pbobjc.Po # am--include-marker
include google/protobuf/$(DEPDIR)/conformance_objc-TestMessagesProto3.pbobjc.Po # am--include-marker
include google/protobuf/$(DEPDIR)/conformance_test_runner-test_messages_proto2.pb.Po # am--include-marker
include google/protobuf/$(DEPDIR)/conformance_test_runner-test_messages_proto3.pb.Po # am--include-marker
include third_party/jsoncpp/$(DEPDIR)/conformance_test_runner-jsoncpp.Po # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.cc.o:
	$(AM_V_CXX)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
	$(CXXCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
	$(am__mv) $$depbase.Tpo $$depbase.Po
#	$(AM_V_CXX)source='$<' object='$@' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXXCOMPILE) -c -o $@ $<

.cc.obj:
	$(AM_V_CXX)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
	$(CXXCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
	$(am__mv) $$depbase.Tpo $$depbase.Po
#	$(AM_V_CXX)source='$<' object='$@' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXXCOMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.cc.lo:
	$(AM_V_CXX)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
	$(LTCXXCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
	$(am__mv) $$depbase.Tpo $$depbase.Plo
#	$(AM_V_CXX)source='$<' object='$@' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LTCXXCOMPILE) -c -o $@ $<

conformance_cpp-conformance_cpp.o: conformance_cpp.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_cpp_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT conformance_cpp-conformance_cpp.o -MD -MP -MF $(DEPDIR)/conformance_cpp-conformance_cpp.Tpo -c -o conformance_cpp-conformance_cpp.o `test -f 'conformance_cpp.cc' || echo '$(srcdir)/'`conformance_cpp.cc
	$(AM_V_at)$(am__mv) $(DEPDIR)/conformance_cpp-conformance_cpp.Tpo $(DEPDIR)/conformance_cpp-conformance_cpp.Po
#	$(AM_V_CXX)source='conformance_cpp.cc' object='conformance_cpp-conformance_cpp.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_cpp_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o conformance_cpp-conformance_cpp.o `test -f 'conformance_cpp.cc' || echo '$(srcdir)/'`conformance_cpp.cc

conformance_cpp-conformance_cpp.obj: conformance_cpp.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_cpp_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT conformance_cpp-conformance_cpp.obj -MD -MP -MF $(DEPDIR)/conformance_cpp-conformance_cpp.Tpo -c -o conformance_cpp-conformance_cpp.obj `if test -f 'conformance_cpp.cc'; then $(CYGPATH_W) 'conformance_cpp.cc'; else $(CYGPATH_W) '$(srcdir)/conformance_cpp.cc'; fi`
	$(AM_V_at)$(am__mv) $(DEPDIR)/conformance_cpp-conformance_cpp.Tpo $(DEPDIR)/conformance_cpp-conformance_cpp.Po
#	$(AM_V_CXX)source='conformance_cpp.cc' object='conformance_cpp-conformance_cpp.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_cpp_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o conformance_cpp-conformance_cpp.obj `if test -f 'conformance_cpp.cc'; then $(CYGPATH_W) 'conformance_cpp.cc'; else $(CYGPATH_W) '$(srcdir)/conformance_cpp.cc'; fi`

conformance_cpp-conformance.pb.o: conformance.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_cpp_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT conformance_cpp-conformance.pb.o -MD -MP -MF $(DEPDIR)/conformance_cpp-conformance.pb.Tpo -c -o conformance_cpp-conformance.pb.o `test -f 'conformance.pb.cc' || echo '$(srcdir)/'`conformance.pb.cc
	$(AM_V_at)$(am__mv) $(DEPDIR)/conformance_cpp-conformance.pb.Tpo $(DEPDIR)/conformance_cpp-conformance.pb.Po
#	$(AM_V_CXX)source='conformance.pb.cc' object='conformance_cpp-conformance.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_cpp_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o conformance_cpp-conformance.pb.o `test -f 'conformance.pb.cc' || echo '$(srcdir)/'`conformance.pb.cc

conformance_cpp-conformance.pb.obj: conformance.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_cpp_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT conformance_cpp-conformance.pb.obj -MD -MP -MF $(DEPDIR)/conformance_cpp-conformance.pb.Tpo -c -o conformance_cpp-conformance.pb.obj `if test -f 'conformance.pb.cc'; then $(CYGPATH_W) 'conformance.pb.cc'; else $(CYGPATH_W) '$(srcdir)/conformance.pb.cc'; fi`
	$(AM_V_at)$(am__mv) $(DEPDIR)/conformance_cpp-conformance.pb.Tpo $(DEPDIR)/conformance_cpp-conformance.pb.Po
#	$(AM_V_CXX)source='conformance.pb.cc' object='conformance_cpp-conformance.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_cpp_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o conformance_cpp-conformance.pb.obj `if test -f 'conformance.pb.cc'; then $(CYGPATH_W) 'conformance.pb.cc'; else $(CYGPATH_W) '$(srcdir)/conformance.pb.cc'; fi`

google/protobuf/conformance_cpp-test_messages_proto3.pb.o: google/protobuf/test_messages_proto3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_cpp_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT google/protobuf/conformance_cpp-test_messages_proto3.pb.o -MD -MP -MF google/protobuf/$(DEPDIR)/conformance_cpp-test_messages_proto3.pb.Tpo -c -o google/protobuf/conformance_cpp-test_messages_proto3.pb.o `test -f 'google/protobuf/test_messages_proto3.pb.cc' || echo '$(srcdir)/'`google/protobuf/test_messages_proto3.pb.cc
	$(AM_V_at)$(am__mv) google/protobuf/$(DEPDIR)/conformance_cpp-test_messages_proto3.pb.Tpo google/protobuf/$(DEPDIR)/conformance_cpp-test_messages_proto3.pb.Po
#	$(AM_V_CXX)source='google/protobuf/test_messages_proto3.pb.cc' object='google/protobuf/conformance_cpp-test_messages_proto3.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_cpp_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o google/protobuf/conformance_cpp-test_messages_proto3.pb.o `test -f 'google/protobuf/test_messages_proto3.pb.cc' || echo '$(srcdir)/'`google/protobuf/test_messages_proto3.pb.cc

google/protobuf/conformance_cpp-test_messages_proto3.pb.obj: google/protobuf/test_messages_proto3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_cpp_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT google/protobuf/conformance_cpp-test_messages_proto3.pb.obj -MD -MP -MF google/protobuf/$(DEPDIR)/conformance_cpp-test_messages_proto3.pb.Tpo -c -o google/protobuf/conformance_cpp-test_messages_proto3.pb.obj `if test -f 'google/protobuf/test_messages_proto3.pb.cc'; then $(CYGPATH_W) 'google/protobuf/test_messages_proto3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/google/protobuf/test_messages_proto3.pb.cc'; fi`
	$(AM_V_at)$(am__mv) google/protobuf/$(DEPDIR)/conformance_cpp-test_messages_proto3.pb.Tpo google/protobuf/$(DEPDIR)/conformance_cpp-test_messages_proto3.pb.Po
#	$(AM_V_CXX)source='google/protobuf/test_messages_proto3.pb.cc' object='google/protobuf/conformance_cpp-test_messages_proto3.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_cpp_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o google/protobuf/conformance_cpp-test_messages_proto3.pb.obj `if test -f 'google/protobuf/test_messages_proto3.pb.cc'; then $(CYGPATH_W) 'google/protobuf/test_messages_proto3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/google/protobuf/test_messages_proto3.pb.cc'; fi`

google/protobuf/conformance_cpp-test_messages_proto2.pb.o: google/protobuf/test_messages_proto2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_cpp_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT google/protobuf/conformance_cpp-test_messages_proto2.pb.o -MD -MP -MF google/protobuf/$(DEPDIR)/conformance_cpp-test_messages_proto2.pb.Tpo -c -o google/protobuf/conformance_cpp-test_messages_proto2.pb.o `test -f 'google/protobuf/test_messages_proto2.pb.cc' || echo '$(srcdir)/'`google/protobuf/test_messages_proto2.pb.cc
	$(AM_V_at)$(am__mv) google/protobuf/$(DEPDIR)/conformance_cpp-test_messages_proto2.pb.Tpo google/protobuf/$(DEPDIR)/conformance_cpp-test_messages_proto2.pb.Po
#	$(AM_V_CXX)source='google/protobuf/test_messages_proto2.pb.cc' object='google/protobuf/conformance_cpp-test_messages_proto2.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_cpp_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o google/protobuf/conformance_cpp-test_messages_proto2.pb.o `test -f 'google/protobuf/test_messages_proto2.pb.cc' || echo '$(srcdir)/'`google/protobuf/test_messages_proto2.pb.cc

google/protobuf/conformance_cpp-test_messages_proto2.pb.obj: google/protobuf/test_messages_proto2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_cpp_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT google/protobuf/conformance_cpp-test_messages_proto2.pb.obj -MD -MP -MF google/protobuf/$(DEPDIR)/conformance_cpp-test_messages_proto2.pb.Tpo -c -o google/protobuf/conformance_cpp-test_messages_proto2.pb.obj `if test -f 'google/protobuf/test_messages_proto2.pb.cc'; then $(CYGPATH_W) 'google/protobuf/test_messages_proto2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/google/protobuf/test_messages_proto2.pb.cc'; fi`
	$(AM_V_at)$(am__mv) google/protobuf/$(DEPDIR)/conformance_cpp-test_messages_proto2.pb.Tpo google/protobuf/$(DEPDIR)/conformance_cpp-test_messages_proto2.pb.Po
#	$(AM_V_CXX)source='google/protobuf/test_messages_proto2.pb.cc' object='google/protobuf/conformance_cpp-test_messages_proto2.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_cpp_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o google/protobuf/conformance_cpp-test_messages_proto2.pb.obj `if test -f 'google/protobuf/test_messages_proto2.pb.cc'; then $(CYGPATH_W) 'google/protobuf/test_messages_proto2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/google/protobuf/test_messages_proto2.pb.cc'; fi`

conformance_test_runner-conformance_test.o: conformance_test.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -MT conformance_test_runner-conformance_test.o -MD -MP -MF $(DEPDIR)/conformance_test_runner-conformance_test.Tpo -c -o conformance_test_runner-conformance_test.o `test -f 'conformance_test.cc' || echo '$(srcdir)/'`conformance_test.cc
	$(AM_V_at)$(am__mv) $(DEPDIR)/conformance_test_runner-conformance_test.Tpo $(DEPDIR)/conformance_test_runner-conformance_test.Po
#	$(AM_V_CXX)source='conformance_test.cc' object='conformance_test_runner-conformance_test.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -c -o conformance_test_runner-conformance_test.o `test -f 'conformance_test.cc' || echo '$(srcdir)/'`conformance_test.cc

conformance_test_runner-conformance_test.obj: conformance_test.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -MT conformance_test_runner-conformance_test.obj -MD -MP -MF $(DEPDIR)/conformance_test_runner-conformance_test.Tpo -c -o conformance_test_runner-conformance_test.obj `if test -f 'conformance_test.cc'; then $(CYGPATH_W) 'conformance_test.cc'; else $(CYGPATH_W) '$(srcdir)/conformance_test.cc'; fi`
	$(AM_V_at)$(am__mv) $(DEPDIR)/conformance_test_runner-conformance_test.Tpo $(DEPDIR)/conformance_test_runner-conformance_test.Po
#	$(AM_V_CXX)source='conformance_test.cc' object='conformance_test_runner-conformance_test.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -c -o conformance_test_runner-conformance_test.obj `if test -f 'conformance_test.cc'; then $(CYGPATH_W) 'conformance_test.cc'; else $(CYGPATH_W) '$(srcdir)/conformance_test.cc'; fi`

conformance_test_runner-conformance_test_main.o: conformance_test_main.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -MT conformance_test_runner-conformance_test_main.o -MD -MP -MF $(DEPDIR)/conformance_test_runner-conformance_test_main.Tpo -c -o conformance_test_runner-conformance_test_main.o `test -f 'conformance_test_main.cc' || echo '$(srcdir)/'`conformance_test_main.cc
	$(AM_V_at)$(am__mv) $(DEPDIR)/conformance_test_runner-conformance_test_main.Tpo $(DEPDIR)/conformance_test_runner-conformance_test_main.Po
#	$(AM_V_CXX)source='conformance_test_main.cc' object='conformance_test_runner-conformance_test_main.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -c -o conformance_test_runner-conformance_test_main.o `test -f 'conformance_test_main.cc' || echo '$(srcdir)/'`conformance_test_main.cc

conformance_test_runner-conformance_test_main.obj: conformance_test_main.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -MT conformance_test_runner-conformance_test_main.obj -MD -MP -MF $(DEPDIR)/conformance_test_runner-conformance_test_main.Tpo -c -o conformance_test_runner-conformance_test_main.obj `if test -f 'conformance_test_main.cc'; then $(CYGPATH_W) 'conformance_test_main.cc'; else $(CYGPATH_W) '$(srcdir)/conformance_test_main.cc'; fi`
	$(AM_V_at)$(am__mv) $(DEPDIR)/conformance_test_runner-conformance_test_main.Tpo $(DEPDIR)/conformance_test_runner-conformance_test_main.Po
#	$(AM_V_CXX)source='conformance_test_main.cc' object='conformance_test_runner-conformance_test_main.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -c -o conformance_test_runner-conformance_test_main.obj `if test -f 'conformance_test_main.cc'; then $(CYGPATH_W) 'conformance_test_main.cc'; else $(CYGPATH_W) '$(srcdir)/conformance_test_main.cc'; fi`

conformance_test_runner-binary_json_conformance_suite.o: binary_json_conformance_suite.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -MT conformance_test_runner-binary_json_conformance_suite.o -MD -MP -MF $(DEPDIR)/conformance_test_runner-binary_json_conformance_suite.Tpo -c -o conformance_test_runner-binary_json_conformance_suite.o `test -f 'binary_json_conformance_suite.cc' || echo '$(srcdir)/'`binary_json_conformance_suite.cc
	$(AM_V_at)$(am__mv) $(DEPDIR)/conformance_test_runner-binary_json_conformance_suite.Tpo $(DEPDIR)/conformance_test_runner-binary_json_conformance_suite.Po
#	$(AM_V_CXX)source='binary_json_conformance_suite.cc' object='conformance_test_runner-binary_json_conformance_suite.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -c -o conformance_test_runner-binary_json_conformance_suite.o `test -f 'binary_json_conformance_suite.cc' || echo '$(srcdir)/'`binary_json_conformance_suite.cc

conformance_test_runner-binary_json_conformance_suite.obj: binary_json_conformance_suite.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -MT conformance_test_runner-binary_json_conformance_suite.obj -MD -MP -MF $(DEPDIR)/conformance_test_runner-binary_json_conformance_suite.Tpo -c -o conformance_test_runner-binary_json_conformance_suite.obj `if test -f 'binary_json_conformance_suite.cc'; then $(CYGPATH_W) 'binary_json_conformance_suite.cc'; else $(CYGPATH_W) '$(srcdir)/binary_json_conformance_suite.cc'; fi`
	$(AM_V_at)$(am__mv) $(DEPDIR)/conformance_test_runner-binary_json_conformance_suite.Tpo $(DEPDIR)/conformance_test_runner-binary_json_conformance_suite.Po
#	$(AM_V_CXX)source='binary_json_conformance_suite.cc' object='conformance_test_runner-binary_json_conformance_suite.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -c -o conformance_test_runner-binary_json_conformance_suite.obj `if test -f 'binary_json_conformance_suite.cc'; then $(CYGPATH_W) 'binary_json_conformance_suite.cc'; else $(CYGPATH_W) '$(srcdir)/binary_json_conformance_suite.cc'; fi`

conformance_test_runner-text_format_conformance_suite.o: text_format_conformance_suite.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -MT conformance_test_runner-text_format_conformance_suite.o -MD -MP -MF $(DEPDIR)/conformance_test_runner-text_format_conformance_suite.Tpo -c -o conformance_test_runner-text_format_conformance_suite.o `test -f 'text_format_conformance_suite.cc' || echo '$(srcdir)/'`text_format_conformance_suite.cc
	$(AM_V_at)$(am__mv) $(DEPDIR)/conformance_test_runner-text_format_conformance_suite.Tpo $(DEPDIR)/conformance_test_runner-text_format_conformance_suite.Po
#	$(AM_V_CXX)source='text_format_conformance_suite.cc' object='conformance_test_runner-text_format_conformance_suite.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -c -o conformance_test_runner-text_format_conformance_suite.o `test -f 'text_format_conformance_suite.cc' || echo '$(srcdir)/'`text_format_conformance_suite.cc

conformance_test_runner-text_format_conformance_suite.obj: text_format_conformance_suite.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -MT conformance_test_runner-text_format_conformance_suite.obj -MD -MP -MF $(DEPDIR)/conformance_test_runner-text_format_conformance_suite.Tpo -c -o conformance_test_runner-text_format_conformance_suite.obj `if test -f 'text_format_conformance_suite.cc'; then $(CYGPATH_W) 'text_format_conformance_suite.cc'; else $(CYGPATH_W) '$(srcdir)/text_format_conformance_suite.cc'; fi`
	$(AM_V_at)$(am__mv) $(DEPDIR)/conformance_test_runner-text_format_conformance_suite.Tpo $(DEPDIR)/conformance_test_runner-text_format_conformance_suite.Po
#	$(AM_V_CXX)source='text_format_conformance_suite.cc' object='conformance_test_runner-text_format_conformance_suite.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -c -o conformance_test_runner-text_format_conformance_suite.obj `if test -f 'text_format_conformance_suite.cc'; then $(CYGPATH_W) 'text_format_conformance_suite.cc'; else $(CYGPATH_W) '$(srcdir)/text_format_conformance_suite.cc'; fi`

conformance_test_runner-conformance_test_runner.o: conformance_test_runner.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -MT conformance_test_runner-conformance_test_runner.o -MD -MP -MF $(DEPDIR)/conformance_test_runner-conformance_test_runner.Tpo -c -o conformance_test_runner-conformance_test_runner.o `test -f 'conformance_test_runner.cc' || echo '$(srcdir)/'`conformance_test_runner.cc
	$(AM_V_at)$(am__mv) $(DEPDIR)/conformance_test_runner-conformance_test_runner.Tpo $(DEPDIR)/conformance_test_runner-conformance_test_runner.Po
#	$(AM_V_CXX)source='conformance_test_runner.cc' object='conformance_test_runner-conformance_test_runner.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -c -o conformance_test_runner-conformance_test_runner.o `test -f 'conformance_test_runner.cc' || echo '$(srcdir)/'`conformance_test_runner.cc

conformance_test_runner-conformance_test_runner.obj: conformance_test_runner.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -MT conformance_test_runner-conformance_test_runner.obj -MD -MP -MF $(DEPDIR)/conformance_test_runner-conformance_test_runner.Tpo -c -o conformance_test_runner-conformance_test_runner.obj `if test -f 'conformance_test_runner.cc'; then $(CYGPATH_W) 'conformance_test_runner.cc'; else $(CYGPATH_W) '$(srcdir)/conformance_test_runner.cc'; fi`
	$(AM_V_at)$(am__mv) $(DEPDIR)/conformance_test_runner-conformance_test_runner.Tpo $(DEPDIR)/conformance_test_runner-conformance_test_runner.Po
#	$(AM_V_CXX)source='conformance_test_runner.cc' object='conformance_test_runner-conformance_test_runner.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -c -o conformance_test_runner-conformance_test_runner.obj `if test -f 'conformance_test_runner.cc'; then $(CYGPATH_W) 'conformance_test_runner.cc'; else $(CYGPATH_W) '$(srcdir)/conformance_test_runner.cc'; fi`

third_party/jsoncpp/conformance_test_runner-jsoncpp.o: third_party/jsoncpp/jsoncpp.cpp
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -MT third_party/jsoncpp/conformance_test_runner-jsoncpp.o -MD -MP -MF third_party/jsoncpp/$(DEPDIR)/conformance_test_runner-jsoncpp.Tpo -c -o third_party/jsoncpp/conformance_test_runner-jsoncpp.o `test -f 'third_party/jsoncpp/jsoncpp.cpp' || echo '$(srcdir)/'`third_party/jsoncpp/jsoncpp.cpp
	$(AM_V_at)$(am__mv) third_party/jsoncpp/$(DEPDIR)/conformance_test_runner-jsoncpp.Tpo third_party/jsoncpp/$(DEPDIR)/conformance_test_runner-jsoncpp.Po
#	$(AM_V_CXX)source='third_party/jsoncpp/jsoncpp.cpp' object='third_party/jsoncpp/conformance_test_runner-jsoncpp.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -c -o third_party/jsoncpp/conformance_test_runner-jsoncpp.o `test -f 'third_party/jsoncpp/jsoncpp.cpp' || echo '$(srcdir)/'`third_party/jsoncpp/jsoncpp.cpp

third_party/jsoncpp/conformance_test_runner-jsoncpp.obj: third_party/jsoncpp/jsoncpp.cpp
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -MT third_party/jsoncpp/conformance_test_runner-jsoncpp.obj -MD -MP -MF third_party/jsoncpp/$(DEPDIR)/conformance_test_runner-jsoncpp.Tpo -c -o third_party/jsoncpp/conformance_test_runner-jsoncpp.obj `if test -f 'third_party/jsoncpp/jsoncpp.cpp'; then $(CYGPATH_W) 'third_party/jsoncpp/jsoncpp.cpp'; else $(CYGPATH_W) '$(srcdir)/third_party/jsoncpp/jsoncpp.cpp'; fi`
	$(AM_V_at)$(am__mv) third_party/jsoncpp/$(DEPDIR)/conformance_test_runner-jsoncpp.Tpo third_party/jsoncpp/$(DEPDIR)/conformance_test_runner-jsoncpp.Po
#	$(AM_V_CXX)source='third_party/jsoncpp/jsoncpp.cpp' object='third_party/jsoncpp/conformance_test_runner-jsoncpp.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -c -o third_party/jsoncpp/conformance_test_runner-jsoncpp.obj `if test -f 'third_party/jsoncpp/jsoncpp.cpp'; then $(CYGPATH_W) 'third_party/jsoncpp/jsoncpp.cpp'; else $(CYGPATH_W) '$(srcdir)/third_party/jsoncpp/jsoncpp.cpp'; fi`

conformance_test_runner-conformance.pb.o: conformance.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -MT conformance_test_runner-conformance.pb.o -MD -MP -MF $(DEPDIR)/conformance_test_runner-conformance.pb.Tpo -c -o conformance_test_runner-conformance.pb.o `test -f 'conformance.pb.cc' || echo '$(srcdir)/'`conformance.pb.cc
	$(AM_V_at)$(am__mv) $(DEPDIR)/conformance_test_runner-conformance.pb.Tpo $(DEPDIR)/conformance_test_runner-conformance.pb.Po
#	$(AM_V_CXX)source='conformance.pb.cc' object='conformance_test_runner-conformance.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -c -o conformance_test_runner-conformance.pb.o `test -f 'conformance.pb.cc' || echo '$(srcdir)/'`conformance.pb.cc

conformance_test_runner-conformance.pb.obj: conformance.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -MT conformance_test_runner-conformance.pb.obj -MD -MP -MF $(DEPDIR)/conformance_test_runner-conformance.pb.Tpo -c -o conformance_test_runner-conformance.pb.obj `if test -f 'conformance.pb.cc'; then $(CYGPATH_W) 'conformance.pb.cc'; else $(CYGPATH_W) '$(srcdir)/conformance.pb.cc'; fi`
	$(AM_V_at)$(am__mv) $(DEPDIR)/conformance_test_runner-conformance.pb.Tpo $(DEPDIR)/conformance_test_runner-conformance.pb.Po
#	$(AM_V_CXX)source='conformance.pb.cc' object='conformance_test_runner-conformance.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -c -o conformance_test_runner-conformance.pb.obj `if test -f 'conformance.pb.cc'; then $(CYGPATH_W) 'conformance.pb.cc'; else $(CYGPATH_W) '$(srcdir)/conformance.pb.cc'; fi`

google/protobuf/conformance_test_runner-test_messages_proto3.pb.o: google/protobuf/test_messages_proto3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -MT google/protobuf/conformance_test_runner-test_messages_proto3.pb.o -MD -MP -MF google/protobuf/$(DEPDIR)/conformance_test_runner-test_messages_proto3.pb.Tpo -c -o google/protobuf/conformance_test_runner-test_messages_proto3.pb.o `test -f 'google/protobuf/test_messages_proto3.pb.cc' || echo '$(srcdir)/'`google/protobuf/test_messages_proto3.pb.cc
	$(AM_V_at)$(am__mv) google/protobuf/$(DEPDIR)/conformance_test_runner-test_messages_proto3.pb.Tpo google/protobuf/$(DEPDIR)/conformance_test_runner-test_messages_proto3.pb.Po
#	$(AM_V_CXX)source='google/protobuf/test_messages_proto3.pb.cc' object='google/protobuf/conformance_test_runner-test_messages_proto3.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -c -o google/protobuf/conformance_test_runner-test_messages_proto3.pb.o `test -f 'google/protobuf/test_messages_proto3.pb.cc' || echo '$(srcdir)/'`google/protobuf/test_messages_proto3.pb.cc

google/protobuf/conformance_test_runner-test_messages_proto3.pb.obj: google/protobuf/test_messages_proto3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -MT google/protobuf/conformance_test_runner-test_messages_proto3.pb.obj -MD -MP -MF google/protobuf/$(DEPDIR)/conformance_test_runner-test_messages_proto3.pb.Tpo -c -o google/protobuf/conformance_test_runner-test_messages_proto3.pb.obj `if test -f 'google/protobuf/test_messages_proto3.pb.cc'; then $(CYGPATH_W) 'google/protobuf/test_messages_proto3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/google/protobuf/test_messages_proto3.pb.cc'; fi`
	$(AM_V_at)$(am__mv) google/protobuf/$(DEPDIR)/conformance_test_runner-test_messages_proto3.pb.Tpo google/protobuf/$(DEPDIR)/conformance_test_runner-test_messages_proto3.pb.Po
#	$(AM_V_CXX)source='google/protobuf/test_messages_proto3.pb.cc' object='google/protobuf/conformance_test_runner-test_messages_proto3.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -c -o google/protobuf/conformance_test_runner-test_messages_proto3.pb.obj `if test -f 'google/protobuf/test_messages_proto3.pb.cc'; then $(CYGPATH_W) 'google/protobuf/test_messages_proto3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/google/protobuf/test_messages_proto3.pb.cc'; fi`

google/protobuf/conformance_test_runner-test_messages_proto2.pb.o: google/protobuf/test_messages_proto2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -MT google/protobuf/conformance_test_runner-test_messages_proto2.pb.o -MD -MP -MF google/protobuf/$(DEPDIR)/conformance_test_runner-test_messages_proto2.pb.Tpo -c -o google/protobuf/conformance_test_runner-test_messages_proto2.pb.o `test -f 'google/protobuf/test_messages_proto2.pb.cc' || echo '$(srcdir)/'`google/protobuf/test_messages_proto2.pb.cc
	$(AM_V_at)$(am__mv) google/protobuf/$(DEPDIR)/conformance_test_runner-test_messages_proto2.pb.Tpo google/protobuf/$(DEPDIR)/conformance_test_runner-test_messages_proto2.pb.Po
#	$(AM_V_CXX)source='google/protobuf/test_messages_proto2.pb.cc' object='google/protobuf/conformance_test_runner-test_messages_proto2.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -c -o google/protobuf/conformance_test_runner-test_messages_proto2.pb.o `test -f 'google/protobuf/test_messages_proto2.pb.cc' || echo '$(srcdir)/'`google/protobuf/test_messages_proto2.pb.cc

google/protobuf/conformance_test_runner-test_messages_proto2.pb.obj: google/protobuf/test_messages_proto2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -MT google/protobuf/conformance_test_runner-test_messages_proto2.pb.obj -MD -MP -MF google/protobuf/$(DEPDIR)/conformance_test_runner-test_messages_proto2.pb.Tpo -c -o google/protobuf/conformance_test_runner-test_messages_proto2.pb.obj `if test -f 'google/protobuf/test_messages_proto2.pb.cc'; then $(CYGPATH_W) 'google/protobuf/test_messages_proto2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/google/protobuf/test_messages_proto2.pb.cc'; fi`
	$(AM_V_at)$(am__mv) google/protobuf/$(DEPDIR)/conformance_test_runner-test_messages_proto2.pb.Tpo google/protobuf/$(DEPDIR)/conformance_test_runner-test_messages_proto2.pb.Po
#	$(AM_V_CXX)source='google/protobuf/test_messages_proto2.pb.cc' object='google/protobuf/conformance_test_runner-test_messages_proto2.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_test_runner_CPPFLAGS) $(CPPFLAGS) $(conformance_test_runner_CXXFLAGS) $(CXXFLAGS) -c -o google/protobuf/conformance_test_runner-test_messages_proto2.pb.obj `if test -f 'google/protobuf/test_messages_proto2.pb.cc'; then $(CYGPATH_W) 'google/protobuf/test_messages_proto2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/google/protobuf/test_messages_proto2.pb.cc'; fi`

.cpp.o:
	$(AM_V_CXX)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
	$(CXXCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
	$(am__mv) $$depbase.Tpo $$depbase.Po
#	$(AM_V_CXX)source='$<' object='$@' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXXCOMPILE) -c -o $@ $<

.cpp.obj:
	$(AM_V_CXX)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
	$(CXXCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
	$(am__mv) $$depbase.Tpo $$depbase.Po
#	$(AM_V_CXX)source='$<' object='$@' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXXCOMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.cpp.lo:
	$(AM_V_CXX)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
	$(LTCXXCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
	$(am__mv) $$depbase.Tpo $$depbase.Plo
#	$(AM_V_CXX)source='$<' object='$@' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LTCXXCOMPILE) -c -o $@ $<

.m.o:
#	$(AM_V_OBJC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
#	$(OBJCCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
#	$(am__mv) $$depbase.Tpo $$depbase.Po
	$(AM_V_OBJC)source='$<' object='$@' libtool=no \
	DEPDIR=$(DEPDIR) $(OBJCDEPMODE) $(depcomp) \
	$(AM_V_OBJC_no)$(OBJCCOMPILE) -c -o $@ $<

.m.obj:
#	$(AM_V_OBJC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
#	$(OBJCCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
#	$(am__mv) $$depbase.Tpo $$depbase.Po
	$(AM_V_OBJC)source='$<' object='$@' libtool=no \
	DEPDIR=$(DEPDIR) $(OBJCDEPMODE) $(depcomp) \
	$(AM_V_OBJC_no)$(OBJCCOMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.m.lo:
#	$(AM_V_OBJC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
#	$(LTOBJCCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
#	$(am__mv) $$depbase.Tpo $$depbase.Plo
	$(AM_V_OBJC)source='$<' object='$@' libtool=yes \
	DEPDIR=$(DEPDIR) $(OBJCDEPMODE) $(depcomp) \
	$(AM_V_OBJC_no)$(LTOBJCCOMPILE) -c -o $@ $<

conformance_objc-conformance_objc.o: conformance_objc.m
#	$(AM_V_OBJC)$(OBJC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_objc_CPPFLAGS) $(CPPFLAGS) $(AM_OBJCFLAGS) $(OBJCFLAGS) -MT conformance_objc-conformance_objc.o -MD -MP -MF $(DEPDIR)/conformance_objc-conformance_objc.Tpo -c -o conformance_objc-conformance_objc.o `test -f 'conformance_objc.m' || echo '$(srcdir)/'`conformance_objc.m
#	$(AM_V_at)$(am__mv) $(DEPDIR)/conformance_objc-conformance_objc.Tpo $(DEPDIR)/conformance_objc-conformance_objc.Po
	$(AM_V_OBJC)source='conformance_objc.m' object='conformance_objc-conformance_objc.o' libtool=no \
	DEPDIR=$(DEPDIR) $(OBJCDEPMODE) $(depcomp) \
	$(AM_V_OBJC_no)$(OBJC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_objc_CPPFLAGS) $(CPPFLAGS) $(AM_OBJCFLAGS) $(OBJCFLAGS) -c -o conformance_objc-conformance_objc.o `test -f 'conformance_objc.m' || echo '$(srcdir)/'`conformance_objc.m

conformance_objc-conformance_objc.obj: conformance_objc.m
#	$(AM_V_OBJC)$(OBJC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_objc_CPPFLAGS) $(CPPFLAGS) $(AM_OBJCFLAGS) $(OBJCFLAGS) -MT conformance_objc-conformance_objc.obj -MD -MP -MF $(DEPDIR)/conformance_objc-conformance_objc.Tpo -c -o conformance_objc-conformance_objc.obj `if test -f 'conformance_objc.m'; then $(CYGPATH_W) 'conformance_objc.m'; else $(CYGPATH_W) '$(srcdir)/conformance_objc.m'; fi`
#	$(AM_V_at)$(am__mv) $(DEPDIR)/conformance_objc-conformance_objc.Tpo $(DEPDIR)/conformance_objc-conformance_objc.Po
	$(AM_V_OBJC)source='conformance_objc.m' object='conformance_objc-conformance_objc.obj' libtool=no \
	DEPDIR=$(DEPDIR) $(OBJCDEPMODE) $(depcomp) \
	$(AM_V_OBJC_no)$(OBJC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_objc_CPPFLAGS) $(CPPFLAGS) $(AM_OBJCFLAGS) $(OBJCFLAGS) -c -o conformance_objc-conformance_objc.obj `if test -f 'conformance_objc.m'; then $(CYGPATH_W) 'conformance_objc.m'; else $(CYGPATH_W) '$(srcdir)/conformance_objc.m'; fi`

../objectivec/conformance_objc-GPBProtocolBuffers.o: ../objectivec/GPBProtocolBuffers.m
#	$(AM_V_OBJC)$(OBJC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_objc_CPPFLAGS) $(CPPFLAGS) $(AM_OBJCFLAGS) $(OBJCFLAGS) -MT ../objectivec/conformance_objc-GPBProtocolBuffers.o -MD -MP -MF ../objectivec/$(DEPDIR)/conformance_objc-GPBProtocolBuffers.Tpo -c -o ../objectivec/conformance_objc-GPBProtocolBuffers.o `test -f '../objectivec/GPBProtocolBuffers.m' || echo '$(srcdir)/'`../objectivec/GPBProtocolBuffers.m
#	$(AM_V_at)$(am__mv) ../objectivec/$(DEPDIR)/conformance_objc-GPBProtocolBuffers.Tpo ../objectivec/$(DEPDIR)/conformance_objc-GPBProtocolBuffers.Po
	$(AM_V_OBJC)source='../objectivec/GPBProtocolBuffers.m' object='../objectivec/conformance_objc-GPBProtocolBuffers.o' libtool=no \
	DEPDIR=$(DEPDIR) $(OBJCDEPMODE) $(depcomp) \
	$(AM_V_OBJC_no)$(OBJC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_objc_CPPFLAGS) $(CPPFLAGS) $(AM_OBJCFLAGS) $(OBJCFLAGS) -c -o ../objectivec/conformance_objc-GPBProtocolBuffers.o `test -f '../objectivec/GPBProtocolBuffers.m' || echo '$(srcdir)/'`../objectivec/GPBProtocolBuffers.m

../objectivec/conformance_objc-GPBProtocolBuffers.obj: ../objectivec/GPBProtocolBuffers.m
#	$(AM_V_OBJC)$(OBJC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_objc_CPPFLAGS) $(CPPFLAGS) $(AM_OBJCFLAGS) $(OBJCFLAGS) -MT ../objectivec/conformance_objc-GPBProtocolBuffers.obj -MD -MP -MF ../objectivec/$(DEPDIR)/conformance_objc-GPBProtocolBuffers.Tpo -c -o ../objectivec/conformance_objc-GPBProtocolBuffers.obj `if test -f '../objectivec/GPBProtocolBuffers.m'; then $(CYGPATH_W) '../objectivec/GPBProtocolBuffers.m'; else $(CYGPATH_W) '$(srcdir)/../objectivec/GPBProtocolBuffers.m'; fi`
#	$(AM_V_at)$(am__mv) ../objectivec/$(DEPDIR)/conformance_objc-GPBProtocolBuffers.Tpo ../objectivec/$(DEPDIR)/conformance_objc-GPBProtocolBuffers.Po
	$(AM_V_OBJC)source='../objectivec/GPBProtocolBuffers.m' object='../objectivec/conformance_objc-GPBProtocolBuffers.obj' libtool=no \
	DEPDIR=$(DEPDIR) $(OBJCDEPMODE) $(depcomp) \
	$(AM_V_OBJC_no)$(OBJC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_objc_CPPFLAGS) $(CPPFLAGS) $(AM_OBJCFLAGS) $(OBJCFLAGS) -c -o ../objectivec/conformance_objc-GPBProtocolBuffers.obj `if test -f '../objectivec/GPBProtocolBuffers.m'; then $(CYGPATH_W) '../objectivec/GPBProtocolBuffers.m'; else $(CYGPATH_W) '$(srcdir)/../objectivec/GPBProtocolBuffers.m'; fi`

conformance_objc-Conformance.pbobjc.o: Conformance.pbobjc.m
#	$(AM_V_OBJC)$(OBJC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_objc_CPPFLAGS) $(CPPFLAGS) $(AM_OBJCFLAGS) $(OBJCFLAGS) -MT conformance_objc-Conformance.pbobjc.o -MD -MP -MF $(DEPDIR)/conformance_objc-Conformance.pbobjc.Tpo -c -o conformance_objc-Conformance.pbobjc.o `test -f 'Conformance.pbobjc.m' || echo '$(srcdir)/'`Conformance.pbobjc.m
#	$(AM_V_at)$(am__mv) $(DEPDIR)/conformance_objc-Conformance.pbobjc.Tpo $(DEPDIR)/conformance_objc-Conformance.pbobjc.Po
	$(AM_V_OBJC)source='Conformance.pbobjc.m' object='conformance_objc-Conformance.pbobjc.o' libtool=no \
	DEPDIR=$(DEPDIR) $(OBJCDEPMODE) $(depcomp) \
	$(AM_V_OBJC_no)$(OBJC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_objc_CPPFLAGS) $(CPPFLAGS) $(AM_OBJCFLAGS) $(OBJCFLAGS) -c -o conformance_objc-Conformance.pbobjc.o `test -f 'Conformance.pbobjc.m' || echo '$(srcdir)/'`Conformance.pbobjc.m

conformance_objc-Conformance.pbobjc.obj: Conformance.pbobjc.m
#	$(AM_V_OBJC)$(OBJC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_objc_CPPFLAGS) $(CPPFLAGS) $(AM_OBJCFLAGS) $(OBJCFLAGS) -MT conformance_objc-Conformance.pbobjc.obj -MD -MP -MF $(DEPDIR)/conformance_objc-Conformance.pbobjc.Tpo -c -o conformance_objc-Conformance.pbobjc.obj `if test -f 'Conformance.pbobjc.m'; then $(CYGPATH_W) 'Conformance.pbobjc.m'; else $(CYGPATH_W) '$(srcdir)/Conformance.pbobjc.m'; fi`
#	$(AM_V_at)$(am__mv) $(DEPDIR)/conformance_objc-Conformance.pbobjc.Tpo $(DEPDIR)/conformance_objc-Conformance.pbobjc.Po
	$(AM_V_OBJC)source='Conformance.pbobjc.m' object='conformance_objc-Conformance.pbobjc.obj' libtool=no \
	DEPDIR=$(DEPDIR) $(OBJCDEPMODE) $(depcomp) \
	$(AM_V_OBJC_no)$(OBJC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_objc_CPPFLAGS) $(CPPFLAGS) $(AM_OBJCFLAGS) $(OBJCFLAGS) -c -o conformance_objc-Conformance.pbobjc.obj `if test -f 'Conformance.pbobjc.m'; then $(CYGPATH_W) 'Conformance.pbobjc.m'; else $(CYGPATH_W) '$(srcdir)/Conformance.pbobjc.m'; fi`

google/protobuf/conformance_objc-TestMessagesProto2.pbobjc.o: google/protobuf/TestMessagesProto2.pbobjc.m
#	$(AM_V_OBJC)$(OBJC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_objc_CPPFLAGS) $(CPPFLAGS) $(AM_OBJCFLAGS) $(OBJCFLAGS) -MT google/protobuf/conformance_objc-TestMessagesProto2.pbobjc.o -MD -MP -MF google/protobuf/$(DEPDIR)/conformance_objc-TestMessagesProto2.pbobjc.Tpo -c -o google/protobuf/conformance_objc-TestMessagesProto2.pbobjc.o `test -f 'google/protobuf/TestMessagesProto2.pbobjc.m' || echo '$(srcdir)/'`google/protobuf/TestMessagesProto2.pbobjc.m
#	$(AM_V_at)$(am__mv) google/protobuf/$(DEPDIR)/conformance_objc-TestMessagesProto2.pbobjc.Tpo google/protobuf/$(DEPDIR)/conformance_objc-TestMessagesProto2.pbobjc.Po
	$(AM_V_OBJC)source='google/protobuf/TestMessagesProto2.pbobjc.m' object='google/protobuf/conformance_objc-TestMessagesProto2.pbobjc.o' libtool=no \
	DEPDIR=$(DEPDIR) $(OBJCDEPMODE) $(depcomp) \
	$(AM_V_OBJC_no)$(OBJC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_objc_CPPFLAGS) $(CPPFLAGS) $(AM_OBJCFLAGS) $(OBJCFLAGS) -c -o google/protobuf/conformance_objc-TestMessagesProto2.pbobjc.o `test -f 'google/protobuf/TestMessagesProto2.pbobjc.m' || echo '$(srcdir)/'`google/protobuf/TestMessagesProto2.pbobjc.m

google/protobuf/conformance_objc-TestMessagesProto2.pbobjc.obj: google/protobuf/TestMessagesProto2.pbobjc.m
#	$(AM_V_OBJC)$(OBJC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_objc_CPPFLAGS) $(CPPFLAGS) $(AM_OBJCFLAGS) $(OBJCFLAGS) -MT google/protobuf/conformance_objc-TestMessagesProto2.pbobjc.obj -MD -MP -MF google/protobuf/$(DEPDIR)/conformance_objc-TestMessagesProto2.pbobjc.Tpo -c -o google/protobuf/conformance_objc-TestMessagesProto2.pbobjc.obj `if test -f 'google/protobuf/TestMessagesProto2.pbobjc.m'; then $(CYGPATH_W) 'google/protobuf/TestMessagesProto2.pbobjc.m'; else $(CYGPATH_W) '$(srcdir)/google/protobuf/TestMessagesProto2.pbobjc.m'; fi`
#	$(AM_V_at)$(am__mv) google/protobuf/$(DEPDIR)/conformance_objc-TestMessagesProto2.pbobjc.Tpo google/protobuf/$(DEPDIR)/conformance_objc-TestMessagesProto2.pbobjc.Po
	$(AM_V_OBJC)source='google/protobuf/TestMessagesProto2.pbobjc.m' object='google/protobuf/conformance_objc-TestMessagesProto2.pbobjc.obj' libtool=no \
	DEPDIR=$(DEPDIR) $(OBJCDEPMODE) $(depcomp) \
	$(AM_V_OBJC_no)$(OBJC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_objc_CPPFLAGS) $(CPPFLAGS) $(AM_OBJCFLAGS) $(OBJCFLAGS) -c -o google/protobuf/conformance_objc-TestMessagesProto2.pbobjc.obj `if test -f 'google/protobuf/TestMessagesProto2.pbobjc.m'; then $(CYGPATH_W) 'google/protobuf/TestMessagesProto2.pbobjc.m'; else $(CYGPATH_W) '$(srcdir)/google/protobuf/TestMessagesProto2.pbobjc.m'; fi`

google/protobuf/conformance_objc-TestMessagesProto3.pbobjc.o: google/protobuf/TestMessagesProto3.pbobjc.m
#	$(AM_V_OBJC)$(OBJC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_objc_CPPFLAGS) $(CPPFLAGS) $(AM_OBJCFLAGS) $(OBJCFLAGS) -MT google/protobuf/conformance_objc-TestMessagesProto3.pbobjc.o -MD -MP -MF google/protobuf/$(DEPDIR)/conformance_objc-TestMessagesProto3.pbobjc.Tpo -c -o google/protobuf/conformance_objc-TestMessagesProto3.pbobjc.o `test -f 'google/protobuf/TestMessagesProto3.pbobjc.m' || echo '$(srcdir)/'`google/protobuf/TestMessagesProto3.pbobjc.m
#	$(AM_V_at)$(am__mv) google/protobuf/$(DEPDIR)/conformance_objc-TestMessagesProto3.pbobjc.Tpo google/protobuf/$(DEPDIR)/conformance_objc-TestMessagesProto3.pbobjc.Po
	$(AM_V_OBJC)source='google/protobuf/TestMessagesProto3.pbobjc.m' object='google/protobuf/conformance_objc-TestMessagesProto3.pbobjc.o' libtool=no \
	DEPDIR=$(DEPDIR) $(OBJCDEPMODE) $(depcomp) \
	$(AM_V_OBJC_no)$(OBJC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_objc_CPPFLAGS) $(CPPFLAGS) $(AM_OBJCFLAGS) $(OBJCFLAGS) -c -o google/protobuf/conformance_objc-TestMessagesProto3.pbobjc.o `test -f 'google/protobuf/TestMessagesProto3.pbobjc.m' || echo '$(srcdir)/'`google/protobuf/TestMessagesProto3.pbobjc.m

google/protobuf/conformance_objc-TestMessagesProto3.pbobjc.obj: google/protobuf/TestMessagesProto3.pbobjc.m
#	$(AM_V_OBJC)$(OBJC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_objc_CPPFLAGS) $(CPPFLAGS) $(AM_OBJCFLAGS) $(OBJCFLAGS) -MT google/protobuf/conformance_objc-TestMessagesProto3.pbobjc.obj -MD -MP -MF google/protobuf/$(DEPDIR)/conformance_objc-TestMessagesProto3.pbobjc.Tpo -c -o google/protobuf/conformance_objc-TestMessagesProto3.pbobjc.obj `if test -f 'google/protobuf/TestMessagesProto3.pbobjc.m'; then $(CYGPATH_W) 'google/protobuf/TestMessagesProto3.pbobjc.m'; else $(CYGPATH_W) '$(srcdir)/google/protobuf/TestMessagesProto3.pbobjc.m'; fi`
#	$(AM_V_at)$(am__mv) google/protobuf/$(DEPDIR)/conformance_objc-TestMessagesProto3.pbobjc.Tpo google/protobuf/$(DEPDIR)/conformance_objc-TestMessagesProto3.pbobjc.Po
	$(AM_V_OBJC)source='google/protobuf/TestMessagesProto3.pbobjc.m' object='google/protobuf/conformance_objc-TestMessagesProto3.pbobjc.obj' libtool=no \
	DEPDIR=$(DEPDIR) $(OBJCDEPMODE) $(depcomp) \
	$(AM_V_OBJC_no)$(OBJC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(conformance_objc_CPPFLAGS) $(CPPFLAGS) $(AM_OBJCFLAGS) $(OBJCFLAGS) -c -o google/protobuf/conformance_objc-TestMessagesProto3.pbobjc.obj `if test -f 'google/protobuf/TestMessagesProto3.pbobjc.m'; then $(CYGPATH_W) 'google/protobuf/TestMessagesProto3.pbobjc.m'; else $(CYGPATH_W) '$(srcdir)/google/protobuf/TestMessagesProto3.pbobjc.m'; fi`

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-am
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-am

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscopelist: cscopelist-am

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags
distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-am
all-am: Makefile $(PROGRAMS)
installdirs:
	for dir in "$(DESTDIR)$(bindir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:
	-test -z "$(CLEANFILES)" || rm -f $(CLEANFILES)

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)
	-rm -f ../objectivec/$(DEPDIR)/$(am__dirstamp)
	-rm -f ../objectivec/$(am__dirstamp)
	-rm -f google/protobuf/$(DEPDIR)/$(am__dirstamp)
	-rm -f google/protobuf/$(am__dirstamp)
	-rm -f third_party/jsoncpp/$(DEPDIR)/$(am__dirstamp)
	-rm -f third_party/jsoncpp/$(am__dirstamp)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
	-test -z "$(MAINTAINERCLEANFILES)" || rm -f $(MAINTAINERCLEANFILES)
clean: clean-am

clean-am: clean-binPROGRAMS clean-generic clean-libtool mostlyclean-am

distclean: distclean-am
		-rm -f ../objectivec/$(DEPDIR)/conformance_objc-GPBProtocolBuffers.Po
	-rm -f ./$(DEPDIR)/conformance_cpp-conformance.pb.Po
	-rm -f ./$(DEPDIR)/conformance_cpp-conformance_cpp.Po
	-rm -f ./$(DEPDIR)/conformance_objc-Conformance.pbobjc.Po
	-rm -f ./$(DEPDIR)/conformance_objc-conformance_objc.Po
	-rm -f ./$(DEPDIR)/conformance_test_runner-binary_json_conformance_suite.Po
	-rm -f ./$(DEPDIR)/conformance_test_runner-conformance.pb.Po
	-rm -f ./$(DEPDIR)/conformance_test_runner-conformance_test.Po
	-rm -f ./$(DEPDIR)/conformance_test_runner-conformance_test_main.Po
	-rm -f ./$(DEPDIR)/conformance_test_runner-conformance_test_runner.Po
	-rm -f ./$(DEPDIR)/conformance_test_runner-text_format_conformance_suite.Po
	-rm -f google/protobuf/$(DEPDIR)/conformance_cpp-test_messages_proto2.pb.Po
	-rm -f google/protobuf/$(DEPDIR)/conformance_cpp-test_messages_proto3.pb.Po
	-rm -f google/protobuf/$(DEPDIR)/conformance_objc-TestMessagesProto2.pbobjc.Po
	-rm -f google/protobuf/$(DEPDIR)/conformance_objc-TestMessagesProto3.pbobjc.Po
	-rm -f google/protobuf/$(DEPDIR)/conformance_test_runner-test_messages_proto2.pb.Po
	-rm -f google/protobuf/$(DEPDIR)/conformance_test_runner-test_messages_proto3.pb.Po
	-rm -f third_party/jsoncpp/$(DEPDIR)/conformance_test_runner-jsoncpp.Po
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am:

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am: install-binPROGRAMS

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
		-rm -f ../objectivec/$(DEPDIR)/conformance_objc-GPBProtocolBuffers.Po
	-rm -f ./$(DEPDIR)/conformance_cpp-conformance.pb.Po
	-rm -f ./$(DEPDIR)/conformance_cpp-conformance_cpp.Po
	-rm -f ./$(DEPDIR)/conformance_objc-Conformance.pbobjc.Po
	-rm -f ./$(DEPDIR)/conformance_objc-conformance_objc.Po
	-rm -f ./$(DEPDIR)/conformance_test_runner-binary_json_conformance_suite.Po
	-rm -f ./$(DEPDIR)/conformance_test_runner-conformance.pb.Po
	-rm -f ./$(DEPDIR)/conformance_test_runner-conformance_test.Po
	-rm -f ./$(DEPDIR)/conformance_test_runner-conformance_test_main.Po
	-rm -f ./$(DEPDIR)/conformance_test_runner-conformance_test_runner.Po
	-rm -f ./$(DEPDIR)/conformance_test_runner-text_format_conformance_suite.Po
	-rm -f google/protobuf/$(DEPDIR)/conformance_cpp-test_messages_proto2.pb.Po
	-rm -f google/protobuf/$(DEPDIR)/conformance_cpp-test_messages_proto3.pb.Po
	-rm -f google/protobuf/$(DEPDIR)/conformance_objc-TestMessagesProto2.pbobjc.Po
	-rm -f google/protobuf/$(DEPDIR)/conformance_objc-TestMessagesProto3.pbobjc.Po
	-rm -f google/protobuf/$(DEPDIR)/conformance_test_runner-test_messages_proto2.pb.Po
	-rm -f google/protobuf/$(DEPDIR)/conformance_test_runner-test_messages_proto3.pb.Po
	-rm -f third_party/jsoncpp/$(DEPDIR)/conformance_test_runner-jsoncpp.Po
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-binPROGRAMS

.MAKE: install-am install-strip

.PHONY: CTAGS GTAGS TAGS all all-am am--depfiles check check-am clean \
	clean-binPROGRAMS clean-generic clean-libtool cscopelist-am \
	ctags ctags-am distclean distclean-compile distclean-generic \
	distclean-libtool distclean-tags distdir dvi dvi-am html \
	html-am info info-am install install-am install-binPROGRAMS \
	install-data install-data-am install-dvi install-dvi-am \
	install-exec install-exec-am install-html install-html-am \
	install-info install-info-am install-man install-pdf \
	install-pdf-am install-ps install-ps-am install-strip \
	installcheck installcheck-am installdirs maintainer-clean \
	maintainer-clean-generic mostlyclean mostlyclean-compile \
	mostlyclean-generic mostlyclean-libtool pdf pdf-am ps ps-am \
	tags tags-am uninstall uninstall-am uninstall-binPROGRAMS

.PRECIOUS: Makefile

  # lite/com/google/protobuf/Any.java                            \
  # lite/com/google/protobuf/AnyOrBuilder.java                   \
  # lite/com/google/protobuf/AnyProto.java                       \
  # lite/com/google/protobuf/BoolValue.java                      \
  # lite/com/google/protobuf/BoolValueOrBuilder.java             \
  # lite/com/google/protobuf/BytesValue.java                     \
  # lite/com/google/protobuf/BytesValueOrBuilder.java            \
  # lite/com/google/protobuf/conformance/Conformance.java        \
  # lite/com/google/protobuf/DoubleValue.java                    \
  # lite/com/google/protobuf/DoubleValueOrBuilder.java           \
  # lite/com/google/protobuf/Duration.java                       \
  # lite/com/google/protobuf/DurationOrBuilder.java              \
  # lite/com/google/protobuf/DurationProto.java                  \
  # lite/com/google/protobuf/FieldMask.java                      \
  # lite/com/google/protobuf/FieldMaskOrBuilder.java             \
  # lite/com/google/protobuf/FieldMaskProto.java                 \
  # lite/com/google/protobuf/FloatValue.java                     \
  # lite/com/google/protobuf/FloatValueOrBuilder.java            \
  # lite/com/google/protobuf/Int32Value.java                     \
  # lite/com/google/protobuf/Int32ValueOrBuilder.java            \
  # lite/com/google/protobuf/Int64Value.java                     \
  # lite/com/google/protobuf/Int64ValueOrBuilder.java            \
  # lite/com/google/protobuf/ListValue.java                      \
  # lite/com/google/protobuf/ListValueOrBuilder.java             \
  # lite/com/google/protobuf/NullValue.java                      \
  # lite/com/google/protobuf/StringValue.java                    \
  # lite/com/google/protobuf/StringValueOrBuilder.java           \
  # lite/com/google/protobuf/Struct.java                         \
  # lite/com/google/protobuf/StructOrBuilder.java                \
  # lite/com/google/protobuf/StructProto.java                    \
  # lite/com/google/protobuf/Timestamp.java                      \
  # lite/com/google/protobuf/TimestampOrBuilder.java             \
  # lite/com/google/protobuf/TimestampProto.java                 \
  # lite/com/google/protobuf/UInt32Value.java                    \
  # lite/com/google/protobuf/UInt32ValueOrBuilder.java           \
  # lite/com/google/protobuf/UInt64Value.java                    \
  # lite/com/google/protobuf/UInt64ValueOrBuilder.java           \
  # lite/com/google/protobuf/Value.java                          \
  # lite/com/google/protobuf/ValueOrBuilder.java                 \
  # lite/com/google/protobuf/WrappersProto.java
# Explicit deps because BUILT_SOURCES are only done before a "make all/check"
# so a direct "make test_cpp" could fail if parallel enough.
conformance_test_runner-conformance_test.$(OBJEXT): conformance.pb.h
conformance_test_runner-conformance_test_runner.$(OBJEXT): conformance.pb.h
# Explicit dep because BUILT_SOURCES are only done before a "make all/check"
# so a direct "make test_cpp" could fail if parallel enough.
conformance_cpp-conformance_cpp.$(OBJEXT): conformance.pb.h
# Explicit dep because BUILT_SOURCES are only done before a "make all/check"
# so a direct "make test_objc" could fail if parallel enough.
#conformance_objc-conformance_objc.$(OBJEXT): Conformance.pbobjc.h google/protobuf/TestMessagesProto2.pbobjc.h google/protobuf/TestMessagesProto3.pbobjc.h

# JavaScript well-known types are expected to be in a directory called
# google-protobuf, because they are usually in the google-protobuf npm
# package.  But we want to use the sources from our tree, so we recreate
# that directory structure here.
google-protobuf:
	mkdir google-protobuf

# Some implementations include pre-generated versions of well-known types.
#protoc_middleman: $(conformance_protoc_inputs) $(conformance_proto2_protoc_inputs) $(well_known_type_protoc_inputs) google-protobuf
#	$(PROTOC) -I$(srcdir) -I$(top_srcdir) --cpp_out=. --java_out=. --ruby_out=. --objc_out=. --python_out=. --php_out=. $(conformance_protoc_inputs)
#	$(PROTOC) -I$(srcdir) -I$(top_srcdir) --cpp_out=. --java_out=. --ruby_out=. --objc_out=. --python_out=. $(conformance_proto2_protoc_inputs)
#	$(PROTOC) -I$(srcdir) -I$(top_srcdir) --cpp_out=. --java_out=. --ruby_out=. --python_out=. $(well_known_type_protoc_inputs)
#	touch protoc_middleman

# We have to cd to $(srcdir) before executing protoc because $(protoc_inputs) is
# relative to srcdir, which may not be the same as the current directory when
# building out-of-tree.
protoc_middleman: $(top_srcdir)/src/protoc$(EXEEXT) $(conformance_protoc_inputs) $(conformance_proto2_protoc_inputs) $(well_known_type_protoc_inputs) google-protobuf
	oldpwd=`pwd` && ( cd $(srcdir) && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$(top_srcdir)/src --cpp_out=$$oldpwd --java_out=$$oldpwd --ruby_out=$$oldpwd --objc_out=$$oldpwd --python_out=$$oldpwd --php_out=$$oldpwd $(conformance_protoc_inputs) )
	oldpwd=`pwd` && ( cd $(srcdir) && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$(top_srcdir)/src --cpp_out=$$oldpwd --java_out=$$oldpwd --ruby_out=$$oldpwd --objc_out=$$oldpwd --python_out=$$oldpwd $(conformance_proto2_protoc_inputs) )
	oldpwd=`pwd` && ( cd $(srcdir) && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$(top_srcdir)/src --cpp_out=$$oldpwd --java_out=$$oldpwd --ruby_out=$$oldpwd --python_out=$$oldpwd $(well_known_type_protoc_inputs) )
	touch protoc_middleman

$(protoc_outputs): protoc_middleman

$(other_language_protoc_outputs): protoc_middleman

javac_middleman: ConformanceJava.java protoc_middleman $(other_language_protoc_outputs)
	jar=`ls ../java/util/target/*jar-with-dependencies.jar` && javac -classpath ../java/target/classes:$$jar ConformanceJava.java com/google/protobuf/conformance/Conformance.java com/google/protobuf_test_messages/proto3/TestMessagesProto3.java com/google/protobuf_test_messages/proto2/TestMessagesProto2.java
	@touch javac_middleman

conformance-java: javac_middleman
	@echo "Writing shortcut script conformance-java..."
	@echo '#! /bin/sh' > conformance-java
	@jar=`ls ../java/util/target/*jar-with-dependencies.jar` && echo java -classpath .:../java/target/classes:$$jar ConformanceJava '$$@' >> conformance-java
	@chmod +x conformance-java

javac_middleman_lite: ConformanceJavaLite.java protoc_middleman $(other_language_protoc_outputs)
	javac -classpath ../java/lite/target/classes:lite ConformanceJavaLite.java lite/com/google/protobuf/conformance/Conformance.java
	@touch javac_middleman_lite

conformance-java-lite: javac_middleman_lite
	@echo "Writing shortcut script conformance-java-lite..."
	@echo '#! /bin/sh' > conformance-java-lite
	@echo java -classpath .:../java/lite/target/classes:lite ConformanceJavaLite '$$@' >> conformance-java-lite
	@chmod +x conformance-java-lite

# Currently the conformance code is alongside the rest of the C#
# source, as it's easier to maintain there. We assume we've already
# built that, so we just need a script to run it.
conformance-csharp: $(other_language_protoc_outputs)
	@echo "Writing shortcut script conformance-csharp..."
	@echo '#! /bin/sh' > conformance-csharp
	@echo 'dotnet ../csharp/src/Google.Protobuf.Conformance/bin/Release/netcoreapp3.1/Google.Protobuf.Conformance.dll "$$@"' >> conformance-csharp
	@chmod +x conformance-csharp

conformance-php:
	@echo "Writing shortcut script conformance-php..."
	@echo '#! /bin/sh' > conformance-php
	@echo 'php -d auto_prepend_file=autoload.php ./conformance_php.php' >> conformance-php
	@chmod +x conformance-php

conformance-php-c:
	@echo "Writing shortcut script conformance-php-c..."
	@echo '#! /bin/sh' > conformance-php-c
	@echo 'php -dextension=../php/ext/google/protobuf/modules/protobuf.so ./conformance_php.php' >> conformance-php-c
	@chmod +x conformance-php-c

# Targets for actually running tests.
test_cpp: protoc_middleman conformance-test-runner conformance-cpp
	./conformance-test-runner --enforce_recommended --failure_list failure_list_cpp.txt --text_format_failure_list text_format_failure_list_cpp.txt ./conformance-cpp

test_java: protoc_middleman conformance-test-runner conformance-java
	./conformance-test-runner --enforce_recommended --failure_list failure_list_java.txt --text_format_failure_list text_format_failure_list_java.txt ./conformance-java

test_java_lite: protoc_middleman conformance-test-runner conformance-java-lite
	./conformance-test-runner --enforce_recommended ./conformance-java-lite

test_csharp: protoc_middleman conformance-test-runner conformance-csharp
	./conformance-test-runner --enforce_recommended --failure_list failure_list_csharp.txt --text_format_failure_list text_format_failure_list_csharp.txt ./conformance-csharp

test_ruby: protoc_middleman conformance-test-runner $(other_language_protoc_outputs)
	RUBYLIB=../ruby/lib:. ./conformance-test-runner --enforce_recommended --failure_list failure_list_ruby.txt --text_format_failure_list text_format_failure_list_ruby.txt ./conformance_ruby.rb

test_jruby: protoc_middleman conformance-test-runner $(other_language_protoc_outputs)
	RUBYLIB=../ruby/lib:. ./conformance-test-runner --enforce_recommended --failure_list failure_list_jruby.txt --text_format_failure_list text_format_failure_list_jruby.txt ./conformance_ruby.rb

test_php: protoc_middleman conformance-test-runner conformance-php $(other_language_protoc_outputs)
	./conformance-test-runner --enforce_recommended --failure_list failure_list_php.txt --text_format_failure_list text_format_failure_list_php.txt ./conformance-php

test_php_c: protoc_middleman conformance-test-runner conformance-php-c $(other_language_protoc_outputs)
	./conformance-test-runner --enforce_recommended --failure_list failure_list_php_c.txt --text_format_failure_list text_format_failure_list_php.txt ./conformance-php-c

# These depend on library paths being properly set up.  The easiest way to
# run them is to just use "tox" from the python dir.
test_python: protoc_middleman conformance-test-runner
	./conformance-test-runner --enforce_recommended --failure_list failure_list_python.txt --text_format_failure_list text_format_failure_list_python.txt ./conformance_python.py

test_python_cpp: protoc_middleman conformance-test-runner
	./conformance-test-runner --enforce_recommended --failure_list failure_list_python_cpp.txt --text_format_failure_list text_format_failure_list_python_cpp.txt ./conformance_python.py

#test_objc: protoc_middleman conformance-test-runner conformance-objc
#	./conformance-test-runner --enforce_recommended --failure_list failure_list_objc.txt ./conformance-objc

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
