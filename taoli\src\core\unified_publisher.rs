/// 统一Redis发布器池
///
/// 完全复制PublisherPool的高性能架构，支持任意格式内容发布
/// 使用多线程+预建连接+无锁队列实现零延迟发布

use std::sync::Arc;
use std::sync::atomic::{Ordering, AtomicU64};
use std::thread::{self, Jo<PERSON><PERSON><PERSON><PERSON>};
use std::time::Instant;
use std::collections::HashSet;
use std::sync::{Mutex, LazyLock};

use crossbeam_channel::{Receiver, Sender, unbounded};
use log::{info, error};

use crate::core::types::{UnifiedEvent, RealTimeMetrics, PipelineConfig, get_config};
use crate::core::stats::plugin_stats;
use crate::database::{client, models};

/// 全局代币白名单 - 只有创建过的代币才能创建交易表
static CREATED_TOKENS: LazyLock<Mutex<HashSet<String>>> = LazyLock::new(|| Mutex::new(HashSet::new()));

/// 全局序列号生成器 - 确保交易按接收顺序排列
static GLOBAL_SEQUENCE: AtomicU64 = AtomicU64::new(1);

/// 统计计数器 - 用于间隔报告
static STATS_CREATED_TOKENS: AtomicU64 = AtomicU64::new(0);
static STATS_INSERTED_TRADES: AtomicU64 = AtomicU64::new(0);
static STATS_CREATED_EVENTS: AtomicU64 = AtomicU64::new(0);
static STATS_PUBLISHED_EVENTS: AtomicU64 = AtomicU64::new(0);

/// 最后一次报告时间
static LAST_REPORT_TIME: LazyLock<Mutex<std::time::Instant>> = LazyLock::new(|| Mutex::new(std::time::Instant::now()));

/// 检查并输出统计报告
fn check_and_report_stats() {
    if let Ok(mut last_time) = LAST_REPORT_TIME.lock() {
        let now = std::time::Instant::now();
        // 每5秒报告一次
        if now.duration_since(*last_time).as_secs() >= 5 {
            let created_tokens = STATS_CREATED_TOKENS.load(Ordering::Relaxed);
            let inserted_trades = STATS_INSERTED_TRADES.load(Ordering::Relaxed);
            let created_events = STATS_CREATED_EVENTS.load(Ordering::Relaxed);
            let published_events = STATS_PUBLISHED_EVENTS.load(Ordering::Relaxed);
            
            // 获取所有插件的统计数据
            let manager = &plugin_stats::PLUGIN_STATS_MANAGER;
            let mut plugin_summary = String::new();
            
            for item in manager.stats.iter() {
                let plugin_name = item.key();
                let stats = item.value();
                
                let received = stats.received_count.load(Ordering::Relaxed);
                let parsed = stats.parsed_count.load(Ordering::Relaxed);
                let published = stats.published_count.load(Ordering::Relaxed);
                let parse_errors = stats.parse_errors.load(Ordering::Relaxed);
                let publish_errors = stats.publish_errors.load(Ordering::Relaxed);
                
                if received > 0 || parsed > 0 || published > 0 {
                    if !plugin_summary.is_empty() {
                        plugin_summary.push_str(", ");
                    }
                    
                    if parse_errors > 0 || publish_errors > 0 {
                        plugin_summary.push_str(&format!("{}: 收{}解{}发{} 错误{}+{}", 
                            plugin_name, received, parsed, published, parse_errors, publish_errors));
                    } else {
                        plugin_summary.push_str(&format!("{}: 收{}解{}发{}", 
                            plugin_name, received, parsed, published));
                    }
                }
            }
            
            if plugin_summary.is_empty() {
                plugin_summary = "无活动".to_string();
            }
            
            info!("📊 统计报告 - 创建代币表: {}, 插入交易: {}, 创建事件: {}, 发布事件: {} | 插件: {}", 
                created_tokens, inserted_trades, created_events, published_events, plugin_summary);
            
            *last_time = now;
        }
    }
}

/// 添加代币到白名单
pub fn add_created_token(mint_address: &str) {
    if let Ok(mut tokens) = CREATED_TOKENS.lock() {
        tokens.insert(mint_address.to_string());
        STATS_CREATED_TOKENS.fetch_add(1, Ordering::Relaxed);
        check_and_report_stats();
    }
}

/// 检查代币是否在白名单中
pub fn is_token_created(mint_address: &str) -> bool {
    if let Ok(tokens) = CREATED_TOKENS.lock() {
        tokens.contains(mint_address)
    } else {
        false
    }
}

/// 统一发布器池 - 完全复制PublisherPool架构
pub struct UnifiedPublisherPool {
    pub workers: Vec<JoinHandle<()>>,
    sender: Sender<UnifiedEvent>,
}

impl UnifiedPublisherPool {
    /// 创建新的统一发布器池
    pub fn new(
        config: PipelineConfig,
        metrics: Arc<RealTimeMetrics>,
    ) -> Self {
        let mut workers = Vec::new();
        let redis_url = &get_config().redis.url;

        // 创建无锁队列
        let (sender, receiver) = unbounded::<UnifiedEvent>();

        info!("🚀 启动 {} 个统一发布器工作线程...", config.publisher_threads);

        // 创建原子计数器跟踪连接状态
        let connected_count = Arc::new(std::sync::atomic::AtomicUsize::new(0));
        let total_threads = config.publisher_threads;

        for worker_id in 0..config.publisher_threads {
            let rx = receiver.clone();
            let metrics = Arc::clone(&metrics);
            let bonk_channel = config.bonk_channel.clone();
            let pump_channel = config.pump_channel.clone();
            let pump_create_channel = config.pump_create_channel.clone();
            let redis_url = redis_url.clone();
            let connected_count = Arc::clone(&connected_count);

            let worker = thread::spawn(move || {
                let rt = tokio::runtime::Runtime::new().unwrap();

                rt.block_on(async {
                    // 启动带重试的Redis发布器
                    if let Err(e) = Self::run_publisher_with_retry(
                        worker_id,
                        &redis_url,
                        &bonk_channel,
                        &pump_channel,
                        &pump_create_channel,
                        &rx,
                        &metrics,
                        &connected_count,
                        total_threads,
                    ).await {
                        error!("统一发布器工作线程 {} 最终失败: {}", worker_id, e);
                    }
                });
            });

            workers.push(worker);
        }

        Self { workers, sender }
    }

    /// 获取发送端 - 供解析器使用
    pub fn get_sender(&self) -> Sender<UnifiedEvent> {
        self.sender.clone()
    }

    /// 运行发布器，带自动重试机制 - 完全复制PublisherPool逻辑
    async fn run_publisher_with_retry(
        worker_id: usize,
        redis_url: &str,
        bonk_channel: &str,
        pump_channel: &str,
        pump_create_channel: &str,
        receiver: &Receiver<UnifiedEvent>,
        metrics: &RealTimeMetrics,
        connected_count: &std::sync::atomic::AtomicUsize,
        total_threads: usize,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut retry_count = 0;
        let max_retries = 10;
        let retry_delay = std::time::Duration::from_secs(2);

        loop {
            // 只在第一次连接和失败重试时打印
            if retry_count > 0 {
                info!("🚀 统一发布器工作线程 {} 尝试连接Redis (重试 {}/{})", worker_id, retry_count, max_retries);
            }

            match Self::run_publisher(worker_id, redis_url, bonk_channel, pump_channel, pump_create_channel, receiver, metrics, connected_count, total_threads).await {
                Ok(_) => {
                    info!("✅ 统一发布器工作线程 {} 正常退出", worker_id);
                    break;
                }
                Err(e) => {
                    retry_count += 1;
                    error!("❌ 统一发布器工作线程 {} 连接失败: {} (重试 {}/{})",
                        worker_id, e, retry_count, max_retries);

                    if retry_count >= max_retries {
                        error!("🚨 统一发布器工作线程 {} 达到最大重试次数", worker_id);
                        return Err(e);
                    }

                    tokio::time::sleep(retry_delay).await;
                }
            }
        }

        Ok(())
    }

    /// 运行单个发布器 - 完全复制PublisherPool逻辑
    async fn run_publisher(
        worker_id: usize,
        redis_url: &str,
        bonk_channel: &str,
        pump_channel: &str,
        pump_create_channel: &str,
        receiver: &Receiver<UnifiedEvent>,
        metrics: &RealTimeMetrics,
        connected_count: &std::sync::atomic::AtomicUsize,
        total_threads: usize,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // 创建Redis连接
        let client = redis::Client::open(redis_url.to_string())?;
        let mut conn = client.get_multiplexed_async_connection().await?;

        // 增加连接计数并检查是否所有工作线程都已连接
        let current_count = connected_count.fetch_add(1, Ordering::SeqCst) + 1;
        if current_count == total_threads {
            info!("✅ 所有统一发布器工作线程已连接到Redis");
        }

        loop {
            match receiver.recv() {
                Ok(event) => {
                    let start = Instant::now();

                    // 根据插件名称选择频道
                    let target_channel = match event.plugin_name.as_str() {
                        "bonk" => bonk_channel,
                        "pump" => pump_channel,
                        "pump_create" => pump_create_channel,
                        _ => bonk_channel, // 默认使用bonk频道
                    };

                    // 立即发布 - 无等待
                    match redis::cmd("PUBLISH")
                        .arg(target_channel)
                        .arg(event.content.as_str())
                        .query_async::<redis::aio::MultiplexedConnection, i64>(&mut conn)
                        .await
                    {
                        Ok(_) => {
                            let duration = start.elapsed().as_nanos() as u64;
                            metrics.publish_time_total.fetch_add(duration, Ordering::Relaxed);
                            metrics.published_count.fetch_add(1, Ordering::Relaxed);

                            // 同时写入数据库
                            if let Err(e) = Self::publish_to_database(&event).await {
                                log::warn!("数据库写入失败: {}", e);
                            }

                            // 更新插件统计
                            plugin_stats::increment_published(event.plugin_name.clone());
                            plugin_stats::add_publish_time(event.plugin_name, duration);
                            
                            // 更新发布统计
                            STATS_PUBLISHED_EVENTS.fetch_add(1, Ordering::Relaxed);
                            check_and_report_stats();
                        }
                        Err(e) => {
                            metrics.publish_errors.fetch_add(1, Ordering::Relaxed);
                            error!("Redis发布失败: {}", e);

                            // Redis错误时退出，让上层重试
                            return Err(e.into());
                        }
                    }
                }
                Err(_) => {
                    info!("统一发布器工作线程 {} 接收通道关闭", worker_id);
                    break;
                }
            }
        }

        Ok(())
    }

    /// 发布事件到数据库
    async fn publish_to_database(event: &UnifiedEvent) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // 根据插件名称和内容解析数据并写入数据库
        match event.plugin_name.as_str() {
            "pump_create" => {
                // 解析pump创建事件JSON
                if let Ok(create_event) = models::PumpCreateEvent::from_json(&event.content) {
                    // 添加代币到白名单
                    add_created_token(&create_event.mint);

                    client::insert_pump_create_event(create_event).await?;
                    STATS_CREATED_EVENTS.fetch_add(1, Ordering::Relaxed);
                    check_and_report_stats();
                }
            }
            "pump" => {
                // 解析pump交易事件文本
                match models::PumpTradeRecord::from_text(&event.content) {
                    Ok(trade_record) => {
                        // 检查代币是否在白名单中
                        if is_token_created(&trade_record.mint_address) {
                            // 生成全局序列号确保按接收顺序排列
                            let sequence_id = GLOBAL_SEQUENCE.fetch_add(1, Ordering::SeqCst);
                            
                            // 只插入到代币专属交易表
                            if let Err(e) = client::insert_token_trade_with_sequence(trade_record.mint_address.clone(), trade_record.clone(), sequence_id).await {
                                log::warn!("代币{}专属表写入失败: {}", trade_record.mint_address, e);
                            } else {
                                STATS_INSERTED_TRADES.fetch_add(1, Ordering::Relaxed);
                                check_and_report_stats();
                            }
                        } else {
                            log::debug!("代币{}未在白名单中，跳过交易记录", trade_record.mint_address);
                        }
                    }
                    Err(e) => {
                        log::warn!("Pump交易事件解析失败: {} - 内容: {}", e, &event.content[..std::cmp::min(200, event.content.len())]);
                    }
                }
            }

            _ => {
                log::debug!("未知事件类型: {}", event.plugin_name);
            }
        }

        Ok(())
    }
}

/// 便捷发布函数 - 供解析器直接调用
///
/// # 参数
/// * `content` - 要发布的内容字符串
/// * `plugin_name` - 插件名称（用于统计）
///
/// # 返回
/// * `Result<()>` - 发布结果
pub fn send_to_unified_publisher(
    sender: &Sender<UnifiedEvent>,
    content: String,
    plugin_name: String,
) -> Result<(), crossbeam_channel::SendError<UnifiedEvent>> {
    use compact_str::CompactString;

    let event = UnifiedEvent {
        content: CompactString::new(&content),
        timestamp: chrono::Utc::now().timestamp() as u64,
        publish_start: Instant::now(),
        plugin_name,
    };

    sender.send(event)
}
