/// 插件统计系统
/// 
/// 独立跟踪各个插件的处理状态，包括接收、解析和发布数量
/// 设计为可扩展的架构，便于添加新插件
/// 完全无锁实现，高性能并发

use std::sync::Arc;
use std::sync::atomic::{AtomicU64, Ordering};
use std::time::{Duration, Instant};
use dashmap::DashMap;
use once_cell::sync::Lazy;
use chrono;
use log::info;

// 自定义日志宏，使用标准日志系统
macro_rules! simple_log {
    ($($arg:tt)+) => {
        info!($($arg)+);
    }
}

/// 插件统计数据
#[derive(Debug)]
pub struct PluginStats {
    /// 插件名称
    pub name: String,
    /// 接收的交易数量
    pub received_count: AtomicU64,
    /// 成功解析的交易数量
    pub parsed_count: AtomicU64,
    /// 成功发布的交易数量
    pub published_count: AtomicU64,
    /// 解析错误数量
    pub parse_errors: AtomicU64,
    /// 发布错误数量
    pub publish_errors: AtomicU64,
    /// 创建时间
    pub created_at: Instant,
    /// 解析总耗时（纳秒）
    pub parse_time_total: AtomicU64,
    /// 发布总耗时（纳秒）
    pub publish_time_total: AtomicU64,
}

impl PluginStats {
    /// 创建新的插件统计
    pub fn new(name: &str) -> Self {
        Self {
            name: name.to_string(),
            received_count: AtomicU64::new(0),
            parsed_count: AtomicU64::new(0),
            published_count: AtomicU64::new(0),
            parse_errors: AtomicU64::new(0),
            publish_errors: AtomicU64::new(0),
            created_at: Instant::now(),
            parse_time_total: AtomicU64::new(0),
            publish_time_total: AtomicU64::new(0),
        }
    }
    
    /// 增加接收计数
    pub fn increment_received(&self) {
        self.received_count.fetch_add(1, Ordering::Relaxed);
    }
    
    /// 增加解析成功计数
    pub fn increment_parsed(&self) {
        self.parsed_count.fetch_add(1, Ordering::Relaxed);
    }
    
    /// 增加发布成功计数
    pub fn increment_published(&self) {
        self.published_count.fetch_add(1, Ordering::Relaxed);
    }
    
    /// 增加解析错误计数
    pub fn increment_parse_error(&self) {
        self.parse_errors.fetch_add(1, Ordering::Relaxed);
    }
    
    /// 增加发布错误计数
    pub fn increment_publish_error(&self) {
        self.publish_errors.fetch_add(1, Ordering::Relaxed);
    }

    /// 增加解析时间统计
    pub fn add_parse_time(&self, time_ns: u64) {
        self.parse_time_total.fetch_add(time_ns, Ordering::Relaxed);
    }
    
    /// 增加发布时间统计
    pub fn add_publish_time(&self, time_ns: u64) {
        self.publish_time_total.fetch_add(time_ns, Ordering::Relaxed);
    }
}

/// 全局插件统计管理器
pub static PLUGIN_STATS_MANAGER: Lazy<Arc<PluginStatsManager>> = Lazy::new(|| {
    PluginStatsManager::new()
});

// 在主函数的tokio运行时上启动报告任务
pub fn start_plugin_stats_reporting() {
    tokio::spawn(async {
        // 给系统一些时间先启动
        tokio::time::sleep(Duration::from_secs(3)).await;
        
        // 然后开始定期报告
        let manager = Arc::clone(&PLUGIN_STATS_MANAGER);
        manager.start_reporting().await;
    });
}

/// 无锁插件统计管理器
#[derive(Debug)]
pub struct PluginStatsManager {
    /// 插件统计数据映射表 - 使用无锁DashMap
    stats: DashMap<String, Arc<PluginStats>>,
    /// 是否已启动报告
    reporting_started: AtomicU64,
}

impl PluginStatsManager {
    /// 创建新的插件统计管理器
    pub fn new() -> Arc<Self> {
        Arc::new(Self {
            stats: DashMap::new(),
            reporting_started: AtomicU64::new(0),
        })
    }
    
    /// 获取插件统计，如果不存在则创建 - 支持异步调用
    pub async fn get_stats(&self, plugin_name: &str) -> Arc<PluginStats> {
        // 无锁访问 - 先检查是否存在
        if let Some(stats) = self.stats.get(plugin_name) {
            return Arc::clone(stats.value());
        }
        
        // 不存在，创建并插入
        let plugin_stats = Arc::new(PluginStats::new(plugin_name));
        self.stats.insert(plugin_name.to_string(), Arc::clone(&plugin_stats));
        plugin_stats
    }
    
    /// 同步获取插件统计的快捷函数
    pub fn get_stats_sync(&self, plugin_name: &str) -> Arc<PluginStats> {
        // 无锁访问 - 先检查是否存在
        if let Some(stats) = self.stats.get(plugin_name) {
            return Arc::clone(stats.value());
        }
        
        // 不存在，创建并插入
        let plugin_stats = Arc::new(PluginStats::new(plugin_name));
        self.stats.insert(plugin_name.to_string(), Arc::clone(&plugin_stats));
        plugin_stats
    }
    
    /// 启动定期报告
    pub async fn start_reporting(&self) {
        // 避免重复启动
        if self.reporting_started.fetch_add(1, Ordering::SeqCst) > 0 {
            return;
        }
        
        // 每秒报告一次统计数据
        let mut interval = tokio::time::interval(Duration::from_secs(1));
        
        // 存储上次报告的计数值，用于计算TPS
        let mut last_counts: DashMap<String, (u64, u64, u64, u64, u64)> = DashMap::new();
        
        loop {
            interval.tick().await;
            
            if self.stats.is_empty() {
                continue;
            }
            
            // 计算并打印每个插件的统计信息
            for item in self.stats.iter() {
                let plugin_name = item.key();
                let stats = item.value();
                
                let received = stats.received_count.load(Ordering::Relaxed);
                let parsed = stats.parsed_count.load(Ordering::Relaxed);
                let published = stats.published_count.load(Ordering::Relaxed);
                let parse_errors = stats.parse_errors.load(Ordering::Relaxed);
                let publish_errors = stats.publish_errors.load(Ordering::Relaxed);
                
                // 计算平均耗时（纳秒）
                let parse_time_total = stats.parse_time_total.load(Ordering::Relaxed);
                let publish_time_total = stats.publish_time_total.load(Ordering::Relaxed);
                let avg_parse_time = if parsed > 0 { parse_time_total / parsed } else { 0 };
                let avg_publish_time = if published > 0 { publish_time_total / published } else { 0 };
                
                // 计算TPS (transactions per second)
                let (last_received, last_parsed, last_published, last_parse_errors, last_publish_errors) = 
                    last_counts.get(plugin_name)
                        .map(|v| *v.value())
                        .unwrap_or((0, 0, 0, 0, 0));
                
                // 计算每秒处理量
                let received_tps = if received > last_received { received - last_received } else { 0 };
                let parsed_tps = if parsed > last_parsed { parsed - last_parsed } else { 0 };
                let published_tps = if published > last_published { published - last_published } else { 0 };
                let parse_errors_tps = if parse_errors > last_parse_errors { parse_errors - last_parse_errors } else { 0 };
                let publish_errors_tps = if publish_errors > last_publish_errors { publish_errors - last_publish_errors } else { 0 };
                
                // 更新上次计数
                last_counts.insert(plugin_name.clone(), 
                           (received, parsed, published, parse_errors, publish_errors));
                
                // 禁用单独的插件统计输出 - 统计数据已合并到统一报告中
                // if received_tps > 0 || parsed_tps > 0 || published_tps > 0 || parse_errors_tps > 0 || publish_errors_tps > 0 {
                //     // 统计输出已合并到统一报告
                // }
            }
        }
    }
}

/// 便捷函数：增加插件接收计数 - 完全无锁实现
pub fn increment_received(plugin_name: String) {
    // 从全局管理器获取统计并增加计数
    let stats = PLUGIN_STATS_MANAGER.get_stats_sync(&plugin_name);
    stats.increment_received();
}

/// 便捷函数：增加插件解析计数 - 完全无锁实现
pub fn increment_parsed(plugin_name: String) {
    // 从全局管理器获取统计并增加计数
    let stats = PLUGIN_STATS_MANAGER.get_stats_sync(&plugin_name);
    stats.increment_parsed();
}

/// 便捷函数：增加插件发布计数 - 完全无锁实现
pub fn increment_published(plugin_name: String) {
    // 从全局管理器获取统计并增加计数
    let stats = PLUGIN_STATS_MANAGER.get_stats_sync(&plugin_name);
    stats.increment_published();
}

/// 便捷函数：增加插件解析错误计数 - 完全无锁实现
pub fn increment_parse_error(plugin_name: String) {
    // 从全局管理器获取统计并增加计数
    let stats = PLUGIN_STATS_MANAGER.get_stats_sync(&plugin_name);
    stats.increment_parse_error();
}

/// 便捷函数：增加插件发布错误计数 - 完全无锁实现
pub fn increment_publish_error(plugin_name: String) {
    // 从全局管理器获取统计并增加计数
    let stats = PLUGIN_STATS_MANAGER.get_stats_sync(&plugin_name);
    stats.increment_publish_error();
} 

/// 便捷函数：增加插件解析耗时
pub fn add_parse_time(plugin_name: String, time_ns: u64) {
    let stats = PLUGIN_STATS_MANAGER.get_stats_sync(&plugin_name);
    stats.add_parse_time(time_ns);
}

/// 便捷函数：增加插件发布耗时
pub fn add_publish_time(plugin_name: String, time_ns: u64) {
    let stats = PLUGIN_STATS_MANAGER.get_stats_sync(&plugin_name);
    stats.add_publish_time(time_ns);
} 