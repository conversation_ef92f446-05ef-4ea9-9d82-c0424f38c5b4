# libprotoc.la - a libtool library file
# Generated by libtool (GNU libtool) 2.4.7
#
# Please DO NOT delete this file!
# It is necessary for linking the library.

# The name that we can dlopen(3).
dlname=''

# Names of this library.
library_names=''

# The name of the static archive.
old_library='libprotoc.a'

# Linker flags that cannot go in dependency_libs.
inherited_linker_flags=' -pthread'

# Libraries that this one depends upon.
dependency_libs=' /d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/lib/libprotobuf.la -lz'

# Names of additional weak libraries provided by this library
weak_library_names=''

# Version information for libprotoc.
current=32
age=0
revision=5

# Is this an already installed library?
installed=yes

# Should we warn about portability when linking against -modules?
shouldnotlink=no

# Files to dlopen/dlpreopen
dlopen=''
dlpreopen=''

# Directory that this library needs to be installed in:
libdir='/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/lib'
