This file contains any messages produced by compilers while
running configure, to aid debugging if configure makes a mistake.

It was created by Protocol Buffers configure 3.21.5, which was
generated by GNU Autoconf 2.71.  Invocation command line was

  $ C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\protobuf-src-1.1.0+21.5\protobuf\configure '--prefix=D:\personal\Desktop\aibot\taoli\target\release\build\protobuf-src-35150cc661cf27c2\out\install' --prefix=/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install --srcdir=/c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf --disable-shared --enable-static --disable-maintainer-mode

## --------- ##
## Platform. ##
## --------- ##

hostname = GXLF-20250516AG
uname -m = x86_64
uname -r = 3.6.3-ab81aae6.x86_64
uname -s = MINGW64_NT-10.0-19045
uname -v = 2025-07-01 18:20 UTC

/usr/bin/uname -p = unknown
/bin/uname -X     = unknown

/bin/arch              = x86_64
/usr/bin/arch -k       = unknown
/usr/convex/getsysinfo = unknown
/usr/bin/hostinfo      = unknown
/bin/machine           = unknown
/usr/bin/oslevel       = unknown
/bin/universe          = unknown

PATH: /d/personal/Desktop/aibot/taoli/target/release/deps/
PATH: /d/personal/Desktop/aibot/taoli/target/release/
PATH: /c/Users/<USER>/.rustup/toolchains/stable-x86_64-pc-windows-gnu/lib/rustlib/x86_64-pc-windows-gnu/lib/
PATH: /d/node/
PATH: /f/
PATH: /c/windows/System32/
PATH: /usr/bin/
PATH: /e/protobuf/bin/
PATH: /e/protobuf/
PATH: /e/protobuf/bin/protoc.exe/
PATH: /c/Users/<USER>/.cargo/bin/
PATH: /d/kaifachanpin/OpenSSL/OpenSSL-Win64/bin/
PATH: /c/windows/system32/
PATH: /c/windows/
PATH: /c/windows/System32/Wbem/
PATH: /c/windows/System32/WindowsPowerShell/v1.0/
PATH: /c/windows/System32/OpenSSH/
PATH: /e/cursor/resources/app/bin/
PATH: /e/Git/cmd/
PATH: /c/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/
PATH: /c/Users/<USER>/.rustup/toolchains/stable-x86_64-pc-windows-msvc/bin/
PATH: /e/Python/
PATH: /d/kaifachanpin/VC/vcpkg/
PATH: /e/git/
PATH: /e/git/usr/bin/
PATH: /c/Users/<USER>/AppData/Roaming/npm/
PATH: /d/kaifachanpin/OpenSSL/OpenSSL-Win64/
PATH: /mingw64/bin/
PATH: /mingw64/include/
PATH: /mingw64/lib/
PATH: /ucrt64/bin/make.exe/
PATH: /c/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.8.0-win32-x64/bundled/scripts/noConfigScripts/
PATH: /ucrt64/include/
PATH: /d/node/
PATH: /d/kiro/Kiro/bin/
PATH: /c/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/
PATH: /c/Program Files/Docker/Docker/resources/bin/docker.exe/
PATH: /c/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-win32-x64/bundled/scripts/noConfigScripts/
PATH: /c/Users/<USER>/.rustup/toolchains/stable-x86_64-pc-windows-gnu/bin/


## ----------- ##
## Core tests. ##
## ----------- ##

configure:3059: looking for aux files: ltmain.sh ar-lib compile missing install-sh config.guess config.sub
configure:3072:  trying /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/
configure:3101:   /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/ltmain.sh found
configure:3101:   /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/ar-lib found
configure:3101:   /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/compile found
configure:3101:   /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/missing found
configure:3083:   /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/install-sh found
configure:3101:   /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/config.guess found
configure:3101:   /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/config.sub found
configure:3214: checking whether to enable maintainer-specific portions of Makefiles
configure:3224: result: no
configure:3275: checking build system type
configure:3290: result: x86_64-pc-mingw64
configure:3310: checking host system type
configure:3324: result: x86_64-pc-mingw64
configure:3344: checking target system type
configure:3358: result: x86_64-pc-mingw64
configure:3402: checking for a BSD-compatible install
configure:3475: result: /usr/bin/install -c
configure:3486: checking whether build environment is sane
configure:3541: result: yes
configure:3700: checking for a race-free mkdir -p
configure:3744: result: /usr/bin/mkdir -p
configure:3751: checking for gawk
configure:3772: found /usr/bin/gawk
configure:3783: result: gawk
configure:3794: checking whether make sets $(MAKE)
configure:3817: result: yes
configure:3847: checking whether make supports nested variables
configure:3865: result: yes
configure:3950: checking whether UID '197108' is supported by ustar format
configure:3953: result: yes
configure:3960: checking whether GID '197121' is supported by ustar format
configure:3963: result: yes
configure:3971: checking how to create a ustar tar archive
configure:3982: tar --version
bsdtar 3.5.2 - libarchive 3.5.2 zlib/1.2.5.f-ipp 
configure:3985: $? = 0
configure:4025: tardir=conftest.dir && eval tar --format=ustar -chf - "$tardir" >conftest.tar
configure:4028: $? = 0
configure:4032: tar -xf - <conftest.tar
configure:4035: $? = 0
configure:4037: cat conftest.dir/file
GrepMe
configure:4040: $? = 0
configure:4054: result: gnutar
configure:4134: checking whether make supports nested variables
configure:4152: result: yes
configure:4263: checking for gcc
configure:4295: result: gcc.exe
configure:4648: checking for C compiler version
configure:4657: gcc.exe --version >&5
gcc.exe (Rev6, Built by MSYS2 project) 15.1.0
Copyright (C) 2025 Free Software Foundation, Inc.
This is free software; see the source for copying conditions.  There is NO
warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.

configure:4668: $? = 0
configure:4657: gcc.exe -v >&5
Using built-in specs.
COLLECT_GCC=C:\msys64\mingw64\bin\gcc.exe
COLLECT_LTO_WRAPPER=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe
Target: x86_64-w64-mingw32
Configured with: ../gcc-15.1.0/configure --prefix=/mingw64 --with-local-prefix=/mingw64/local --with-native-system-header-dir=/mingw64/include --libexecdir=/mingw64/lib --enable-bootstrap --enable-checking=release --with-arch=nocona --with-tune=generic --enable-mingw-wildcard --enable-languages=c,lto,c++,fortran,ada,objc,obj-c++,jit --enable-shared --enable-static --enable-libatomic --enable-threads=posix --enable-graphite --enable-fully-dynamic-string --enable-libstdcxx-backtrace=yes --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --disable-libstdcxx-pch --enable-lto --enable-libgomp --disable-libssp --disable-multilib --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-libiconv --with-system-zlib --with-gmp=/mingw64 --with-mpfr=/mingw64 --with-mpc=/mingw64 --with-isl=/mingw64 --with-pkgversion='Rev6, Built by MSYS2 project' --with-bugurl=https://github.com/msys2/MINGW-packages/issues --with-gnu-as --with-gnu-ld --disable-libstdcxx-debug --enable-plugin --with-boot-ldflags=-static-libstdc++ --with-stage1-ldflags=-static-libstdc++
Thread model: posix
Supported LTO compression algorithms: zlib zstd
gcc version 15.1.0 (Rev6, Built by MSYS2 project) 
configure:4668: $? = 0
configure:4657: gcc.exe -V >&5
gcc.exe: error: unrecognized command-line option '-V'
gcc.exe: fatal error: no input files
compilation terminated.
configure:4668: $? = 1
configure:4657: gcc.exe -qversion >&5
gcc.exe: error: unrecognized command-line option '-qversion'; did you mean '--version'?
gcc.exe: fatal error: no input files
compilation terminated.
configure:4668: $? = 1
configure:4657: gcc.exe -version >&5
gcc.exe: error: unrecognized command-line option '-version'
gcc.exe: fatal error: no input files
compilation terminated.
configure:4668: $? = 1
configure:4688: checking whether the C compiler works
configure:4710: gcc.exe -O0 -ffunction-sections -fdata-sections -m64   conftest.c  >&5
configure:4714: $? = 0
configure:4764: result: yes
configure:4767: checking for C compiler default output file name
configure:4769: result: a.exe
configure:4775: checking for suffix of executables
configure:4782: gcc.exe -o conftest.exe -O0 -ffunction-sections -fdata-sections -m64   conftest.c  >&5
configure:4786: $? = 0
configure:4809: result: .exe
configure:4831: checking whether we are cross compiling
configure:4839: gcc.exe -o conftest.exe -O0 -ffunction-sections -fdata-sections -m64   conftest.c  >&5
configure:4843: $? = 0
configure:4850: ./conftest.exe
configure:4854: $? = 0
configure:4869: result: no
configure:4874: checking for suffix of object files
configure:4897: gcc.exe -c -O0 -ffunction-sections -fdata-sections -m64  conftest.c >&5
configure:4901: $? = 0
configure:4923: result: o
configure:4927: checking whether the compiler supports GNU C
configure:4947: gcc.exe -c -O0 -ffunction-sections -fdata-sections -m64  conftest.c >&5
configure:4947: $? = 0
configure:4957: result: yes
configure:4968: checking whether gcc.exe accepts -g
configure:4989: gcc.exe -c -g  conftest.c >&5
configure:4989: $? = 0
configure:5033: result: yes
configure:5053: checking for gcc.exe option to enable C11 features
configure:5068: gcc.exe  -c -O0 -ffunction-sections -fdata-sections -m64  conftest.c >&5
conftest.c: In function 'e':
conftest.c:25:14: warning: old-style function definition [-Wold-style-definition]
   25 | static char *e (p, i)
      |              ^
configure:5068: $? = 0
configure:5086: result: none needed
configure:5202: checking whether gcc.exe understands -c and -o together
configure:5225: gcc.exe -c conftest.c -o conftest2.o
configure:5228: $? = 0
configure:5225: gcc.exe -c conftest.c -o conftest2.o
configure:5228: $? = 0
configure:5240: result: yes
configure:5260: checking whether make supports the include directive
configure:5275: make -f confmf.GNU && cat confinc.out
this is the am__doit target
configure:5278: $? = 0
configure:5297: result: yes (GNU style)
configure:5323: checking dependency style of gcc.exe
configure:5435: result: gcc3
configure:5578: checking for C++ compiler version
configure:5587: g++.exe --version >&5
g++.exe (Rev6, Built by MSYS2 project) 15.1.0
Copyright (C) 2025 Free Software Foundation, Inc.
This is free software; see the source for copying conditions.  There is NO
warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.

configure:5598: $? = 0
configure:5587: g++.exe -v >&5
Using built-in specs.
COLLECT_GCC=C:\msys64\mingw64\bin\g++.exe
COLLECT_LTO_WRAPPER=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe
Target: x86_64-w64-mingw32
Configured with: ../gcc-15.1.0/configure --prefix=/mingw64 --with-local-prefix=/mingw64/local --with-native-system-header-dir=/mingw64/include --libexecdir=/mingw64/lib --enable-bootstrap --enable-checking=release --with-arch=nocona --with-tune=generic --enable-mingw-wildcard --enable-languages=c,lto,c++,fortran,ada,objc,obj-c++,jit --enable-shared --enable-static --enable-libatomic --enable-threads=posix --enable-graphite --enable-fully-dynamic-string --enable-libstdcxx-backtrace=yes --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --disable-libstdcxx-pch --enable-lto --enable-libgomp --disable-libssp --disable-multilib --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-libiconv --with-system-zlib --with-gmp=/mingw64 --with-mpfr=/mingw64 --with-mpc=/mingw64 --with-isl=/mingw64 --with-pkgversion='Rev6, Built by MSYS2 project' --with-bugurl=https://github.com/msys2/MINGW-packages/issues --with-gnu-as --with-gnu-ld --disable-libstdcxx-debug --enable-plugin --with-boot-ldflags=-static-libstdc++ --with-stage1-ldflags=-static-libstdc++
Thread model: posix
Supported LTO compression algorithms: zlib zstd
gcc version 15.1.0 (Rev6, Built by MSYS2 project) 
configure:5598: $? = 0
configure:5587: g++.exe -V >&5
g++.exe: error: unrecognized command-line option '-V'
g++.exe: fatal error: no input files
compilation terminated.
configure:5598: $? = 1
configure:5587: g++.exe -qversion >&5
g++.exe: error: unrecognized command-line option '-qversion'; did you mean '--version'?
g++.exe: fatal error: no input files
compilation terminated.
configure:5598: $? = 1
configure:5602: checking whether the compiler supports GNU C++
configure:5622: g++.exe -c -O0 -ffunction-sections -fdata-sections -m64  conftest.cpp >&5
configure:5622: $? = 0
configure:5632: result: yes
configure:5643: checking whether g++.exe accepts -g
configure:5664: g++.exe -c -g  conftest.cpp >&5
configure:5664: $? = 0
configure:5708: result: yes
configure:5728: checking for g++.exe option to enable C++11 features
configure:5743: g++.exe  -c -O0 -ffunction-sections -fdata-sections -m64  conftest.cpp >&5
conftest.cpp: In function 'int main(int, char**)':
conftest.cpp:177:25: warning: empty parentheses were disambiguated as a function declaration [-Wvexing-parse]
  177 |   cxx11test::delegate d2();
      |                         ^~
conftest.cpp:177:25: note: remove parentheses to default-initialize a variable
  177 |   cxx11test::delegate d2();
      |                         ^~
      |                         --
conftest.cpp:177:25: note: or replace parentheses with braces to value-initialize a variable
configure:5743: $? = 0
configure:5761: result: none needed
configure:5827: checking dependency style of g++.exe
configure:5939: result: gcc3
configure:5959: checking how to run the C preprocessor
configure:5985: gcc.exe -E  conftest.c
configure:5985: $? = 0
configure:6000: gcc.exe -E  conftest.c
conftest.c:11:10: fatal error: ac_nonexistent.h: No such file or directory
   11 | #include <ac_nonexistent.h>
      |          ^~~~~~~~~~~~~~~~~~
compilation terminated.
configure:6000: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "Protocol Buffers"
| #define PACKAGE_TARNAME "protobuf"
| #define PACKAGE_VERSION "3.21.5"
| #define PACKAGE_STRING "Protocol Buffers 3.21.5"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define PACKAGE "protobuf"
| #define VERSION "3.21.5"
| /* end confdefs.h.  */
| #include <ac_nonexistent.h>
configure:6027: result: gcc.exe -E
configure:6041: gcc.exe -E  conftest.c
configure:6041: $? = 0
configure:6056: gcc.exe -E  conftest.c
conftest.c:11:10: fatal error: ac_nonexistent.h: No such file or directory
   11 | #include <ac_nonexistent.h>
      |          ^~~~~~~~~~~~~~~~~~
compilation terminated.
configure:6056: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "Protocol Buffers"
| #define PACKAGE_TARNAME "protobuf"
| #define PACKAGE_VERSION "3.21.5"
| #define PACKAGE_STRING "Protocol Buffers 3.21.5"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define PACKAGE "protobuf"
| #define VERSION "3.21.5"
| /* end confdefs.h.  */
| #include <ac_nonexistent.h>
configure:6147: checking for gcc
configure:6168: found /mingw64/bin/gcc
configure:6179: result: gcc
configure:6532: checking for C compiler version
configure:6541: gcc --version >&5
gcc.exe (Rev6, Built by MSYS2 project) 15.1.0
Copyright (C) 2025 Free Software Foundation, Inc.
This is free software; see the source for copying conditions.  There is NO
warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.

configure:6552: $? = 0
configure:6541: gcc -v >&5
Using built-in specs.
COLLECT_GCC=C:\msys64\mingw64\bin\gcc.exe
COLLECT_LTO_WRAPPER=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe
Target: x86_64-w64-mingw32
Configured with: ../gcc-15.1.0/configure --prefix=/mingw64 --with-local-prefix=/mingw64/local --with-native-system-header-dir=/mingw64/include --libexecdir=/mingw64/lib --enable-bootstrap --enable-checking=release --with-arch=nocona --with-tune=generic --enable-mingw-wildcard --enable-languages=c,lto,c++,fortran,ada,objc,obj-c++,jit --enable-shared --enable-static --enable-libatomic --enable-threads=posix --enable-graphite --enable-fully-dynamic-string --enable-libstdcxx-backtrace=yes --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --disable-libstdcxx-pch --enable-lto --enable-libgomp --disable-libssp --disable-multilib --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-libiconv --with-system-zlib --with-gmp=/mingw64 --with-mpfr=/mingw64 --with-mpc=/mingw64 --with-isl=/mingw64 --with-pkgversion='Rev6, Built by MSYS2 project' --with-bugurl=https://github.com/msys2/MINGW-packages/issues --with-gnu-as --with-gnu-ld --disable-libstdcxx-debug --enable-plugin --with-boot-ldflags=-static-libstdc++ --with-stage1-ldflags=-static-libstdc++
Thread model: posix
Supported LTO compression algorithms: zlib zstd
gcc version 15.1.0 (Rev6, Built by MSYS2 project) 
configure:6552: $? = 0
configure:6541: gcc -V >&5
gcc.exe: error: unrecognized command-line option '-V'
gcc.exe: fatal error: no input files
compilation terminated.
configure:6552: $? = 1
configure:6541: gcc -qversion >&5
gcc.exe: error: unrecognized command-line option '-qversion'; did you mean '--version'?
gcc.exe: fatal error: no input files
compilation terminated.
configure:6552: $? = 1
configure:6541: gcc -version >&5
gcc.exe: error: unrecognized command-line option '-version'
gcc.exe: fatal error: no input files
compilation terminated.
configure:6552: $? = 1
configure:6556: checking whether the compiler supports GNU C
configure:6586: result: yes
configure:6597: checking whether gcc accepts -g
configure:6618: gcc.exe -c -O0 -ffunction-sections -fdata-sections -m64  conftest.c >&5
configure:6618: $? = 0
configure:6662: result: yes
configure:6682: checking for gcc option to enable C11 features
configure:6715: result: none needed
configure:6831: checking whether gcc understands -c and -o together
configure:6869: result: yes
configure:6888: checking dependency style of gcc
configure:7000: result: gcc3
configure:7020: checking how to run the C preprocessor
configure:7046: gcc.exe -E  conftest.c
configure:7046: $? = 0
configure:7061: gcc.exe -E  conftest.c
conftest.c:11:10: fatal error: ac_nonexistent.h: No such file or directory
   11 | #include <ac_nonexistent.h>
      |          ^~~~~~~~~~~~~~~~~~
compilation terminated.
configure:7061: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "Protocol Buffers"
| #define PACKAGE_TARNAME "protobuf"
| #define PACKAGE_VERSION "3.21.5"
| #define PACKAGE_STRING "Protocol Buffers 3.21.5"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define PACKAGE "protobuf"
| #define VERSION "3.21.5"
| /* end confdefs.h.  */
| #include <ac_nonexistent.h>
configure:7088: result: gcc -E
configure:7102: gcc.exe -E  conftest.c
configure:7102: $? = 0
configure:7117: gcc.exe -E  conftest.c
conftest.c:11:10: fatal error: ac_nonexistent.h: No such file or directory
   11 | #include <ac_nonexistent.h>
      |          ^~~~~~~~~~~~~~~~~~
compilation terminated.
configure:7117: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "Protocol Buffers"
| #define PACKAGE_TARNAME "protobuf"
| #define PACKAGE_VERSION "3.21.5"
| #define PACKAGE_STRING "Protocol Buffers 3.21.5"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define PACKAGE "protobuf"
| #define VERSION "3.21.5"
| /* end confdefs.h.  */
| #include <ac_nonexistent.h>
configure:7161: checking how to run the C++ preprocessor
configure:7183: g++.exe -E  conftest.cpp
configure:7183: $? = 0
configure:7198: g++.exe -E  conftest.cpp
conftest.cpp:11:10: fatal error: ac_nonexistent.h: No such file or directory
   11 | #include <ac_nonexistent.h>
      |          ^~~~~~~~~~~~~~~~~~
compilation terminated.
configure:7198: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "Protocol Buffers"
| #define PACKAGE_TARNAME "protobuf"
| #define PACKAGE_VERSION "3.21.5"
| #define PACKAGE_STRING "Protocol Buffers 3.21.5"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define PACKAGE "protobuf"
| #define VERSION "3.21.5"
| /* end confdefs.h.  */
| #include <ac_nonexistent.h>
configure:7225: result: g++.exe -E
configure:7239: g++.exe -E  conftest.cpp
configure:7239: $? = 0
configure:7254: g++.exe -E  conftest.cpp
conftest.cpp:11:10: fatal error: ac_nonexistent.h: No such file or directory
   11 | #include <ac_nonexistent.h>
      |          ^~~~~~~~~~~~~~~~~~
compilation terminated.
configure:7254: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "Protocol Buffers"
| #define PACKAGE_TARNAME "protobuf"
| #define PACKAGE_VERSION "3.21.5"
| #define PACKAGE_STRING "Protocol Buffers 3.21.5"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define PACKAGE "protobuf"
| #define VERSION "3.21.5"
| /* end confdefs.h.  */
| #include <ac_nonexistent.h>
configure:7354: checking for g++
configure:7375: found /mingw64/bin/g++
configure:7386: result: g++
configure:7413: checking for C++ compiler version
configure:7422: g++ --version >&5
g++.exe (Rev6, Built by MSYS2 project) 15.1.0
Copyright (C) 2025 Free Software Foundation, Inc.
This is free software; see the source for copying conditions.  There is NO
warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.

configure:7433: $? = 0
configure:7422: g++ -v >&5
Using built-in specs.
COLLECT_GCC=C:\msys64\mingw64\bin\g++.exe
COLLECT_LTO_WRAPPER=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe
Target: x86_64-w64-mingw32
Configured with: ../gcc-15.1.0/configure --prefix=/mingw64 --with-local-prefix=/mingw64/local --with-native-system-header-dir=/mingw64/include --libexecdir=/mingw64/lib --enable-bootstrap --enable-checking=release --with-arch=nocona --with-tune=generic --enable-mingw-wildcard --enable-languages=c,lto,c++,fortran,ada,objc,obj-c++,jit --enable-shared --enable-static --enable-libatomic --enable-threads=posix --enable-graphite --enable-fully-dynamic-string --enable-libstdcxx-backtrace=yes --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --disable-libstdcxx-pch --enable-lto --enable-libgomp --disable-libssp --disable-multilib --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-libiconv --with-system-zlib --with-gmp=/mingw64 --with-mpfr=/mingw64 --with-mpc=/mingw64 --with-isl=/mingw64 --with-pkgversion='Rev6, Built by MSYS2 project' --with-bugurl=https://github.com/msys2/MINGW-packages/issues --with-gnu-as --with-gnu-ld --disable-libstdcxx-debug --enable-plugin --with-boot-ldflags=-static-libstdc++ --with-stage1-ldflags=-static-libstdc++
Thread model: posix
Supported LTO compression algorithms: zlib zstd
gcc version 15.1.0 (Rev6, Built by MSYS2 project) 
configure:7433: $? = 0
configure:7422: g++ -V >&5
g++.exe: error: unrecognized command-line option '-V'
g++.exe: fatal error: no input files
compilation terminated.
configure:7433: $? = 1
configure:7422: g++ -qversion >&5
g++.exe: error: unrecognized command-line option '-qversion'; did you mean '--version'?
g++.exe: fatal error: no input files
compilation terminated.
configure:7433: $? = 1
configure:7437: checking whether the compiler supports GNU C++
configure:7467: result: yes
configure:7478: checking whether g++ accepts -g
configure:7499: gcc.exe -c -O0 -ffunction-sections -fdata-sections -m64  conftest.cpp >&5
configure:7499: $? = 0
configure:7543: result: yes
configure:7563: checking for g++ option to enable C++11 features
configure:7596: result: none needed
configure:7662: checking dependency style of g++
configure:7774: result: gcc3
configure:7794: checking how to run the C++ preprocessor
configure:7816: g++ -E  conftest.cpp
configure:7816: $? = 0
configure:7831: g++ -E  conftest.cpp
conftest.cpp:11:10: fatal error: ac_nonexistent.h: No such file or directory
   11 | #include <ac_nonexistent.h>
      |          ^~~~~~~~~~~~~~~~~~
compilation terminated.
configure:7831: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "Protocol Buffers"
| #define PACKAGE_TARNAME "protobuf"
| #define PACKAGE_VERSION "3.21.5"
| #define PACKAGE_STRING "Protocol Buffers 3.21.5"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define PACKAGE "protobuf"
| #define VERSION "3.21.5"
| /* end confdefs.h.  */
| #include <ac_nonexistent.h>
configure:7858: result: g++ -E
configure:7872: g++ -E  conftest.cpp
configure:7872: $? = 0
configure:7887: g++ -E  conftest.cpp
conftest.cpp:11:10: fatal error: ac_nonexistent.h: No such file or directory
   11 | #include <ac_nonexistent.h>
      |          ^~~~~~~~~~~~~~~~~~
compilation terminated.
configure:7887: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "Protocol Buffers"
| #define PACKAGE_TARNAME "protobuf"
| #define PACKAGE_VERSION "3.21.5"
| #define PACKAGE_STRING "Protocol Buffers 3.21.5"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define PACKAGE "protobuf"
| #define VERSION "3.21.5"
| /* end confdefs.h.  */
| #include <ac_nonexistent.h>
configure:7934: checking for stdio.h
configure:7934: g++.exe -c -O0 -ffunction-sections -fdata-sections -m64  conftest.cpp >&5
configure:7934: $? = 0
configure:7934: result: yes
configure:7934: checking for stdlib.h
configure:7934: g++.exe -c -O0 -ffunction-sections -fdata-sections -m64  conftest.cpp >&5
configure:7934: $? = 0
configure:7934: result: yes
configure:7934: checking for string.h
configure:7934: g++.exe -c -O0 -ffunction-sections -fdata-sections -m64  conftest.cpp >&5
configure:7934: $? = 0
configure:7934: result: yes
configure:7934: checking for inttypes.h
configure:7934: g++.exe -c -O0 -ffunction-sections -fdata-sections -m64  conftest.cpp >&5
configure:7934: $? = 0
configure:7934: result: yes
configure:7934: checking for stdint.h
configure:7934: g++.exe -c -O0 -ffunction-sections -fdata-sections -m64  conftest.cpp >&5
configure:7934: $? = 0
configure:7934: result: yes
configure:7934: checking for strings.h
configure:7934: g++.exe -c -O0 -ffunction-sections -fdata-sections -m64  conftest.cpp >&5
configure:7934: $? = 0
configure:7934: result: yes
configure:7934: checking for sys/stat.h
configure:7934: g++.exe -c -O0 -ffunction-sections -fdata-sections -m64  conftest.cpp >&5
configure:7934: $? = 0
configure:7934: result: yes
configure:7934: checking for sys/types.h
configure:7934: g++.exe -c -O0 -ffunction-sections -fdata-sections -m64  conftest.cpp >&5
configure:7934: $? = 0
configure:7934: result: yes
configure:7934: checking for unistd.h
configure:7934: g++.exe -c -O0 -ffunction-sections -fdata-sections -m64  conftest.cpp >&5
configure:7934: $? = 0
configure:7934: result: yes
configure:7934: checking for wchar.h
configure:7934: g++.exe -c -O0 -ffunction-sections -fdata-sections -m64  conftest.cpp >&5
configure:7934: $? = 0
configure:7934: result: yes
configure:7934: checking for minix/config.h
configure:7934: g++.exe -c -O0 -ffunction-sections -fdata-sections -m64  conftest.cpp >&5
conftest.cpp:49:10: fatal error: minix/config.h: No such file or directory
   49 | #include <minix/config.h>
      |          ^~~~~~~~~~~~~~~~
compilation terminated.
configure:7934: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "Protocol Buffers"
| #define PACKAGE_TARNAME "protobuf"
| #define PACKAGE_VERSION "3.21.5"
| #define PACKAGE_STRING "Protocol Buffers 3.21.5"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define PACKAGE "protobuf"
| #define VERSION "3.21.5"
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_WCHAR_H 1
| /* end confdefs.h.  */
| #include <stddef.h>
| #ifdef HAVE_STDIO_H
| # include <stdio.h>
| #endif
| #ifdef HAVE_STDLIB_H
| # include <stdlib.h>
| #endif
| #ifdef HAVE_STRING_H
| # include <string.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| #include <minix/config.h>
configure:7934: result: no
configure:7965: checking whether it is safe to define __EXTENSIONS__
configure:7984: g++.exe -c -O0 -ffunction-sections -fdata-sections -m64  conftest.cpp >&5
configure:7984: $? = 0
configure:7992: result: yes
configure:7995: checking whether _XOPEN_SOURCE should be defined
configure:8017: g++.exe -c -O0 -ffunction-sections -fdata-sections -m64  conftest.cpp >&5
configure:8017: $? = 0
configure:8044: result: no
configure:8161: checking for ar
configure:8182: found /mingw64/bin/ar
configure:8193: result: ar
configure:8219: checking the archiver (ar) interface
configure:8236: gcc.exe -c -O0 -ffunction-sections -fdata-sections -m64  conftest.c >&5
configure:8236: $? = 0
configure:8239: ar cru libconftest.a conftest.o >&5
C:\msys64\mingw64\bin\ar.exe: `u' modifier ignored since `D' is the default (see `U')
configure:8242: $? = 0
configure:8270: result: ar
configure:8708: checking C++ compiler flags...
configure:8731: result: use user-supplied: -O0 -ffunction-sections -fdata-sections -m64
configure:8738: checking for g++.exe options needed to detect all undeclared functions
configure:8760: g++.exe -c -O0 -ffunction-sections -fdata-sections -m64  conftest.cpp >&5
conftest.cpp: In function 'int main()':
conftest.cpp:42:8: error: 'strchr' was not declared in this scope
   42 | (void) strchr;
      |        ^~~~~~
conftest.cpp:1:1: note: 'strchr' is defined in header '<cstring>'; this is probably fixable by adding '#include <cstring>'
    1 | /* confdefs.h */
configure:8760: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "Protocol Buffers"
| #define PACKAGE_TARNAME "protobuf"
| #define PACKAGE_VERSION "3.21.5"
| #define PACKAGE_STRING "Protocol Buffers 3.21.5"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define PACKAGE "protobuf"
| #define VERSION "3.21.5"
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_WCHAR_H 1
| #define STDC_HEADERS 1
| #define _ALL_SOURCE 1
| #define _DARWIN_C_SOURCE 1
| #define _GNU_SOURCE 1
| #define _HPUX_ALT_XOPEN_SOCKET_API 1
| #define _NETBSD_SOURCE 1
| #define _OPENBSD_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define __STDC_WANT_IEC_60559_ATTRIBS_EXT__ 1
| #define __STDC_WANT_IEC_60559_BFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_DFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_FUNCS_EXT__ 1
| #define __STDC_WANT_IEC_60559_TYPES_EXT__ 1
| #define __STDC_WANT_LIB_EXT2__ 1
| #define __STDC_WANT_MATH_SPEC_FUNCS__ 1
| #define _TANDEM_SOURCE 1
| #define __EXTENSIONS__ 1
| /* end confdefs.h.  */
| 
| int
| main (void)
| {
| (void) strchr;
|   ;
|   return 0;
| }
configure:8787: g++.exe -c -O0 -ffunction-sections -fdata-sections -m64  conftest.cpp >&5
configure:8787: $? = 0
configure:8804: result: none needed
configure:8826: checking whether __SUNPRO_CC is declared
configure:8826: g++.exe -c -O0 -ffunction-sections -fdata-sections -m64   conftest.cpp >&5
conftest.cpp: In function 'int main()':
conftest.cpp:71:10: error: '__SUNPRO_CC' was not declared in this scope
   71 |   (void) __SUNPRO_CC;
      |          ^~~~~~~~~~~
configure:8826: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "Protocol Buffers"
| #define PACKAGE_TARNAME "protobuf"
| #define PACKAGE_VERSION "3.21.5"
| #define PACKAGE_STRING "Protocol Buffers 3.21.5"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define PACKAGE "protobuf"
| #define VERSION "3.21.5"
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_WCHAR_H 1
| #define STDC_HEADERS 1
| #define _ALL_SOURCE 1
| #define _DARWIN_C_SOURCE 1
| #define _GNU_SOURCE 1
| #define _HPUX_ALT_XOPEN_SOCKET_API 1
| #define _NETBSD_SOURCE 1
| #define _OPENBSD_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define __STDC_WANT_IEC_60559_ATTRIBS_EXT__ 1
| #define __STDC_WANT_IEC_60559_BFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_DFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_FUNCS_EXT__ 1
| #define __STDC_WANT_IEC_60559_TYPES_EXT__ 1
| #define __STDC_WANT_LIB_EXT2__ 1
| #define __STDC_WANT_MATH_SPEC_FUNCS__ 1
| #define _TANDEM_SOURCE 1
| #define __EXTENSIONS__ 1
| /* end confdefs.h.  */
| #include <stddef.h>
| #ifdef HAVE_STDIO_H
| # include <stdio.h>
| #endif
| #ifdef HAVE_STDLIB_H
| # include <stdlib.h>
| #endif
| #ifdef HAVE_STRING_H
| # include <string.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| int
| main (void)
| {
| #ifndef __SUNPRO_CC
| #ifdef __cplusplus
|   (void) __SUNPRO_CC;
| #else
|   (void) __SUNPRO_CC;
| #endif
| #endif
| 
|   ;
|   return 0;
| }
configure:8826: result: no
configure:9009: checking how to print strings
configure:9036: result: printf
configure:9057: checking for a sed that does not truncate output
configure:9127: result: /usr/bin/sed
configure:9145: checking for grep that handles long lines and -e
configure:9209: result: /usr/bin/grep
configure:9214: checking for egrep
configure:9282: result: /usr/bin/grep -E
configure:9287: checking for fgrep
configure:9355: result: /usr/bin/grep -F
configure:9391: checking for ld used by gcc.exe
configure:9459: result: C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe
configure:9466: checking if the linker (C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe) is GNU ld
configure:9482: result: yes
configure:9494: checking for BSD- or MS-compatible name lister (nm)
configure:9549: result: /mingw64/bin/nm -B
configure:9689: checking the name lister (/mingw64/bin/nm -B) interface
configure:9697: g++.exe -c -O0 -ffunction-sections -fdata-sections -m64  conftest.cpp >&5
configure:9700: /mingw64/bin/nm -B "conftest.o"
configure:9703: output
0000000000000000 b .bss
0000000000000000 d .data
0000000000000000 d .data$some_variable
0000000000000000 r .rdata$zzz
0000000000000000 t .text
0000000000000000 D some_variable
configure:9710: result: BSD nm
configure:9713: checking whether ln -s works
configure:9720: result: no, using cp -pR
configure:9725: checking the maximum length of command line arguments
configure:9857: result: 8192
configure:9905: checking how to convert x86_64-pc-mingw64 file names to x86_64-pc-mingw64 format
configure:9946: result: func_convert_file_msys_to_w32
configure:9953: checking how to convert x86_64-pc-mingw64 file names to toolchain format
configure:9974: result: func_convert_file_msys_to_w32
configure:9981: checking for C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe option to reload object files
configure:9989: result: -r
configure:10068: checking for file
configure:10089: found /usr/bin/file
configure:10100: result: file
configure:10176: checking for objdump
configure:10197: found /mingw64/bin/objdump
configure:10208: result: objdump
configure:10240: checking how to recognize dependent libraries
configure:10441: result: file_magic ^x86 archive import|^x86 DLL
configure:10531: checking for dlltool
configure:10552: found /mingw64/bin/dlltool
configure:10563: result: dlltool
configure:10596: checking how to associate runtime and link libraries
configure:10624: result: func_cygming_dll_for_implib
configure:10774: checking for archiver @FILE support
configure:10792: g++.exe -c -O0 -ffunction-sections -fdata-sections -m64  conftest.cpp >&5
configure:10792: $? = 0
configure:10796: ar cr libconftest.a @conftest.lst >&5
configure:10799: $? = 0
configure:10804: ar cr libconftest.a @conftest.lst >&5
C:\msys64\mingw64\bin\ar.exe: conftest.o: No such file or directory
configure:10807: $? = 1
configure:10819: result: @
configure:10882: checking for strip
configure:10903: found /mingw64/bin/strip
configure:10914: result: strip
configure:10991: checking for ranlib
configure:11012: found /mingw64/bin/ranlib
configure:11023: result: ranlib
configure:11125: checking command to parse /mingw64/bin/nm -B output from gcc.exe object
configure:11279: g++.exe -c -O0 -ffunction-sections -fdata-sections -m64  conftest.cpp >&5
configure:11282: $? = 0
configure:11286: /mingw64/bin/nm -B conftest.o \| /usr/bin/sed -n -e 's/^.*[ ]\([ABCDGIRSTW][ABCDGIRSTW]*\)[ ][ ]*\([_A-Za-z][_A-Za-z0-9]*\)\{0,1\}$/\1 \2 \2/p' | /usr/bin/sed '/ __gnu_lto/d' \> conftest.nm
configure:11289: $? = 0
configure:11355: g++.exe -o conftest.exe -O0 -ffunction-sections -fdata-sections -m64   conftest.cpp conftstm.o >&5
configure:11358: $? = 0
configure:11396: result: ok
configure:11443: checking for sysroot
configure:11474: result: no
configure:11481: checking for a working dd
configure:11525: result: /usr/bin/dd
configure:11529: checking how to truncate binary pipes
configure:11545: result: /usr/bin/dd bs=4096 count=1
configure:11882: checking for mt
configure:11917: result: no
configure:11937: checking if : is a manifest tool
configure:11944: : '-?'
configure:11952: result: no
configure:12673: checking for dlfcn.h
configure:12673: g++.exe -c -O0 -ffunction-sections -fdata-sections -m64  conftest.cpp >&5
conftest.cpp:67:10: fatal error: dlfcn.h: No such file or directory
   67 | #include <dlfcn.h>
      |          ^~~~~~~~~
compilation terminated.
configure:12673: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "Protocol Buffers"
| #define PACKAGE_TARNAME "protobuf"
| #define PACKAGE_VERSION "3.21.5"
| #define PACKAGE_STRING "Protocol Buffers 3.21.5"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define PACKAGE "protobuf"
| #define VERSION "3.21.5"
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_WCHAR_H 1
| #define STDC_HEADERS 1
| #define _ALL_SOURCE 1
| #define _DARWIN_C_SOURCE 1
| #define _GNU_SOURCE 1
| #define _HPUX_ALT_XOPEN_SOCKET_API 1
| #define _NETBSD_SOURCE 1
| #define _OPENBSD_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define __STDC_WANT_IEC_60559_ATTRIBS_EXT__ 1
| #define __STDC_WANT_IEC_60559_BFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_DFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_FUNCS_EXT__ 1
| #define __STDC_WANT_IEC_60559_TYPES_EXT__ 1
| #define __STDC_WANT_LIB_EXT2__ 1
| #define __STDC_WANT_MATH_SPEC_FUNCS__ 1
| #define _TANDEM_SOURCE 1
| #define __EXTENSIONS__ 1
| /* end confdefs.h.  */
| #include <stddef.h>
| #ifdef HAVE_STDIO_H
| # include <stdio.h>
| #endif
| #ifdef HAVE_STDLIB_H
| # include <stdlib.h>
| #endif
| #ifdef HAVE_STRING_H
| # include <string.h>
| #endif
| #ifdef HAVE_INTTYPES_H
| # include <inttypes.h>
| #endif
| #ifdef HAVE_STDINT_H
| # include <stdint.h>
| #endif
| #ifdef HAVE_STRINGS_H
| # include <strings.h>
| #endif
| #ifdef HAVE_SYS_TYPES_H
| # include <sys/types.h>
| #endif
| #ifdef HAVE_SYS_STAT_H
| # include <sys/stat.h>
| #endif
| #ifdef HAVE_UNISTD_H
| # include <unistd.h>
| #endif
| 
| #include <dlfcn.h>
configure:12673: result: no
configure:12942: checking for objdir
configure:12958: result: .libs
configure:13222: checking if gcc.exe supports -fno-rtti -fno-exceptions
configure:13241: gcc.exe -c -O0 -ffunction-sections -fdata-sections -m64  -fno-rtti -fno-exceptions conftest.c >&5
cc1.exe: warning: command-line option '-fno-rtti' is valid for C++/D/ObjC++ but not for C
configure:13245: $? = 0
configure:13258: result: no
configure:13616: checking for gcc.exe option to produce PIC
configure:13624: result: -DDLL_EXPORT -DPIC
configure:13632: checking if gcc.exe PIC flag -DDLL_EXPORT -DPIC works
configure:13651: gcc.exe -c -O0 -ffunction-sections -fdata-sections -m64  -DDLL_EXPORT -DPIC -DPIC conftest.c >&5
configure:13655: $? = 0
configure:13668: result: yes
configure:13697: checking if gcc.exe static flag -static works
configure:13726: result: yes
configure:13741: checking if gcc.exe supports -c -o file.o
configure:13763: gcc.exe -c -O0 -ffunction-sections -fdata-sections -m64  -o out/conftest2.o conftest.c >&5
configure:13767: $? = 0
configure:13789: result: yes
configure:13797: checking if gcc.exe supports -c -o file.o
configure:13845: result: yes
configure:13878: checking whether the gcc.exe linker (C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe) supports shared libraries
configure:15146: result: yes
configure:15387: checking dynamic linker characteristics
configure:16208: result: Win32 ld.exe
configure:16330: checking how to hardcode library paths into programs
configure:16355: result: immediate
configure:16907: checking whether stripping libraries is possible
configure:16916: result: yes
configure:16958: checking if libtool supports shared libraries
configure:16960: result: yes
configure:16963: checking whether to build shared libraries
configure:16988: result: no
configure:16991: checking whether to build static libraries
configure:16995: result: yes
configure:17018: checking how to run the C++ preprocessor
configure:17082: result: g++.exe -E
configure:17096: g++.exe -E  conftest.cpp
configure:17096: $? = 0
configure:17111: g++.exe -E  conftest.cpp
conftest.cpp:39:10: fatal error: ac_nonexistent.h: No such file or directory
   39 | #include <ac_nonexistent.h>
      |          ^~~~~~~~~~~~~~~~~~
compilation terminated.
configure:17111: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "Protocol Buffers"
| #define PACKAGE_TARNAME "protobuf"
| #define PACKAGE_VERSION "3.21.5"
| #define PACKAGE_STRING "Protocol Buffers 3.21.5"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define PACKAGE "protobuf"
| #define VERSION "3.21.5"
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_WCHAR_H 1
| #define STDC_HEADERS 1
| #define _ALL_SOURCE 1
| #define _DARWIN_C_SOURCE 1
| #define _GNU_SOURCE 1
| #define _HPUX_ALT_XOPEN_SOCKET_API 1
| #define _NETBSD_SOURCE 1
| #define _OPENBSD_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define __STDC_WANT_IEC_60559_ATTRIBS_EXT__ 1
| #define __STDC_WANT_IEC_60559_BFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_DFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_FUNCS_EXT__ 1
| #define __STDC_WANT_IEC_60559_TYPES_EXT__ 1
| #define __STDC_WANT_LIB_EXT2__ 1
| #define __STDC_WANT_MATH_SPEC_FUNCS__ 1
| #define _TANDEM_SOURCE 1
| #define __EXTENSIONS__ 1
| #define LT_OBJDIR ".libs/"
| /* end confdefs.h.  */
| #include <ac_nonexistent.h>
configure:17276: checking for ld used by g++.exe
configure:17344: result: C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe
configure:17351: checking if the linker (C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe) is GNU ld
configure:17367: result: yes
configure:17422: checking whether the g++.exe linker (C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe) supports shared libraries
configure:18500: result: yes
configure:18536: g++.exe -c -O0 -ffunction-sections -fdata-sections -m64  conftest.cpp >&5
configure:18539: $? = 0
configure:19020: checking for g++.exe option to produce PIC
configure:19028: result: -DDLL_EXPORT -DPIC
configure:19036: checking if g++.exe PIC flag -DDLL_EXPORT -DPIC works
configure:19055: g++.exe -c -O0 -ffunction-sections -fdata-sections -m64  -DDLL_EXPORT -DPIC -DPIC conftest.cpp >&5
configure:19059: $? = 0
configure:19072: result: yes
configure:19095: checking if g++.exe static flag -static works
configure:19124: result: yes
configure:19136: checking if g++.exe supports -c -o file.o
configure:19158: g++.exe -c -O0 -ffunction-sections -fdata-sections -m64  -o out/conftest2.o conftest.cpp >&5
configure:19162: $? = 0
configure:19184: result: yes
configure:19189: checking if g++.exe supports -c -o file.o
configure:19237: result: yes
configure:19267: checking whether the g++.exe linker (C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe) supports shared libraries
configure:19307: result: yes
configure:19449: checking dynamic linker characteristics
configure:20197: result: Win32 ld.exe
configure:20262: checking how to hardcode library paths into programs
configure:20287: result: immediate
configure:20350: checking whether the linker supports version scripts
configure:20366: g++.exe -o conftest.exe -O0 -ffunction-sections -fdata-sections -m64   -Wl,--version-script=conftest.map conftest.cpp  >&5
configure:20366: $? = 0
configure:20368: result: yes
configure:20390: checking for egrep
configure:20458: result: /usr/bin/grep -E
configure:20464: checking for fcntl.h
configure:20464: g++.exe -c -O0 -ffunction-sections -fdata-sections -m64  conftest.cpp >&5
configure:20464: $? = 0
configure:20464: result: yes
configure:20470: checking for inttypes.h
configure:20470: result: yes
configure:20476: checking for limits.h
configure:20476: g++.exe -c -O0 -ffunction-sections -fdata-sections -m64  conftest.cpp >&5
configure:20476: $? = 0
configure:20476: result: yes
configure:20482: checking for stdlib.h
configure:20482: result: yes
configure:20488: checking for unistd.h
configure:20488: result: yes
configure:20497: checking for working memcmp
configure:20542: g++.exe -o conftest.exe -O0 -ffunction-sections -fdata-sections -m64   conftest.cpp  >&5
configure:20542: $? = 0
configure:20542: ./conftest.exe
configure:20542: $? = 0
configure:20553: result: yes
configure:20562: checking for working strtod
configure:20605: g++.exe -o conftest.exe -O0 -ffunction-sections -fdata-sections -m64   conftest.cpp  >&5
conftest.cpp: In function 'int main()':
conftest.cpp:81:20: warning: ISO C++ forbids converting a string constant to 'char*' [-Wwrite-strings]
   81 |     char *string = " +69";
      |                    ^~~~~~
conftest.cpp:92:20: warning: ISO C++ forbids converting a string constant to 'char*' [-Wwrite-strings]
   92 |     char *string = "NaN";
      |                    ^~~~~
configure:20605: $? = 0
configure:20605: ./conftest.exe
configure:20605: $? = 0
configure:20616: result: yes
configure:20678: checking for ftruncate
configure:20678: g++.exe -o conftest.exe -O0 -ffunction-sections -fdata-sections -m64   conftest.cpp  >&5
configure:20678: $? = 0
configure:20678: result: yes
configure:20684: checking for memset
configure:20684: g++.exe -o conftest.exe -O0 -ffunction-sections -fdata-sections -m64   conftest.cpp  >&5
conftest.cpp:61:6: warning: declaration of 'char memset()' conflicts with built-in declaration 'void* memset(void*, int, long long unsigned int)' [-Wbuiltin-declaration-mismatch]
   61 | char memset ();
      |      ^~~~~~
configure:20684: $? = 0
configure:20684: result: yes
configure:20690: checking for mkdir
configure:20690: g++.exe -o conftest.exe -O0 -ffunction-sections -fdata-sections -m64   conftest.cpp  >&5
configure:20690: $? = 0
configure:20690: result: yes
configure:20696: checking for strchr
configure:20696: g++.exe -o conftest.exe -O0 -ffunction-sections -fdata-sections -m64   conftest.cpp  >&5
conftest.cpp:63:6: warning: declaration of 'char strchr()' conflicts with built-in declaration 'char* strchr(const char*, int)' [-Wbuiltin-declaration-mismatch]
   63 | char strchr ();
      |      ^~~~~~
configure:20696: $? = 0
configure:20696: result: yes
configure:20702: checking for strerror
configure:20702: g++.exe -o conftest.exe -O0 -ffunction-sections -fdata-sections -m64   conftest.cpp  >&5
configure:20702: $? = 0
configure:20702: result: yes
configure:20708: checking for strtol
configure:20708: g++.exe -o conftest.exe -O0 -ffunction-sections -fdata-sections -m64   conftest.cpp  >&5
configure:20708: $? = 0
configure:20708: result: yes
configure:20721: checking zlib version
configure:20741: g++.exe -c -O0 -ffunction-sections -fdata-sections -m64  conftest.cpp >&5
configure:20741: $? = 0
configure:20744: result: ok (1.2.0.4 or later)
configure:20748: checking for library containing zlibVersion
configure:20777: g++.exe -o conftest.exe -O0 -ffunction-sections -fdata-sections -m64   conftest.cpp  >&5
C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe: D:\personal\temp\ccEDH6rz.o:conftest.cpp:(.text$main+0xe): undefined reference to `zlibVersion'
collect2.exe: error: ld returned 1 exit status
configure:20777: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "Protocol Buffers"
| #define PACKAGE_TARNAME "protobuf"
| #define PACKAGE_VERSION "3.21.5"
| #define PACKAGE_STRING "Protocol Buffers 3.21.5"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define PACKAGE "protobuf"
| #define VERSION "3.21.5"
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_WCHAR_H 1
| #define STDC_HEADERS 1
| #define _ALL_SOURCE 1
| #define _DARWIN_C_SOURCE 1
| #define _GNU_SOURCE 1
| #define _HPUX_ALT_XOPEN_SOCKET_API 1
| #define _NETBSD_SOURCE 1
| #define _OPENBSD_SOURCE 1
| #define _POSIX_PTHREAD_SEMANTICS 1
| #define __STDC_WANT_IEC_60559_ATTRIBS_EXT__ 1
| #define __STDC_WANT_IEC_60559_BFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_DFP_EXT__ 1
| #define __STDC_WANT_IEC_60559_FUNCS_EXT__ 1
| #define __STDC_WANT_IEC_60559_TYPES_EXT__ 1
| #define __STDC_WANT_LIB_EXT2__ 1
| #define __STDC_WANT_MATH_SPEC_FUNCS__ 1
| #define _TANDEM_SOURCE 1
| #define __EXTENSIONS__ 1
| #define LT_OBJDIR ".libs/"
| #define HAVE_FCNTL_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_LIMITS_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_FTRUNCATE 1
| #define HAVE_MEMSET 1
| #define HAVE_MKDIR 1
| #define HAVE_STRCHR 1
| #define HAVE_STRERROR 1
| #define HAVE_STRTOL 1
| /* end confdefs.h.  */
| 
| namespace conftest {
|   extern "C" int zlibVersion ();
| }
| int
| main (void)
| {
| return conftest::zlibVersion ();
|   ;
|   return 0;
| }
configure:20777: g++.exe -o conftest.exe -O0 -ffunction-sections -fdata-sections -m64   conftest.cpp -lz   >&5
configure:20777: $? = 0
configure:20797: result: -lz
configure:20863: checking whether g++.exe supports C++11 features by default
configure:21177: g++.exe -c -O0 -ffunction-sections -fdata-sections -m64  conftest.cpp >&5
configure:21177: $? = 0
configure:21185: result: yes
configure:21564: checking whether -latomic is needed
configure:21577: g++.exe -o conftest.exe -O0 -ffunction-sections -fdata-sections -m64   conftest.cpp -lz  >&5
configure:21577: $? = 0
configure:21585: result: no
configure:21784: checking whether gcc.exe is Clang
configure:21811: result: no
configure:21876: checking whether pthreads work with "-pthread" and "-lpthread"
configure:21987: gcc.exe -o conftest.exe -O0 -ffunction-sections -fdata-sections -m64 -pthread   conftest.c -lpthread -lz  >&5
configure:21987: $? = 0
configure:21997: result: yes
configure:22120: checking for joinable pthread attribute
configure:22139: gcc.exe -o conftest.exe -O0 -ffunction-sections -fdata-sections -m64 -pthread   conftest.c -lpthread -lz  >&5
configure:22139: $? = 0
configure:22148: result: PTHREAD_CREATE_JOINABLE
configure:22161: checking whether more special flags are required for pthreads
configure:22175: result: no
configure:22184: checking for PTHREAD_PRIO_INHERIT
configure:22202: gcc.exe -o conftest.exe -O0 -ffunction-sections -fdata-sections -m64 -pthread   conftest.c -lpthread -lz  >&5
configure:22202: $? = 0
configure:22212: result: yes
configure:22331: checking the location of hash_map
configure:22357: g++.exe -c -O0 -ffunction-sections -fdata-sections -m64  conftest.cpp >&5
configure:22357: $? = 0
configure:22380: g++.exe -c -O0 -ffunction-sections -fdata-sections -m64  conftest.cpp >&5
configure:22380: $? = 0
configure:22439: result: <unordered_map>
configure:22465: checking whether -llog is needed
configure:22473: result: no
configure:22597: checking that generated files are newer than configure
configure:22603: result: done
configure:22670: creating ./config.status

## ---------------------- ##
## Running config.status. ##
## ---------------------- ##

This file was extended by Protocol Buffers config.status 3.21.5, which was
generated by GNU Autoconf 2.71.  Invocation command line was

  CONFIG_FILES    = 
  CONFIG_HEADERS  = 
  CONFIG_LINKS    = 
  CONFIG_COMMANDS = 
  $ ./config.status 

on GXLF-20250516AG

config.status:1259: creating Makefile
config.status:1259: creating src/Makefile
config.status:1259: creating benchmarks/Makefile
config.status:1259: creating conformance/Makefile
config.status:1259: creating protobuf.pc
config.status:1259: creating protobuf-lite.pc
config.status:1259: creating config.h
config.status:1473: executing depfiles commands
config.status:1550: cd src       && sed -e '/# am--include-marker/d' Makefile         | make -f - am--depfiles
config.status:1555: $? = 0
config.status:1550: cd benchmarks       && sed -e '/# am--include-marker/d' Makefile         | make -f - am--depfiles
config.status:1555: $? = 0
config.status:1550: cd conformance       && sed -e '/# am--include-marker/d' Makefile         | make -f - am--depfiles
config.status:1555: $? = 0
config.status:1473: executing libtool commands
configure:25156: === configuring in third_party/googletest (/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/build/third_party/googletest)
configure:25217: running /bin/sh /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/third_party/googletest/configure --disable-option-checking '--prefix=/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install'  '--disable-shared' '--enable-static' '--disable-maintainer-mode' 'CC=gcc.exe' 'CFLAGS=-O0 -ffunction-sections -fdata-sections -m64' 'CXX=g++.exe' 'CXXFLAGS=-O0 -ffunction-sections -fdata-sections -m64' --cache-file=/dev/null --srcdir=/c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/third_party/googletest

## ---------------- ##
## Cache variables. ##
## ---------------- ##

ac_cv_build_prog_cc_g=yes
ac_cv_build_prog_CPP='gcc -E'
ac_cv_build_prog_cxx_g=yes
ac_cv_build_prog_CXXCPP='g++ -E'
ac_cv_build=x86_64-pc-mingw64
ac_cv_c_compiler_gnu=yes
ac_cv_cxx_compiler_gnu=yes
ac_cv_cxx_hash_map_class=unordered_map
ac_cv_cxx_hash_map='<unordered_map>'
ac_cv_cxx_hash_namespace=std
ac_cv_cxx_hash_set_class=unordered_set
ac_cv_cxx_hash_set='<unordered_set>'
ac_cv_cxx_undeclared_builtin_options='none needed'
ac_cv_env_build_alias_set=
ac_cv_env_build_alias_value=
ac_cv_env_CC_set=set
ac_cv_env_CC_value=gcc.exe
ac_cv_env_CCC_set=
ac_cv_env_CCC_value=
ac_cv_env_CFLAGS_set=set
ac_cv_env_CFLAGS_value='-O0 -ffunction-sections -fdata-sections -m64'
ac_cv_env_CPP_set=
ac_cv_env_CPP_value=
ac_cv_env_CPPFLAGS_set=
ac_cv_env_CPPFLAGS_value=
ac_cv_env_CXX_set=set
ac_cv_env_CXX_value=g++.exe
ac_cv_env_CXXCPP_set=
ac_cv_env_CXXCPP_value=
ac_cv_env_CXXFLAGS_set=set
ac_cv_env_CXXFLAGS_value='-O0 -ffunction-sections -fdata-sections -m64'
ac_cv_env_DIST_LANG_set=
ac_cv_env_DIST_LANG_value=
ac_cv_env_host_alias_set=
ac_cv_env_host_alias_value=
ac_cv_env_LDFLAGS_set=
ac_cv_env_LDFLAGS_value=
ac_cv_env_LIBS_set=
ac_cv_env_LIBS_value=
ac_cv_env_LT_SYS_LIBRARY_PATH_set=
ac_cv_env_LT_SYS_LIBRARY_PATH_value=
ac_cv_env_OBJC_set=
ac_cv_env_OBJC_value=
ac_cv_env_OBJCFLAGS_set=
ac_cv_env_OBJCFLAGS_value=
ac_cv_env_target_alias_set=
ac_cv_env_target_alias_value=
ac_cv_exeext=.exe
ac_cv_func_ftruncate=yes
ac_cv_func_memcmp_working=yes
ac_cv_func_memset=yes
ac_cv_func_mkdir=yes
ac_cv_func_strchr=yes
ac_cv_func_strerror=yes
ac_cv_func_strtod=yes
ac_cv_func_strtol=yes
ac_cv_have_decl___SUNPRO_CC=no
ac_cv_header_dlfcn_h=no
ac_cv_header_fcntl_h=yes
ac_cv_header_inttypes_h=yes
ac_cv_header_limits_h=yes
ac_cv_header_minix_config_h=no
ac_cv_header_stdint_h=yes
ac_cv_header_stdio_h=yes
ac_cv_header_stdlib_h=yes
ac_cv_header_string_h=yes
ac_cv_header_strings_h=yes
ac_cv_header_sys_stat_h=yes
ac_cv_header_sys_types_h=yes
ac_cv_header_unistd_h=yes
ac_cv_header_wchar_h=yes
ac_cv_host=x86_64-pc-mingw64
ac_cv_objext=o
ac_cv_path_EGREP='/usr/bin/grep -E'
ac_cv_path_FGREP='/usr/bin/grep -F'
ac_cv_path_GREP=/usr/bin/grep
ac_cv_path_install='/usr/bin/install -c'
ac_cv_path_lt_DD=/usr/bin/dd
ac_cv_path_mkdir=/usr/bin/mkdir
ac_cv_path_SED=/usr/bin/sed
ac_cv_prog_ac_ct_AR=ar
ac_cv_prog_ac_ct_CC_FOR_BUILD=gcc
ac_cv_prog_ac_ct_CC=gcc.exe
ac_cv_prog_ac_ct_CXX_FOR_BUILD=g++
ac_cv_prog_ac_ct_DLLTOOL=dlltool
ac_cv_prog_ac_ct_FILECMD=file
ac_cv_prog_ac_ct_OBJDUMP=objdump
ac_cv_prog_ac_ct_RANLIB=ranlib
ac_cv_prog_ac_ct_STRIP=strip
ac_cv_prog_AWK=gawk
ac_cv_prog_cc_c11=
ac_cv_prog_cc_g=yes
ac_cv_prog_cc_stdc=
ac_cv_prog_CPP='gcc.exe -E'
ac_cv_prog_cxx_11=no
ac_cv_prog_cxx_cxx11=
ac_cv_prog_cxx_g=yes
ac_cv_prog_cxx_stdcxx=
ac_cv_prog_CXXCPP='g++.exe -E'
ac_cv_prog_make_make_set=yes
ac_cv_safe_to_define___extensions__=yes
ac_cv_search_zlibVersion=-lz
ac_cv_should_define__xopen_source=no
ac_cv_target=x86_64-pc-mingw64
am_cv_ar_interface=ar
am_cv_CC_dependencies_compiler_type=gcc3
am_cv_CXX_dependencies_compiler_type=gcc3
am_cv_make_support_nested_variables=yes
am_cv_prog_cc_c_o=yes
am_cv_prog_tar_ustar=gnutar
ax_cv_cxx_compile_cxx11=yes
ax_cv_PTHREAD_CLANG=no
ax_cv_PTHREAD_JOINABLE_ATTR=PTHREAD_CREATE_JOINABLE
ax_cv_PTHREAD_PRIO_INHERIT=yes
ax_cv_PTHREAD_SPECIAL_FLAGS=no
lt_cv_ar_at_file=@
lt_cv_deplibs_check_method='file_magic ^x86 archive import|^x86 DLL'
lt_cv_file_magic_cmd=func_win32_libid
lt_cv_file_magic_test_file=
lt_cv_ld_reload_flag=-r
lt_cv_nm_interface='BSD nm'
lt_cv_objdir=.libs
lt_cv_path_LD=C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe
lt_cv_path_LDCXX=C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe
lt_cv_path_mainfest_tool=no
lt_cv_path_NM='/mingw64/bin/nm -B'
lt_cv_prog_compiler_c_o_CXX=yes
lt_cv_prog_compiler_c_o=yes
lt_cv_prog_compiler_pic_CXX='-DDLL_EXPORT -DPIC'
lt_cv_prog_compiler_pic_works_CXX=yes
lt_cv_prog_compiler_pic_works=yes
lt_cv_prog_compiler_pic='-DDLL_EXPORT -DPIC'
lt_cv_prog_compiler_rtti_exceptions=no
lt_cv_prog_compiler_static_works_CXX=yes
lt_cv_prog_compiler_static_works=yes
lt_cv_prog_gnu_ld=yes
lt_cv_prog_gnu_ldcxx=yes
lt_cv_sharedlib_from_linklib_cmd=func_cygming_dll_for_implib
lt_cv_sys_global_symbol_pipe='/usr/bin/sed -n -e '\''s/^.*[	 ]\([ABCDGIRSTW][ABCDGIRSTW]*\)[	 ][	 ]*\([_A-Za-z][_A-Za-z0-9]*\)
\{0,1\}$/\1 \2 \2/p'\'' | /usr/bin/sed '\''/ __gnu_lto/d'\'''
lt_cv_sys_global_symbol_to_c_name_address_lib_prefix='/usr/bin/sed -n -e '\''s/^: \(.*\) .*$/  {"\1", (void *) 0},/p'\'' -e '\''s/^[ABCDGIRSTW][ABCDGIRSTW]* .* \(lib.*\)$/  {"\1", (void *) \&\1},/p'\'' -e '\''s/^[ABCDGIRSTW][ABCDGIRSTW]* .* \(.*\)$/  {"lib\1", (void *) \&\1},/p'\'''
lt_cv_sys_global_symbol_to_c_name_address='/usr/bin/sed -n -e '\''s/^: \(.*\) .*$/  {"\1", (void *) 0},/p'\'' -e '\''s/^[ABCDGIRSTW][ABCDGIRSTW]* .* \(.*\)$/  {"\1", (void *) \&\1},/p'\'''
lt_cv_sys_global_symbol_to_cdecl='/usr/bin/sed -n -e '\''s/^T .* \(.*\)$/extern int \1();/p'\'' -e '\''s/^[ABCDGIRSTW][ABCDGIRSTW]* .* \(.*\)$/extern char \1;/p'\'''
lt_cv_sys_global_symbol_to_import=
lt_cv_sys_max_cmd_len=8192
lt_cv_to_host_file_cmd=func_convert_file_msys_to_w32
lt_cv_to_tool_file_cmd=func_convert_file_msys_to_w32
lt_cv_truncate_bin='/usr/bin/dd bs=4096 count=1'

## ----------------- ##
## Output variables. ##
## ----------------- ##

ac_ct_AR='ar'
ac_ct_CC_FOR_BUILD='gcc'
ac_ct_CC='gcc.exe'
ac_ct_CXX_FOR_BUILD='g++'
ac_ct_CXX=''
ac_ct_DUMPBIN=''
ac_ct_OBJC=''
ACLOCAL='${SHELL} '\''/c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/missing'\'' aclocal-1.16'
am__EXEEXT_FALSE='#'
am__EXEEXT_TRUE=''
am__fastdepCC_FALSE='#'
am__fastdepCC_TRUE=''
am__fastdepCXX_FALSE='#'
am__fastdepCXX_TRUE=''
am__fastdepOBJC_FALSE=''
am__fastdepOBJC_TRUE='#'
am__include='include'
am__isrc=' -I$(srcdir)'
am__leading_dot='.'
am__nodep='_no'
am__quote=''
am__tar='tar --format=ustar -chf - "$$tardir"'
am__untar='tar -xf -'
AM_BACKSLASH='\'
AM_DEFAULT_V='$(AM_DEFAULT_VERBOSITY)'
AM_DEFAULT_VERBOSITY='0'
AM_V='$(V)'
AMDEP_FALSE='#'
AMDEP_TRUE=''
AMDEPBACKSLASH='\'
AMTAR='$${TAR-tar}'
AR='ar'
AUTOCONF='${SHELL} '\''/c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/missing'\'' autoconf'
AUTOHEADER='${SHELL} '\''/c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/missing'\'' autoheader'
AUTOMAKE='${SHELL} '\''/c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/missing'\'' automake-1.16'
AWK='gawk'
ax_pthread_config=''
bindir='${exec_prefix}/bin'
build_alias=''
build_cpu='x86_64'
BUILD_EXEEXT=''
BUILD_OBJEXT=''
build_os='mingw64'
build_vendor='pc'
build='x86_64-pc-mingw64'
CC_FOR_BUILD='gcc'
CC='gcc.exe'
CCDEPMODE='depmode=gcc3'
CFLAGS_FOR_BUILD='-g -O2'
CFLAGS='-O0 -ffunction-sections -fdata-sections -m64'
CPP_FOR_BUILD='gcc -E'
CPP='gcc.exe -E'
CPPFLAGS_FOR_BUILD=''
CPPFLAGS=''
CSCOPE='cscope'
CTAGS='ctags'
CXX_FOR_BUILD='g++'
CXX='g++.exe'
CXXCPP_FOR_BUILD='g++ -E'
CXXCPP='g++.exe -E'
CXXCPPFLAGS_FOR_BUILD=''
CXXDEPMODE='depmode=gcc3'
CXXFLAGS_FOR_BUILD='-g -O2'
CXXFLAGS='-O0 -ffunction-sections -fdata-sections -m64'
CYGPATH_W='cygpath -w'
datadir='${datarootdir}'
datarootdir='${prefix}/share'
DEFS='-DHAVE_CONFIG_H'
DEPDIR='.deps'
DIST_LANG='all'
DLLTOOL='dlltool'
docdir='${datarootdir}/doc/${PACKAGE_TARNAME}'
DSYMUTIL=''
DUMPBIN=''
dvidir='${docdir}'
ECHO_C=''
ECHO_N='-n'
ECHO_T=''
EGREP='/usr/bin/grep -E'
ETAGS='etags'
exec_prefix='${prefix}'
EXEEXT='.exe'
FGREP='/usr/bin/grep -F'
FILECMD='file'
GCC_FALSE='#'
GCC_TRUE=''
GREP='/usr/bin/grep'
HAVE_CXX11='1'
HAVE_LD_VERSION_SCRIPT_FALSE='#'
HAVE_LD_VERSION_SCRIPT_TRUE=''
HAVE_PTHREAD_FALSE='#'
HAVE_PTHREAD_TRUE=''
HAVE_ZLIB_FALSE='#'
HAVE_ZLIB_TRUE=''
host_alias=''
host_cpu='x86_64'
host_os='mingw64'
host_vendor='pc'
host='x86_64-pc-mingw64'
htmldir='${docdir}'
includedir='${prefix}/include'
infodir='${datarootdir}/info'
INSTALL_DATA='${INSTALL} -m 644'
INSTALL_PROGRAM='${INSTALL}'
INSTALL_SCRIPT='${INSTALL}'
install_sh='${SHELL} /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/install-sh'
INSTALL_STRIP_PROGRAM='$(install_sh) -c -s'
ISAINFO=''
LD='C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe'
LDFLAGS_FOR_BUILD=''
LDFLAGS=''
LIBATOMIC_LIBS=''
libdir='${exec_prefix}/lib'
libexecdir='${exec_prefix}/libexec'
LIBLOG_LIBS=''
LIBOBJS=''
LIBS='-lz '
LIBTOOL='$(SHELL) $(top_builddir)/libtool'
LIPO=''
LN_S='cp -pR'
localedir='${datarootdir}/locale'
localstatedir='${prefix}/var'
LT_SYS_LIBRARY_PATH=''
LTLIBOBJS=''
MAINT='#'
MAINTAINER_MODE_FALSE=''
MAINTAINER_MODE_TRUE='#'
MAKEINFO='${SHELL} '\''/c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/missing'\'' makeinfo'
mandir='${datarootdir}/man'
MANIFEST_TOOL=':'
mkdir_p='$(MKDIR_P)'
MKDIR_P='/usr/bin/mkdir -p'
NM='/mingw64/bin/nm -B'
NMEDIT=''
OBJC_CONFORMANCE_TEST_FALSE=''
OBJC_CONFORMANCE_TEST_TRUE='#'
OBJC=''
OBJCDEPMODE=''
OBJCFLAGS=''
OBJDUMP='objdump'
OBJEXT='o'
oldincludedir='/usr/include'
OTOOL=''
OTOOL64=''
PACKAGE_BUGREPORT='<EMAIL>'
PACKAGE_NAME='Protocol Buffers'
PACKAGE_STRING='Protocol Buffers 3.21.5'
PACKAGE_TARNAME='protobuf'
PACKAGE_URL=''
PACKAGE_VERSION='3.21.5'
PACKAGE='protobuf'
PATH_SEPARATOR=':'
pdfdir='${docdir}'
POW_LIB=''
prefix='/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install'
program_transform_name='s,x,x,'
PROTOBUF_OPT_FLAG=''
PROTOC=''
psdir='${docdir}'
PTHREAD_CC='gcc.exe'
PTHREAD_CFLAGS='-pthread'
PTHREAD_LIBS='-lpthread'
RANLIB='ranlib'
runstatedir='${localstatedir}/run'
sbindir='${exec_prefix}/sbin'
SED='/usr/bin/sed'
SET_MAKE=''
sharedstatedir='${prefix}/com'
SHELL='/bin/sh'
STRIP='strip'
subdirs=' third_party/googletest'
sysconfdir='${prefix}/etc'
target_alias=''
target_cpu='x86_64'
target_os='mingw64'
target_vendor='pc'
target='x86_64-pc-mingw64'
USE_EXTERNAL_PROTOC_FALSE=''
USE_EXTERNAL_PROTOC_TRUE='#'
VERSION='3.21.5'

## ----------- ##
## confdefs.h. ##
## ----------- ##

/* confdefs.h */
#define PACKAGE_NAME "Protocol Buffers"
#define PACKAGE_TARNAME "protobuf"
#define PACKAGE_VERSION "3.21.5"
#define PACKAGE_STRING "Protocol Buffers 3.21.5"
#define PACKAGE_BUGREPORT "<EMAIL>"
#define PACKAGE_URL ""
#define PACKAGE "protobuf"
#define VERSION "3.21.5"
#define HAVE_STDIO_H 1
#define HAVE_STDLIB_H 1
#define HAVE_STRING_H 1
#define HAVE_INTTYPES_H 1
#define HAVE_STDINT_H 1
#define HAVE_STRINGS_H 1
#define HAVE_SYS_STAT_H 1
#define HAVE_SYS_TYPES_H 1
#define HAVE_UNISTD_H 1
#define HAVE_WCHAR_H 1
#define STDC_HEADERS 1
#define _ALL_SOURCE 1
#define _DARWIN_C_SOURCE 1
#define _GNU_SOURCE 1
#define _HPUX_ALT_XOPEN_SOCKET_API 1
#define _NETBSD_SOURCE 1
#define _OPENBSD_SOURCE 1
#define _POSIX_PTHREAD_SEMANTICS 1
#define __STDC_WANT_IEC_60559_ATTRIBS_EXT__ 1
#define __STDC_WANT_IEC_60559_BFP_EXT__ 1
#define __STDC_WANT_IEC_60559_DFP_EXT__ 1
#define __STDC_WANT_IEC_60559_FUNCS_EXT__ 1
#define __STDC_WANT_IEC_60559_TYPES_EXT__ 1
#define __STDC_WANT_LIB_EXT2__ 1
#define __STDC_WANT_MATH_SPEC_FUNCS__ 1
#define _TANDEM_SOURCE 1
#define __EXTENSIONS__ 1
#define LT_OBJDIR ".libs/"
#define HAVE_FCNTL_H 1
#define HAVE_INTTYPES_H 1
#define HAVE_LIMITS_H 1
#define HAVE_STDLIB_H 1
#define HAVE_UNISTD_H 1
#define HAVE_FTRUNCATE 1
#define HAVE_MEMSET 1
#define HAVE_MKDIR 1
#define HAVE_STRCHR 1
#define HAVE_STRERROR 1
#define HAVE_STRTOL 1
#define HAVE_ZLIB 1
#define HAVE_CXX11 1
#define HAVE_PTHREAD_PRIO_INHERIT 1
#define HAVE_PTHREAD 1
#define HAVE_HASH_MAP 1
#define HAVE_HASH_SET 1
#define HASH_MAP_H <unordered_map>
#define HASH_SET_H <unordered_set>
#define HASH_NAMESPACE std
#define HASH_MAP_CLASS unordered_map
#define HASH_SET_CLASS unordered_set

configure: exit 0
