// This file is @generated by prost-build.
#[derive(<PERSON><PERSON>, PartialEq, ::prost::Message)]
pub struct SubscribeRequest {
    #[prost(map = "string, message", tag = "1")]
    pub accounts: ::std::collections::HashMap<
        ::prost::alloc::string::String,
        SubscribeRequestFilterAccounts,
    >,
    #[prost(map = "string, message", tag = "2")]
    pub slots: ::std::collections::HashMap<
        ::prost::alloc::string::String,
        SubscribeRequestFilterSlots,
    >,
    #[prost(map = "string, message", tag = "3")]
    pub transactions: ::std::collections::HashMap<
        ::prost::alloc::string::String,
        SubscribeRequestFilterTransactions,
    >,
    #[prost(map = "string, message", tag = "10")]
    pub transactions_status: ::std::collections::HashMap<
        ::prost::alloc::string::String,
        SubscribeRequestFilterTransactions,
    >,
    #[prost(map = "string, message", tag = "4")]
    pub blocks: ::std::collections::HashMap<
        ::prost::alloc::string::String,
        SubscribeRequestFilterBlocks,
    >,
    #[prost(map = "string, message", tag = "5")]
    pub blocks_meta: ::std::collections::HashMap<
        ::prost::alloc::string::String,
        SubscribeRequestFilterBlocksMeta,
    >,
    #[prost(map = "string, message", tag = "8")]
    pub entry: ::std::collections::HashMap<
        ::prost::alloc::string::String,
        SubscribeRequestFilterEntry,
    >,
    #[prost(enumeration = "CommitmentLevel", optional, tag = "6")]
    pub commitment: ::core::option::Option<i32>,
    #[prost(message, repeated, tag = "7")]
    pub accounts_data_slice: ::prost::alloc::vec::Vec<SubscribeRequestAccountsDataSlice>,
    #[prost(message, optional, tag = "9")]
    pub ping: ::core::option::Option<SubscribeRequestPing>,
    #[prost(uint64, optional, tag = "11")]
    pub from_slot: ::core::option::Option<u64>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeRequestFilterAccounts {
    #[prost(string, repeated, tag = "2")]
    pub account: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    #[prost(string, repeated, tag = "3")]
    pub owner: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    #[prost(message, repeated, tag = "4")]
    pub filters: ::prost::alloc::vec::Vec<SubscribeRequestFilterAccountsFilter>,
    #[prost(bool, optional, tag = "5")]
    pub nonempty_txn_signature: ::core::option::Option<bool>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeRequestFilterAccountsFilter {
    #[prost(
        oneof = "subscribe_request_filter_accounts_filter::Filter",
        tags = "1, 2, 3, 4"
    )]
    pub filter: ::core::option::Option<subscribe_request_filter_accounts_filter::Filter>,
}
/// Nested message and enum types in `SubscribeRequestFilterAccountsFilter`.
pub mod subscribe_request_filter_accounts_filter {
    #[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Filter {
        #[prost(message, tag = "1")]
        Memcmp(super::SubscribeRequestFilterAccountsFilterMemcmp),
        #[prost(uint64, tag = "2")]
        Datasize(u64),
        #[prost(bool, tag = "3")]
        TokenAccountState(bool),
        #[prost(message, tag = "4")]
        Lamports(super::SubscribeRequestFilterAccountsFilterLamports),
    }
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeRequestFilterAccountsFilterMemcmp {
    #[prost(uint64, tag = "1")]
    pub offset: u64,
    #[prost(
        oneof = "subscribe_request_filter_accounts_filter_memcmp::Data",
        tags = "2, 3, 4"
    )]
    pub data: ::core::option::Option<
        subscribe_request_filter_accounts_filter_memcmp::Data,
    >,
}
/// Nested message and enum types in `SubscribeRequestFilterAccountsFilterMemcmp`.
pub mod subscribe_request_filter_accounts_filter_memcmp {
    #[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Data {
        #[prost(bytes, tag = "2")]
        Bytes(::prost::alloc::vec::Vec<u8>),
        #[prost(string, tag = "3")]
        Base58(::prost::alloc::string::String),
        #[prost(string, tag = "4")]
        Base64(::prost::alloc::string::String),
    }
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct SubscribeRequestFilterAccountsFilterLamports {
    #[prost(
        oneof = "subscribe_request_filter_accounts_filter_lamports::Cmp",
        tags = "1, 2, 3, 4"
    )]
    pub cmp: ::core::option::Option<
        subscribe_request_filter_accounts_filter_lamports::Cmp,
    >,
}
/// Nested message and enum types in `SubscribeRequestFilterAccountsFilterLamports`.
pub mod subscribe_request_filter_accounts_filter_lamports {
    #[derive(Clone, Copy, PartialEq, ::prost::Oneof)]
    pub enum Cmp {
        #[prost(uint64, tag = "1")]
        Eq(u64),
        #[prost(uint64, tag = "2")]
        Ne(u64),
        #[prost(uint64, tag = "3")]
        Lt(u64),
        #[prost(uint64, tag = "4")]
        Gt(u64),
    }
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct SubscribeRequestFilterSlots {
    #[prost(bool, optional, tag = "1")]
    pub filter_by_commitment: ::core::option::Option<bool>,
    #[prost(bool, optional, tag = "2")]
    pub interslot_updates: ::core::option::Option<bool>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeRequestFilterTransactions {
    #[prost(bool, optional, tag = "1")]
    pub vote: ::core::option::Option<bool>,
    #[prost(bool, optional, tag = "2")]
    pub failed: ::core::option::Option<bool>,
    #[prost(string, optional, tag = "5")]
    pub signature: ::core::option::Option<::prost::alloc::string::String>,
    #[prost(string, repeated, tag = "3")]
    pub account_include: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    #[prost(string, repeated, tag = "4")]
    pub account_exclude: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    #[prost(string, repeated, tag = "6")]
    pub account_required: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeRequestFilterBlocks {
    #[prost(string, repeated, tag = "1")]
    pub account_include: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    #[prost(bool, optional, tag = "2")]
    pub include_transactions: ::core::option::Option<bool>,
    #[prost(bool, optional, tag = "3")]
    pub include_accounts: ::core::option::Option<bool>,
    #[prost(bool, optional, tag = "4")]
    pub include_entries: ::core::option::Option<bool>,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct SubscribeRequestFilterBlocksMeta {}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct SubscribeRequestFilterEntry {}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct SubscribeRequestAccountsDataSlice {
    #[prost(uint64, tag = "1")]
    pub offset: u64,
    #[prost(uint64, tag = "2")]
    pub length: u64,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct SubscribeRequestPing {
    #[prost(int32, tag = "1")]
    pub id: i32,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeUpdate {
    #[prost(string, repeated, tag = "1")]
    pub filters: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    #[prost(message, optional, tag = "11")]
    pub created_at: ::core::option::Option<::prost_types::Timestamp>,
    #[prost(
        oneof = "subscribe_update::UpdateOneof",
        tags = "2, 3, 4, 10, 5, 6, 9, 7, 8"
    )]
    pub update_oneof: ::core::option::Option<subscribe_update::UpdateOneof>,
}
/// Nested message and enum types in `SubscribeUpdate`.
pub mod subscribe_update {
    #[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum UpdateOneof {
        #[prost(message, tag = "2")]
        Account(super::SubscribeUpdateAccount),
        #[prost(message, tag = "3")]
        Slot(super::SubscribeUpdateSlot),
        #[prost(message, tag = "4")]
        Transaction(super::SubscribeUpdateTransaction),
        #[prost(message, tag = "10")]
        TransactionStatus(super::SubscribeUpdateTransactionStatus),
        #[prost(message, tag = "5")]
        Block(super::SubscribeUpdateBlock),
        #[prost(message, tag = "6")]
        Ping(super::SubscribeUpdatePing),
        #[prost(message, tag = "9")]
        Pong(super::SubscribeUpdatePong),
        #[prost(message, tag = "7")]
        BlockMeta(super::SubscribeUpdateBlockMeta),
        #[prost(message, tag = "8")]
        Entry(super::SubscribeUpdateEntry),
    }
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeUpdateAccount {
    #[prost(message, optional, tag = "1")]
    pub account: ::core::option::Option<SubscribeUpdateAccountInfo>,
    #[prost(uint64, tag = "2")]
    pub slot: u64,
    #[prost(bool, tag = "3")]
    pub is_startup: bool,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeUpdateAccountInfo {
    #[prost(bytes = "vec", tag = "1")]
    pub pubkey: ::prost::alloc::vec::Vec<u8>,
    #[prost(uint64, tag = "2")]
    pub lamports: u64,
    #[prost(bytes = "vec", tag = "3")]
    pub owner: ::prost::alloc::vec::Vec<u8>,
    #[prost(bool, tag = "4")]
    pub executable: bool,
    #[prost(uint64, tag = "5")]
    pub rent_epoch: u64,
    #[prost(bytes = "vec", tag = "6")]
    pub data: ::prost::alloc::vec::Vec<u8>,
    #[prost(uint64, tag = "7")]
    pub write_version: u64,
    #[prost(bytes = "vec", optional, tag = "8")]
    pub txn_signature: ::core::option::Option<::prost::alloc::vec::Vec<u8>>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeUpdateSlot {
    #[prost(uint64, tag = "1")]
    pub slot: u64,
    #[prost(uint64, optional, tag = "2")]
    pub parent: ::core::option::Option<u64>,
    #[prost(enumeration = "CommitmentLevel", tag = "3")]
    pub status: i32,
    #[prost(string, optional, tag = "4")]
    pub dead_error: ::core::option::Option<::prost::alloc::string::String>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeUpdateTransaction {
    #[prost(message, optional, tag = "1")]
    pub transaction: ::core::option::Option<SubscribeUpdateTransactionInfo>,
    #[prost(uint64, tag = "2")]
    pub slot: u64,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeUpdateTransactionInfo {
    #[prost(bytes = "vec", tag = "1")]
    pub signature: ::prost::alloc::vec::Vec<u8>,
    #[prost(bool, tag = "2")]
    pub is_vote: bool,
    #[prost(message, optional, tag = "3")]
    pub transaction: ::core::option::Option<
        super::solana::storage::confirmed_block::Transaction,
    >,
    #[prost(message, optional, tag = "4")]
    pub meta: ::core::option::Option<
        super::solana::storage::confirmed_block::TransactionStatusMeta,
    >,
    #[prost(uint64, tag = "5")]
    pub index: u64,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeUpdateTransactionStatus {
    #[prost(uint64, tag = "1")]
    pub slot: u64,
    #[prost(bytes = "vec", tag = "2")]
    pub signature: ::prost::alloc::vec::Vec<u8>,
    #[prost(bool, tag = "3")]
    pub is_vote: bool,
    #[prost(uint64, tag = "4")]
    pub index: u64,
    #[prost(message, optional, tag = "5")]
    pub err: ::core::option::Option<
        super::solana::storage::confirmed_block::TransactionError,
    >,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeUpdateBlock {
    #[prost(uint64, tag = "1")]
    pub slot: u64,
    #[prost(string, tag = "2")]
    pub blockhash: ::prost::alloc::string::String,
    #[prost(message, optional, tag = "3")]
    pub rewards: ::core::option::Option<
        super::solana::storage::confirmed_block::Rewards,
    >,
    #[prost(message, optional, tag = "4")]
    pub block_time: ::core::option::Option<
        super::solana::storage::confirmed_block::UnixTimestamp,
    >,
    #[prost(message, optional, tag = "5")]
    pub block_height: ::core::option::Option<
        super::solana::storage::confirmed_block::BlockHeight,
    >,
    #[prost(uint64, tag = "7")]
    pub parent_slot: u64,
    #[prost(string, tag = "8")]
    pub parent_blockhash: ::prost::alloc::string::String,
    #[prost(uint64, tag = "9")]
    pub executed_transaction_count: u64,
    #[prost(message, repeated, tag = "6")]
    pub transactions: ::prost::alloc::vec::Vec<SubscribeUpdateTransactionInfo>,
    #[prost(uint64, tag = "10")]
    pub updated_account_count: u64,
    #[prost(message, repeated, tag = "11")]
    pub accounts: ::prost::alloc::vec::Vec<SubscribeUpdateAccountInfo>,
    #[prost(uint64, tag = "12")]
    pub entries_count: u64,
    #[prost(message, repeated, tag = "13")]
    pub entries: ::prost::alloc::vec::Vec<SubscribeUpdateEntry>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeUpdateBlockMeta {
    #[prost(uint64, tag = "1")]
    pub slot: u64,
    #[prost(string, tag = "2")]
    pub blockhash: ::prost::alloc::string::String,
    #[prost(message, optional, tag = "3")]
    pub rewards: ::core::option::Option<
        super::solana::storage::confirmed_block::Rewards,
    >,
    #[prost(message, optional, tag = "4")]
    pub block_time: ::core::option::Option<
        super::solana::storage::confirmed_block::UnixTimestamp,
    >,
    #[prost(message, optional, tag = "5")]
    pub block_height: ::core::option::Option<
        super::solana::storage::confirmed_block::BlockHeight,
    >,
    #[prost(uint64, tag = "6")]
    pub parent_slot: u64,
    #[prost(string, tag = "7")]
    pub parent_blockhash: ::prost::alloc::string::String,
    #[prost(uint64, tag = "8")]
    pub executed_transaction_count: u64,
    #[prost(uint64, tag = "9")]
    pub entries_count: u64,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeUpdateEntry {
    #[prost(uint64, tag = "1")]
    pub slot: u64,
    #[prost(uint64, tag = "2")]
    pub index: u64,
    #[prost(uint64, tag = "3")]
    pub num_hashes: u64,
    #[prost(bytes = "vec", tag = "4")]
    pub hash: ::prost::alloc::vec::Vec<u8>,
    #[prost(uint64, tag = "5")]
    pub executed_transaction_count: u64,
    /// added in v1.18, for solana 1.17 value is always 0
    #[prost(uint64, tag = "6")]
    pub starting_transaction_index: u64,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct SubscribeUpdatePing {}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct SubscribeUpdatePong {
    #[prost(int32, tag = "1")]
    pub id: i32,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct PingRequest {
    #[prost(int32, tag = "1")]
    pub count: i32,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct PongResponse {
    #[prost(int32, tag = "1")]
    pub count: i32,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct GetLatestBlockhashRequest {
    #[prost(enumeration = "CommitmentLevel", optional, tag = "1")]
    pub commitment: ::core::option::Option<i32>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct GetLatestBlockhashResponse {
    #[prost(uint64, tag = "1")]
    pub slot: u64,
    #[prost(string, tag = "2")]
    pub blockhash: ::prost::alloc::string::String,
    #[prost(uint64, tag = "3")]
    pub last_valid_block_height: u64,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct GetBlockHeightRequest {
    #[prost(enumeration = "CommitmentLevel", optional, tag = "1")]
    pub commitment: ::core::option::Option<i32>,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct GetBlockHeightResponse {
    #[prost(uint64, tag = "1")]
    pub block_height: u64,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct GetSlotRequest {
    #[prost(enumeration = "CommitmentLevel", optional, tag = "1")]
    pub commitment: ::core::option::Option<i32>,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct GetSlotResponse {
    #[prost(uint64, tag = "1")]
    pub slot: u64,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct GetVersionRequest {}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct GetVersionResponse {
    #[prost(string, tag = "1")]
    pub version: ::prost::alloc::string::String,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct IsBlockhashValidRequest {
    #[prost(string, tag = "1")]
    pub blockhash: ::prost::alloc::string::String,
    #[prost(enumeration = "CommitmentLevel", optional, tag = "2")]
    pub commitment: ::core::option::Option<i32>,
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct IsBlockhashValidResponse {
    #[prost(uint64, tag = "1")]
    pub slot: u64,
    #[prost(bool, tag = "2")]
    pub valid: bool,
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum CommitmentLevel {
    Processed = 0,
    Confirmed = 1,
    Finalized = 2,
    FirstShredReceived = 3,
    Completed = 4,
    CreatedBank = 5,
    Dead = 6,
}
impl CommitmentLevel {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            Self::Processed => "PROCESSED",
            Self::Confirmed => "CONFIRMED",
            Self::Finalized => "FINALIZED",
            Self::FirstShredReceived => "FIRST_SHRED_RECEIVED",
            Self::Completed => "COMPLETED",
            Self::CreatedBank => "CREATED_BANK",
            Self::Dead => "DEAD",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "PROCESSED" => Some(Self::Processed),
            "CONFIRMED" => Some(Self::Confirmed),
            "FINALIZED" => Some(Self::Finalized),
            "FIRST_SHRED_RECEIVED" => Some(Self::FirstShredReceived),
            "COMPLETED" => Some(Self::Completed),
            "CREATED_BANK" => Some(Self::CreatedBank),
            "DEAD" => Some(Self::Dead),
            _ => None,
        }
    }
}
/// Generated client implementations.
pub mod geyser_client {
    #![allow(
        unused_variables,
        dead_code,
        missing_docs,
        clippy::wildcard_imports,
        clippy::let_unit_value,
    )]
    use tonic::codegen::*;
    use tonic::codegen::http::Uri;
    #[derive(Debug, Clone)]
    pub struct GeyserClient<T> {
        inner: tonic::client::Grpc<T>,
    }
    impl GeyserClient<tonic::transport::Channel> {
        /// Attempt to create a new client by connecting to a given endpoint.
        pub async fn connect<D>(dst: D) -> Result<Self, tonic::transport::Error>
        where
            D: TryInto<tonic::transport::Endpoint>,
            D::Error: Into<StdError>,
        {
            let conn = tonic::transport::Endpoint::new(dst)?.connect().await?;
            Ok(Self::new(conn))
        }
    }
    impl<T> GeyserClient<T>
    where
        T: tonic::client::GrpcService<tonic::body::BoxBody>,
        T::Error: Into<StdError>,
        T::ResponseBody: Body<Data = Bytes> + std::marker::Send + 'static,
        <T::ResponseBody as Body>::Error: Into<StdError> + std::marker::Send,
    {
        pub fn new(inner: T) -> Self {
            let inner = tonic::client::Grpc::new(inner);
            Self { inner }
        }
        pub fn with_origin(inner: T, origin: Uri) -> Self {
            let inner = tonic::client::Grpc::with_origin(inner, origin);
            Self { inner }
        }
        pub fn with_interceptor<F>(
            inner: T,
            interceptor: F,
        ) -> GeyserClient<InterceptedService<T, F>>
        where
            F: tonic::service::Interceptor,
            T::ResponseBody: Default,
            T: tonic::codegen::Service<
                http::Request<tonic::body::BoxBody>,
                Response = http::Response<
                    <T as tonic::client::GrpcService<tonic::body::BoxBody>>::ResponseBody,
                >,
            >,
            <T as tonic::codegen::Service<
                http::Request<tonic::body::BoxBody>,
            >>::Error: Into<StdError> + std::marker::Send + std::marker::Sync,
        {
            GeyserClient::new(InterceptedService::new(inner, interceptor))
        }
        /// Compress requests with the given encoding.
        ///
        /// This requires the server to support it otherwise it might respond with an
        /// error.
        #[must_use]
        pub fn send_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.inner = self.inner.send_compressed(encoding);
            self
        }
        /// Enable decompressing responses.
        #[must_use]
        pub fn accept_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.inner = self.inner.accept_compressed(encoding);
            self
        }
        /// Limits the maximum size of a decoded message.
        ///
        /// Default: `4MB`
        #[must_use]
        pub fn max_decoding_message_size(mut self, limit: usize) -> Self {
            self.inner = self.inner.max_decoding_message_size(limit);
            self
        }
        /// Limits the maximum size of an encoded message.
        ///
        /// Default: `usize::MAX`
        #[must_use]
        pub fn max_encoding_message_size(mut self, limit: usize) -> Self {
            self.inner = self.inner.max_encoding_message_size(limit);
            self
        }
        pub async fn subscribe(
            &mut self,
            request: impl tonic::IntoStreamingRequest<Message = super::SubscribeRequest>,
        ) -> std::result::Result<
            tonic::Response<tonic::codec::Streaming<super::SubscribeUpdate>>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static("/geyser.Geyser/Subscribe");
            let mut req = request.into_streaming_request();
            req.extensions_mut().insert(GrpcMethod::new("geyser.Geyser", "Subscribe"));
            self.inner.streaming(req, path, codec).await
        }
        pub async fn ping(
            &mut self,
            request: impl tonic::IntoRequest<super::PingRequest>,
        ) -> std::result::Result<tonic::Response<super::PongResponse>, tonic::Status> {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static("/geyser.Geyser/Ping");
            let mut req = request.into_request();
            req.extensions_mut().insert(GrpcMethod::new("geyser.Geyser", "Ping"));
            self.inner.unary(req, path, codec).await
        }
        pub async fn get_latest_blockhash(
            &mut self,
            request: impl tonic::IntoRequest<super::GetLatestBlockhashRequest>,
        ) -> std::result::Result<
            tonic::Response<super::GetLatestBlockhashResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/geyser.Geyser/GetLatestBlockhash",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("geyser.Geyser", "GetLatestBlockhash"));
            self.inner.unary(req, path, codec).await
        }
        pub async fn get_block_height(
            &mut self,
            request: impl tonic::IntoRequest<super::GetBlockHeightRequest>,
        ) -> std::result::Result<
            tonic::Response<super::GetBlockHeightResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/geyser.Geyser/GetBlockHeight",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("geyser.Geyser", "GetBlockHeight"));
            self.inner.unary(req, path, codec).await
        }
        pub async fn get_slot(
            &mut self,
            request: impl tonic::IntoRequest<super::GetSlotRequest>,
        ) -> std::result::Result<
            tonic::Response<super::GetSlotResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static("/geyser.Geyser/GetSlot");
            let mut req = request.into_request();
            req.extensions_mut().insert(GrpcMethod::new("geyser.Geyser", "GetSlot"));
            self.inner.unary(req, path, codec).await
        }
        pub async fn is_blockhash_valid(
            &mut self,
            request: impl tonic::IntoRequest<super::IsBlockhashValidRequest>,
        ) -> std::result::Result<
            tonic::Response<super::IsBlockhashValidResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/geyser.Geyser/IsBlockhashValid",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(GrpcMethod::new("geyser.Geyser", "IsBlockhashValid"));
            self.inner.unary(req, path, codec).await
        }
        pub async fn get_version(
            &mut self,
            request: impl tonic::IntoRequest<super::GetVersionRequest>,
        ) -> std::result::Result<
            tonic::Response<super::GetVersionResponse>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static("/geyser.Geyser/GetVersion");
            let mut req = request.into_request();
            req.extensions_mut().insert(GrpcMethod::new("geyser.Geyser", "GetVersion"));
            self.inner.unary(req, path, codec).await
        }
    }
}
/// Generated server implementations.
pub mod geyser_server {
    #![allow(
        unused_variables,
        dead_code,
        missing_docs,
        clippy::wildcard_imports,
        clippy::let_unit_value,
    )]
    use tonic::codegen::*;
    /// Generated trait containing gRPC methods that should be implemented for use with GeyserServer.
    #[async_trait]
    pub trait Geyser: std::marker::Send + std::marker::Sync + 'static {
        /// Server streaming response type for the Subscribe method.
        type SubscribeStream: tonic::codegen::tokio_stream::Stream<
                Item = std::result::Result<super::SubscribeUpdate, tonic::Status>,
            >
            + std::marker::Send
            + 'static;
        async fn subscribe(
            &self,
            request: tonic::Request<tonic::Streaming<super::SubscribeRequest>>,
        ) -> std::result::Result<tonic::Response<Self::SubscribeStream>, tonic::Status>;
        async fn ping(
            &self,
            request: tonic::Request<super::PingRequest>,
        ) -> std::result::Result<tonic::Response<super::PongResponse>, tonic::Status>;
        async fn get_latest_blockhash(
            &self,
            request: tonic::Request<super::GetLatestBlockhashRequest>,
        ) -> std::result::Result<
            tonic::Response<super::GetLatestBlockhashResponse>,
            tonic::Status,
        >;
        async fn get_block_height(
            &self,
            request: tonic::Request<super::GetBlockHeightRequest>,
        ) -> std::result::Result<
            tonic::Response<super::GetBlockHeightResponse>,
            tonic::Status,
        >;
        async fn get_slot(
            &self,
            request: tonic::Request<super::GetSlotRequest>,
        ) -> std::result::Result<tonic::Response<super::GetSlotResponse>, tonic::Status>;
        async fn is_blockhash_valid(
            &self,
            request: tonic::Request<super::IsBlockhashValidRequest>,
        ) -> std::result::Result<
            tonic::Response<super::IsBlockhashValidResponse>,
            tonic::Status,
        >;
        async fn get_version(
            &self,
            request: tonic::Request<super::GetVersionRequest>,
        ) -> std::result::Result<
            tonic::Response<super::GetVersionResponse>,
            tonic::Status,
        >;
    }
    #[derive(Debug)]
    pub struct GeyserServer<T> {
        inner: Arc<T>,
        accept_compression_encodings: EnabledCompressionEncodings,
        send_compression_encodings: EnabledCompressionEncodings,
        max_decoding_message_size: Option<usize>,
        max_encoding_message_size: Option<usize>,
    }
    impl<T> GeyserServer<T> {
        pub fn new(inner: T) -> Self {
            Self::from_arc(Arc::new(inner))
        }
        pub fn from_arc(inner: Arc<T>) -> Self {
            Self {
                inner,
                accept_compression_encodings: Default::default(),
                send_compression_encodings: Default::default(),
                max_decoding_message_size: None,
                max_encoding_message_size: None,
            }
        }
        pub fn with_interceptor<F>(
            inner: T,
            interceptor: F,
        ) -> InterceptedService<Self, F>
        where
            F: tonic::service::Interceptor,
        {
            InterceptedService::new(Self::new(inner), interceptor)
        }
        /// Enable decompressing requests with the given encoding.
        #[must_use]
        pub fn accept_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.accept_compression_encodings.enable(encoding);
            self
        }
        /// Compress responses with the given encoding, if the client supports it.
        #[must_use]
        pub fn send_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.send_compression_encodings.enable(encoding);
            self
        }
        /// Limits the maximum size of a decoded message.
        ///
        /// Default: `4MB`
        #[must_use]
        pub fn max_decoding_message_size(mut self, limit: usize) -> Self {
            self.max_decoding_message_size = Some(limit);
            self
        }
        /// Limits the maximum size of an encoded message.
        ///
        /// Default: `usize::MAX`
        #[must_use]
        pub fn max_encoding_message_size(mut self, limit: usize) -> Self {
            self.max_encoding_message_size = Some(limit);
            self
        }
    }
    impl<T, B> tonic::codegen::Service<http::Request<B>> for GeyserServer<T>
    where
        T: Geyser,
        B: Body + std::marker::Send + 'static,
        B::Error: Into<StdError> + std::marker::Send + 'static,
    {
        type Response = http::Response<tonic::body::BoxBody>;
        type Error = std::convert::Infallible;
        type Future = BoxFuture<Self::Response, Self::Error>;
        fn poll_ready(
            &mut self,
            _cx: &mut Context<'_>,
        ) -> Poll<std::result::Result<(), Self::Error>> {
            Poll::Ready(Ok(()))
        }
        fn call(&mut self, req: http::Request<B>) -> Self::Future {
            match req.uri().path() {
                "/geyser.Geyser/Subscribe" => {
                    #[allow(non_camel_case_types)]
                    struct SubscribeSvc<T: Geyser>(pub Arc<T>);
                    impl<
                        T: Geyser,
                    > tonic::server::StreamingService<super::SubscribeRequest>
                    for SubscribeSvc<T> {
                        type Response = super::SubscribeUpdate;
                        type ResponseStream = T::SubscribeStream;
                        type Future = BoxFuture<
                            tonic::Response<Self::ResponseStream>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<
                                tonic::Streaming<super::SubscribeRequest>,
                            >,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as Geyser>::subscribe(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = SubscribeSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.streaming(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/geyser.Geyser/Ping" => {
                    #[allow(non_camel_case_types)]
                    struct PingSvc<T: Geyser>(pub Arc<T>);
                    impl<T: Geyser> tonic::server::UnaryService<super::PingRequest>
                    for PingSvc<T> {
                        type Response = super::PongResponse;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::PingRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as Geyser>::ping(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = PingSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/geyser.Geyser/GetLatestBlockhash" => {
                    #[allow(non_camel_case_types)]
                    struct GetLatestBlockhashSvc<T: Geyser>(pub Arc<T>);
                    impl<
                        T: Geyser,
                    > tonic::server::UnaryService<super::GetLatestBlockhashRequest>
                    for GetLatestBlockhashSvc<T> {
                        type Response = super::GetLatestBlockhashResponse;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::GetLatestBlockhashRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as Geyser>::get_latest_blockhash(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = GetLatestBlockhashSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/geyser.Geyser/GetBlockHeight" => {
                    #[allow(non_camel_case_types)]
                    struct GetBlockHeightSvc<T: Geyser>(pub Arc<T>);
                    impl<
                        T: Geyser,
                    > tonic::server::UnaryService<super::GetBlockHeightRequest>
                    for GetBlockHeightSvc<T> {
                        type Response = super::GetBlockHeightResponse;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::GetBlockHeightRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as Geyser>::get_block_height(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = GetBlockHeightSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/geyser.Geyser/GetSlot" => {
                    #[allow(non_camel_case_types)]
                    struct GetSlotSvc<T: Geyser>(pub Arc<T>);
                    impl<T: Geyser> tonic::server::UnaryService<super::GetSlotRequest>
                    for GetSlotSvc<T> {
                        type Response = super::GetSlotResponse;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::GetSlotRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as Geyser>::get_slot(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = GetSlotSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/geyser.Geyser/IsBlockhashValid" => {
                    #[allow(non_camel_case_types)]
                    struct IsBlockhashValidSvc<T: Geyser>(pub Arc<T>);
                    impl<
                        T: Geyser,
                    > tonic::server::UnaryService<super::IsBlockhashValidRequest>
                    for IsBlockhashValidSvc<T> {
                        type Response = super::IsBlockhashValidResponse;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::IsBlockhashValidRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as Geyser>::is_blockhash_valid(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = IsBlockhashValidSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                "/geyser.Geyser/GetVersion" => {
                    #[allow(non_camel_case_types)]
                    struct GetVersionSvc<T: Geyser>(pub Arc<T>);
                    impl<T: Geyser> tonic::server::UnaryService<super::GetVersionRequest>
                    for GetVersionSvc<T> {
                        type Response = super::GetVersionResponse;
                        type Future = BoxFuture<
                            tonic::Response<Self::Response>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::GetVersionRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as Geyser>::get_version(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = GetVersionSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.unary(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                _ => {
                    Box::pin(async move {
                        let mut response = http::Response::new(empty_body());
                        let headers = response.headers_mut();
                        headers
                            .insert(
                                tonic::Status::GRPC_STATUS,
                                (tonic::Code::Unimplemented as i32).into(),
                            );
                        headers
                            .insert(
                                http::header::CONTENT_TYPE,
                                tonic::metadata::GRPC_CONTENT_TYPE,
                            );
                        Ok(response)
                    })
                }
            }
        }
    }
    impl<T> Clone for GeyserServer<T> {
        fn clone(&self) -> Self {
            let inner = self.inner.clone();
            Self {
                inner,
                accept_compression_encodings: self.accept_compression_encodings,
                send_compression_encodings: self.send_compression_encodings,
                max_decoding_message_size: self.max_decoding_message_size,
                max_encoding_message_size: self.max_encoding_message_size,
            }
        }
    }
    /// Generated gRPC service name
    pub const SERVICE_NAME: &str = "geyser.Geyser";
    impl<T> tonic::server::NamedService for GeyserServer<T> {
        const NAME: &'static str = SERVICE_NAME;
    }
}
