# Makefile.in generated by automake 1.16.5 from Makefile.am.
# benchmarks/Makefile.  Generated from Makefile.in by configure.

# Copyright (C) 1994-2021 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.




VPATH = /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/benchmarks
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/protobuf
pkgincludedir = $(includedir)/protobuf
pkglibdir = $(libdir)/protobuf
pkglibexecdir = $(libexecdir)/protobuf
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = x86_64-pc-mingw64
host_triplet = x86_64-pc-mingw64
target_triplet = x86_64-pc-mingw64
bin_PROGRAMS = cpp-benchmark$(EXEEXT) cpp-no-group-benchmark$(EXEEXT) \
	protoc-gen-gogoproto$(EXEEXT) gogo-data-scrubber$(EXEEXT) \
	protoc-gen-proto2_to_proto3$(EXEEXT) \
	proto3-data-stripper$(EXEEXT)
subdir = benchmarks
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/ac_system_extensions.m4 \
	$(top_srcdir)/m4/acx_check_suncc.m4 \
	$(top_srcdir)/m4/ax_cxx_compile_stdcxx.m4 \
	$(top_srcdir)/m4/ax_prog_cc_for_build.m4 \
	$(top_srcdir)/m4/ax_prog_cxx_for_build.m4 \
	$(top_srcdir)/m4/ax_pthread.m4 $(top_srcdir)/m4/libtool.m4 \
	$(top_srcdir)/m4/ltoptions.m4 $(top_srcdir)/m4/ltsugar.m4 \
	$(top_srcdir)/m4/ltversion.m4 $(top_srcdir)/m4/lt~obsolete.m4 \
	$(top_srcdir)/m4/stl_hash.m4 $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
am__installdirs = "$(DESTDIR)$(bindir)" "$(DESTDIR)$(libdir)"
PROGRAMS = $(bin_PROGRAMS)
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__uninstall_files_from_dir = { \
  test -z "$$files" \
    || { test ! -d "$$dir" && test ! -f "$$dir" && test ! -r "$$dir"; } \
    || { echo " ( cd '$$dir' && rm -f" $$files ")"; \
         $(am__cd) "$$dir" && rm -f $$files; }; \
  }
LTLIBRARIES = $(lib_LTLIBRARIES)
libbenchmark_messages_la_DEPENDENCIES =  \
	$(top_srcdir)/src/.libs/libprotobuf.la
am__dirstamp = $(am__leading_dot)dirstamp
am_libbenchmark_messages_la_OBJECTS =  \
	python/libbenchmark_messages_la-python_benchmark_messages.lo
am__objects_1 = cpp/libbenchmark_messages_la-benchmarks.pb.lo \
	cpp/datasets/google_message1/proto3/libbenchmark_messages_la-benchmark_message1_proto3.pb.lo
am__objects_2 = cpp/datasets/google_message1/proto2/libbenchmark_messages_la-benchmark_message1_proto2.pb.lo \
	cpp/datasets/google_message2/libbenchmark_messages_la-benchmark_message2.pb.lo \
	cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3.pb.lo \
	cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_1.pb.lo \
	cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_2.pb.lo \
	cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_3.pb.lo \
	cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_4.pb.lo \
	cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_5.pb.lo \
	cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_6.pb.lo \
	cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_7.pb.lo \
	cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_8.pb.lo \
	cpp/datasets/google_message4/libbenchmark_messages_la-benchmark_message4.pb.lo \
	cpp/datasets/google_message4/libbenchmark_messages_la-benchmark_message4_1.pb.lo \
	cpp/datasets/google_message4/libbenchmark_messages_la-benchmark_message4_2.pb.lo \
	cpp/datasets/google_message4/libbenchmark_messages_la-benchmark_message4_3.pb.lo
am__objects_3 =
nodist_libbenchmark_messages_la_OBJECTS = $(am__objects_1) \
	$(am__objects_2) $(am__objects_3) $(am__objects_3)
libbenchmark_messages_la_OBJECTS =  \
	$(am_libbenchmark_messages_la_OBJECTS) \
	$(nodist_libbenchmark_messages_la_OBJECTS)
AM_V_lt = $(am__v_lt_$(V))
am__v_lt_ = $(am__v_lt_$(AM_DEFAULT_VERBOSITY))
am__v_lt_0 = --silent
am__v_lt_1 = 
libbenchmark_messages_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CXXLD) \
	$(AM_CXXFLAGS) $(CXXFLAGS) $(libbenchmark_messages_la_LDFLAGS) \
	$(LDFLAGS) -o $@
am_cpp_benchmark_OBJECTS = cpp/benchmark-cpp_benchmark.$(OBJEXT)
am__objects_4 = cpp/benchmark-benchmarks.pb.$(OBJEXT) \
	cpp/datasets/google_message1/proto3/benchmark-benchmark_message1_proto3.pb.$(OBJEXT)
am__objects_5 = cpp/datasets/google_message1/proto2/benchmark-benchmark_message1_proto2.pb.$(OBJEXT) \
	cpp/datasets/google_message2/benchmark-benchmark_message2.pb.$(OBJEXT) \
	cpp/datasets/google_message3/benchmark-benchmark_message3.pb.$(OBJEXT) \
	cpp/datasets/google_message3/benchmark-benchmark_message3_1.pb.$(OBJEXT) \
	cpp/datasets/google_message3/benchmark-benchmark_message3_2.pb.$(OBJEXT) \
	cpp/datasets/google_message3/benchmark-benchmark_message3_3.pb.$(OBJEXT) \
	cpp/datasets/google_message3/benchmark-benchmark_message3_4.pb.$(OBJEXT) \
	cpp/datasets/google_message3/benchmark-benchmark_message3_5.pb.$(OBJEXT) \
	cpp/datasets/google_message3/benchmark-benchmark_message3_6.pb.$(OBJEXT) \
	cpp/datasets/google_message3/benchmark-benchmark_message3_7.pb.$(OBJEXT) \
	cpp/datasets/google_message3/benchmark-benchmark_message3_8.pb.$(OBJEXT) \
	cpp/datasets/google_message4/benchmark-benchmark_message4.pb.$(OBJEXT) \
	cpp/datasets/google_message4/benchmark-benchmark_message4_1.pb.$(OBJEXT) \
	cpp/datasets/google_message4/benchmark-benchmark_message4_2.pb.$(OBJEXT) \
	cpp/datasets/google_message4/benchmark-benchmark_message4_3.pb.$(OBJEXT)
nodist_cpp_benchmark_OBJECTS = $(am__objects_4) $(am__objects_5) \
	$(am__objects_3) $(am__objects_3)
cpp_benchmark_OBJECTS = $(am_cpp_benchmark_OBJECTS) \
	$(nodist_cpp_benchmark_OBJECTS)
cpp_benchmark_DEPENDENCIES = $(top_srcdir)/src/libprotobuf.la \
	$(top_srcdir)/third_party/benchmark/src/libbenchmark.a
am_cpp_no_group_benchmark_OBJECTS = gogo/cpp_no_group/cpp_no_group_benchmark-cpp_benchmark.$(OBJEXT)
am__objects_6 = gogo/cpp_no_group/datasets/google_message1/proto2/cpp_no_group_benchmark-benchmark_message1_proto2.pb.$(OBJEXT) \
	gogo/cpp_no_group/datasets/google_message2/cpp_no_group_benchmark-benchmark_message2.pb.$(OBJEXT) \
	gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3.pb.$(OBJEXT) \
	gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_1.pb.$(OBJEXT) \
	gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_2.pb.$(OBJEXT) \
	gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_3.pb.$(OBJEXT) \
	gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_4.pb.$(OBJEXT) \
	gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_5.pb.$(OBJEXT) \
	gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_6.pb.$(OBJEXT) \
	gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_7.pb.$(OBJEXT) \
	gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_8.pb.$(OBJEXT) \
	gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4.pb.$(OBJEXT) \
	gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_1.pb.$(OBJEXT) \
	gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_2.pb.$(OBJEXT) \
	gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_3.pb.$(OBJEXT)
am__objects_7 = gogo/cpp_no_group/cpp_no_group_benchmark-benchmarks.pb.$(OBJEXT) \
	gogo/cpp_no_group/datasets/google_message1/proto3/cpp_no_group_benchmark-benchmark_message1_proto3.pb.$(OBJEXT)
nodist_cpp_no_group_benchmark_OBJECTS = $(am__objects_6) \
	$(am__objects_7) $(am__objects_3) $(am__objects_3)
cpp_no_group_benchmark_OBJECTS = $(am_cpp_no_group_benchmark_OBJECTS) \
	$(nodist_cpp_no_group_benchmark_OBJECTS)
cpp_no_group_benchmark_DEPENDENCIES =  \
	$(top_srcdir)/src/libprotobuf.la \
	$(top_srcdir)/third_party/benchmark/src/libbenchmark.a
am_gogo_data_scrubber_OBJECTS =  \
	util/gogo_data_scrubber-gogo_data_scrubber.$(OBJEXT)
am__objects_8 = cpp/gogo_data_scrubber-benchmarks.pb.$(OBJEXT) \
	cpp/datasets/google_message1/proto3/gogo_data_scrubber-benchmark_message1_proto3.pb.$(OBJEXT)
am__objects_9 = cpp/datasets/google_message1/proto2/gogo_data_scrubber-benchmark_message1_proto2.pb.$(OBJEXT) \
	cpp/datasets/google_message2/gogo_data_scrubber-benchmark_message2.pb.$(OBJEXT) \
	cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3.pb.$(OBJEXT) \
	cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_1.pb.$(OBJEXT) \
	cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_2.pb.$(OBJEXT) \
	cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_3.pb.$(OBJEXT) \
	cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_4.pb.$(OBJEXT) \
	cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_5.pb.$(OBJEXT) \
	cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_6.pb.$(OBJEXT) \
	cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_7.pb.$(OBJEXT) \
	cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_8.pb.$(OBJEXT) \
	cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4.pb.$(OBJEXT) \
	cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_1.pb.$(OBJEXT) \
	cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_2.pb.$(OBJEXT) \
	cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_3.pb.$(OBJEXT)
nodist_gogo_data_scrubber_OBJECTS = $(am__objects_8) $(am__objects_9) \
	$(am__objects_3) $(am__objects_3)
gogo_data_scrubber_OBJECTS = $(am_gogo_data_scrubber_OBJECTS) \
	$(nodist_gogo_data_scrubber_OBJECTS)
gogo_data_scrubber_DEPENDENCIES = $(top_srcdir)/src/libprotobuf.la
am_proto3_data_stripper_OBJECTS =  \
	util/proto3_data_stripper-proto3_data_stripper.$(OBJEXT)
am__objects_10 = cpp/proto3_data_stripper-benchmarks.pb.$(OBJEXT) \
	cpp/datasets/google_message1/proto3/proto3_data_stripper-benchmark_message1_proto3.pb.$(OBJEXT)
am__objects_11 = cpp/datasets/google_message1/proto2/proto3_data_stripper-benchmark_message1_proto2.pb.$(OBJEXT) \
	cpp/datasets/google_message2/proto3_data_stripper-benchmark_message2.pb.$(OBJEXT) \
	cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3.pb.$(OBJEXT) \
	cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_1.pb.$(OBJEXT) \
	cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_2.pb.$(OBJEXT) \
	cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_3.pb.$(OBJEXT) \
	cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_4.pb.$(OBJEXT) \
	cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_5.pb.$(OBJEXT) \
	cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_6.pb.$(OBJEXT) \
	cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_7.pb.$(OBJEXT) \
	cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_8.pb.$(OBJEXT) \
	cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4.pb.$(OBJEXT) \
	cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_1.pb.$(OBJEXT) \
	cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_2.pb.$(OBJEXT) \
	cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_3.pb.$(OBJEXT)
nodist_proto3_data_stripper_OBJECTS = $(am__objects_10) \
	$(am__objects_11) $(am__objects_3) $(am__objects_3)
proto3_data_stripper_OBJECTS = $(am_proto3_data_stripper_OBJECTS) \
	$(nodist_proto3_data_stripper_OBJECTS)
proto3_data_stripper_DEPENDENCIES = $(top_srcdir)/src/libprotobuf.la
am_protoc_gen_gogoproto_OBJECTS =  \
	util/protoc_gen_gogoproto-protoc-gen-gogoproto.$(OBJEXT)
protoc_gen_gogoproto_OBJECTS = $(am_protoc_gen_gogoproto_OBJECTS)
protoc_gen_gogoproto_DEPENDENCIES = $(top_srcdir)/src/libprotobuf.la \
	$(top_srcdir)/src/libprotoc.la
am_protoc_gen_proto2_to_proto3_OBJECTS = util/protoc_gen_proto2_to_proto3-protoc-gen-proto2_to_proto3.$(OBJEXT)
protoc_gen_proto2_to_proto3_OBJECTS =  \
	$(am_protoc_gen_proto2_to_proto3_OBJECTS)
protoc_gen_proto2_to_proto3_DEPENDENCIES =  \
	$(top_srcdir)/src/libprotobuf.la \
	$(top_srcdir)/src/libprotoc.la
AM_V_P = $(am__v_P_$(V))
am__v_P_ = $(am__v_P_$(AM_DEFAULT_VERBOSITY))
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_$(V))
am__v_GEN_ = $(am__v_GEN_$(AM_DEFAULT_VERBOSITY))
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_$(V))
am__v_at_ = $(am__v_at_$(AM_DEFAULT_VERBOSITY))
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = -I. -I$(srcdir) -I$(top_builddir)
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade = cpp/$(DEPDIR)/benchmark-benchmarks.pb.Po \
	cpp/$(DEPDIR)/benchmark-cpp_benchmark.Po \
	cpp/$(DEPDIR)/gogo_data_scrubber-benchmarks.pb.Po \
	cpp/$(DEPDIR)/libbenchmark_messages_la-benchmarks.pb.Plo \
	cpp/$(DEPDIR)/proto3_data_stripper-benchmarks.pb.Po \
	cpp/datasets/google_message1/proto2/$(DEPDIR)/benchmark-benchmark_message1_proto2.pb.Po \
	cpp/datasets/google_message1/proto2/$(DEPDIR)/gogo_data_scrubber-benchmark_message1_proto2.pb.Po \
	cpp/datasets/google_message1/proto2/$(DEPDIR)/libbenchmark_messages_la-benchmark_message1_proto2.pb.Plo \
	cpp/datasets/google_message1/proto2/$(DEPDIR)/proto3_data_stripper-benchmark_message1_proto2.pb.Po \
	cpp/datasets/google_message1/proto3/$(DEPDIR)/benchmark-benchmark_message1_proto3.pb.Po \
	cpp/datasets/google_message1/proto3/$(DEPDIR)/gogo_data_scrubber-benchmark_message1_proto3.pb.Po \
	cpp/datasets/google_message1/proto3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message1_proto3.pb.Plo \
	cpp/datasets/google_message1/proto3/$(DEPDIR)/proto3_data_stripper-benchmark_message1_proto3.pb.Po \
	cpp/datasets/google_message2/$(DEPDIR)/benchmark-benchmark_message2.pb.Po \
	cpp/datasets/google_message2/$(DEPDIR)/gogo_data_scrubber-benchmark_message2.pb.Po \
	cpp/datasets/google_message2/$(DEPDIR)/libbenchmark_messages_la-benchmark_message2.pb.Plo \
	cpp/datasets/google_message2/$(DEPDIR)/proto3_data_stripper-benchmark_message2.pb.Po \
	cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3.pb.Po \
	cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_1.pb.Po \
	cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_2.pb.Po \
	cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_3.pb.Po \
	cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_4.pb.Po \
	cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_5.pb.Po \
	cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_6.pb.Po \
	cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_7.pb.Po \
	cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_8.pb.Po \
	cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3.pb.Po \
	cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_1.pb.Po \
	cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_2.pb.Po \
	cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_3.pb.Po \
	cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_4.pb.Po \
	cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_5.pb.Po \
	cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_6.pb.Po \
	cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_7.pb.Po \
	cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_8.pb.Po \
	cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3.pb.Plo \
	cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_1.pb.Plo \
	cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_2.pb.Plo \
	cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_3.pb.Plo \
	cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_4.pb.Plo \
	cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_5.pb.Plo \
	cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_6.pb.Plo \
	cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_7.pb.Plo \
	cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_8.pb.Plo \
	cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3.pb.Po \
	cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_1.pb.Po \
	cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_2.pb.Po \
	cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_3.pb.Po \
	cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_4.pb.Po \
	cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_5.pb.Po \
	cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_6.pb.Po \
	cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_7.pb.Po \
	cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_8.pb.Po \
	cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4.pb.Po \
	cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4_1.pb.Po \
	cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4_2.pb.Po \
	cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4_3.pb.Po \
	cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4.pb.Po \
	cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4_1.pb.Po \
	cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4_2.pb.Po \
	cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4_3.pb.Po \
	cpp/datasets/google_message4/$(DEPDIR)/libbenchmark_messages_la-benchmark_message4.pb.Plo \
	cpp/datasets/google_message4/$(DEPDIR)/libbenchmark_messages_la-benchmark_message4_1.pb.Plo \
	cpp/datasets/google_message4/$(DEPDIR)/libbenchmark_messages_la-benchmark_message4_2.pb.Plo \
	cpp/datasets/google_message4/$(DEPDIR)/libbenchmark_messages_la-benchmark_message4_3.pb.Plo \
	cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4.pb.Po \
	cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4_1.pb.Po \
	cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4_2.pb.Po \
	cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4_3.pb.Po \
	gogo/cpp_no_group/$(DEPDIR)/cpp_no_group_benchmark-benchmarks.pb.Po \
	gogo/cpp_no_group/$(DEPDIR)/cpp_no_group_benchmark-cpp_benchmark.Po \
	gogo/cpp_no_group/datasets/google_message1/proto2/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message1_proto2.pb.Po \
	gogo/cpp_no_group/datasets/google_message1/proto3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message1_proto3.pb.Po \
	gogo/cpp_no_group/datasets/google_message2/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message2.pb.Po \
	gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3.pb.Po \
	gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_1.pb.Po \
	gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_2.pb.Po \
	gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_3.pb.Po \
	gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_4.pb.Po \
	gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_5.pb.Po \
	gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_6.pb.Po \
	gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_7.pb.Po \
	gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_8.pb.Po \
	gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4.pb.Po \
	gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4_1.pb.Po \
	gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4_2.pb.Po \
	gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4_3.pb.Po \
	python/$(DEPDIR)/libbenchmark_messages_la-python_benchmark_messages.Plo \
	util/$(DEPDIR)/gogo_data_scrubber-gogo_data_scrubber.Po \
	util/$(DEPDIR)/proto3_data_stripper-proto3_data_stripper.Po \
	util/$(DEPDIR)/protoc_gen_gogoproto-protoc-gen-gogoproto.Po \
	util/$(DEPDIR)/protoc_gen_proto2_to_proto3-protoc-gen-proto2_to_proto3.Po
am__mv = mv -f
CXXCOMPILE = $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) \
	$(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS)
LTCXXCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CXXFLAGS) $(CXXFLAGS)
AM_V_CXX = $(am__v_CXX_$(V))
am__v_CXX_ = $(am__v_CXX_$(AM_DEFAULT_VERBOSITY))
am__v_CXX_0 = @echo "  CXX     " $@;
am__v_CXX_1 = 
CXXLD = $(CXX)
CXXLINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CXXLD) $(AM_CXXFLAGS) \
	$(CXXFLAGS) $(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CXXLD = $(am__v_CXXLD_$(V))
am__v_CXXLD_ = $(am__v_CXXLD_$(AM_DEFAULT_VERBOSITY))
am__v_CXXLD_0 = @echo "  CXXLD   " $@;
am__v_CXXLD_1 = 
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
LTCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_$(V))
am__v_CC_ = $(am__v_CC_$(AM_DEFAULT_VERBOSITY))
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_$(V))
am__v_CCLD_ = $(am__v_CCLD_$(AM_DEFAULT_VERBOSITY))
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
SOURCES = $(libbenchmark_messages_la_SOURCES) \
	$(nodist_libbenchmark_messages_la_SOURCES) \
	$(cpp_benchmark_SOURCES) $(nodist_cpp_benchmark_SOURCES) \
	$(cpp_no_group_benchmark_SOURCES) \
	$(nodist_cpp_no_group_benchmark_SOURCES) \
	$(gogo_data_scrubber_SOURCES) \
	$(nodist_gogo_data_scrubber_SOURCES) \
	$(proto3_data_stripper_SOURCES) \
	$(nodist_proto3_data_stripper_SOURCES) \
	$(protoc_gen_gogoproto_SOURCES) \
	$(protoc_gen_proto2_to_proto3_SOURCES)
DIST_SOURCES = $(libbenchmark_messages_la_SOURCES) \
	$(cpp_benchmark_SOURCES) $(cpp_no_group_benchmark_SOURCES) \
	$(gogo_data_scrubber_SOURCES) $(proto3_data_stripper_SOURCES) \
	$(protoc_gen_gogoproto_SOURCES) \
	$(protoc_gen_proto2_to_proto3_SOURCES)
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
am__DIST_COMMON = $(srcdir)/Makefile.in $(top_srcdir)/depcomp \
	README.md
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = ${SHELL} '/c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/missing' aclocal-1.16
AMTAR = $${TAR-tar}
AM_DEFAULT_VERBOSITY = 0
AR = ar
AUTOCONF = ${SHELL} '/c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/missing' autoconf
AUTOHEADER = ${SHELL} '/c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/missing' autoheader
AUTOMAKE = ${SHELL} '/c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/missing' automake-1.16
AWK = gawk
BUILD_EXEEXT = 
BUILD_OBJEXT = 
CC = gcc.exe
CCDEPMODE = depmode=gcc3
CC_FOR_BUILD = gcc
CFLAGS = -O0 -ffunction-sections -fdata-sections -m64
CFLAGS_FOR_BUILD = -g -O2
CPP = gcc.exe -E
CPPFLAGS = 
CPPFLAGS_FOR_BUILD = 
CPP_FOR_BUILD = gcc -E
CSCOPE = cscope
CTAGS = ctags
CXX = g++.exe
CXXCPP = g++.exe -E
CXXCPPFLAGS_FOR_BUILD = 
CXXCPP_FOR_BUILD = g++ -E
CXXDEPMODE = depmode=gcc3
CXXFLAGS = -O0 -ffunction-sections -fdata-sections -m64
CXXFLAGS_FOR_BUILD = -g -O2
CXX_FOR_BUILD = g++
CYGPATH_W = cygpath -w
DEFS = -DHAVE_CONFIG_H
DEPDIR = .deps
DIST_LANG = all
DLLTOOL = dlltool
DSYMUTIL = 
DUMPBIN = 
ECHO_C = 
ECHO_N = -n
ECHO_T = 
EGREP = /usr/bin/grep -E
ETAGS = etags
EXEEXT = .exe
FGREP = /usr/bin/grep -F
FILECMD = file
GREP = /usr/bin/grep
HAVE_CXX11 = 1
INSTALL = /usr/bin/install -c
INSTALL_DATA = ${INSTALL} -m 644
INSTALL_PROGRAM = ${INSTALL}
INSTALL_SCRIPT = ${INSTALL}
INSTALL_STRIP_PROGRAM = $(install_sh) -c -s
ISAINFO = 
LD = C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe
LDFLAGS = 
LDFLAGS_FOR_BUILD = 
LIBATOMIC_LIBS = 
LIBLOG_LIBS = 
LIBOBJS = 
LIBS = -lz 
LIBTOOL = $(SHELL) $(top_builddir)/libtool
LIPO = 
LN_S = cp -pR
LTLIBOBJS = 
LT_SYS_LIBRARY_PATH = 
MAINT = #
MAKEINFO = ${SHELL} '/c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/missing' makeinfo
MANIFEST_TOOL = :
MKDIR_P = /usr/bin/mkdir -p
NM = /mingw64/bin/nm -B
NMEDIT = 
OBJC = 
OBJCDEPMODE = 
OBJCFLAGS = 
OBJDUMP = objdump
OBJEXT = o
OTOOL = 
OTOOL64 = 
PACKAGE = protobuf
PACKAGE_BUGREPORT = <EMAIL>
PACKAGE_NAME = Protocol Buffers
PACKAGE_STRING = Protocol Buffers 3.21.5
PACKAGE_TARNAME = protobuf
PACKAGE_URL = 
PACKAGE_VERSION = 3.21.5
PATH_SEPARATOR = :
POW_LIB = 
PROTOBUF_OPT_FLAG = 
PROTOC = 
PTHREAD_CC = gcc.exe
PTHREAD_CFLAGS = -pthread
PTHREAD_LIBS = -lpthread
RANLIB = ranlib
SED = /usr/bin/sed
SET_MAKE = 
SHELL = /bin/sh
STRIP = strip
VERSION = 3.21.5
abs_builddir = /d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/build/benchmarks
abs_srcdir = /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/benchmarks
abs_top_builddir = /d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/build
abs_top_srcdir = /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf
ac_ct_AR = ar
ac_ct_CC = gcc.exe
ac_ct_CC_FOR_BUILD = gcc
ac_ct_CXX = 
ac_ct_CXX_FOR_BUILD = g++
ac_ct_DUMPBIN = 
ac_ct_OBJC = 
am__include = include
am__leading_dot = .
am__quote = 
am__tar = tar --format=ustar -chf - "$$tardir"
am__untar = tar -xf -
ax_pthread_config = 
bindir = ${exec_prefix}/bin
build = x86_64-pc-mingw64
build_alias = 
build_cpu = x86_64
build_os = mingw64
build_vendor = pc
builddir = .
datadir = ${datarootdir}
datarootdir = ${prefix}/share
docdir = ${datarootdir}/doc/${PACKAGE_TARNAME}
dvidir = ${docdir}
exec_prefix = ${prefix}
host = x86_64-pc-mingw64
host_alias = 
host_cpu = x86_64
host_os = mingw64
host_vendor = pc
htmldir = ${docdir}
includedir = ${prefix}/include
infodir = ${datarootdir}/info
install_sh = ${SHELL} /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/install-sh
libdir = ${exec_prefix}/lib
libexecdir = ${exec_prefix}/libexec
localedir = ${datarootdir}/locale
localstatedir = ${prefix}/var
mandir = ${datarootdir}/man
mkdir_p = $(MKDIR_P)
oldincludedir = /usr/include
pdfdir = ${docdir}
prefix = /d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install
program_transform_name = s,x,x,
psdir = ${docdir}
runstatedir = ${localstatedir}/run
sbindir = ${exec_prefix}/sbin
sharedstatedir = ${prefix}/com
srcdir = /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/benchmarks
subdirs =  third_party/googletest
sysconfdir = ${prefix}/etc
target = x86_64-pc-mingw64
target_alias = 
target_cpu = x86_64
target_os = mingw64
target_vendor = pc
top_build_prefix = ../
top_builddir = ..
top_srcdir = /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf
benchmarks_protoc_inputs_benchmark_wrapper = \
	benchmarks.proto

benchmarks_protoc_inputs = \
	datasets/google_message1/proto3/benchmark_message1_proto3.proto

benchmarks_protoc_inputs_proto2 = \
	datasets/google_message1/proto2/benchmark_message1_proto2.proto          \
	datasets/google_message2/benchmark_message2.proto                        \
	datasets/google_message3/benchmark_message3.proto                        \
	datasets/google_message3/benchmark_message3_1.proto                      \
	datasets/google_message3/benchmark_message3_2.proto                      \
	datasets/google_message3/benchmark_message3_3.proto                      \
	datasets/google_message3/benchmark_message3_4.proto                      \
	datasets/google_message3/benchmark_message3_5.proto                      \
	datasets/google_message3/benchmark_message3_6.proto                      \
	datasets/google_message3/benchmark_message3_7.proto                      \
	datasets/google_message3/benchmark_message3_8.proto                      \
	datasets/google_message4/benchmark_message4.proto                        \
	datasets/google_message4/benchmark_message4_1.proto                      \
	datasets/google_message4/benchmark_message4_2.proto                      \
	datasets/google_message4/benchmark_message4_3.proto

all_data = $$(find $$(cd $(srcdir) && pwd) -type f -name "dataset.*.pb" -not -path "$$(cd $(srcdir) && pwd)/tmp/*")

############# CPP RULES ##############
benchmarks_protoc_outputs = \
	cpp/benchmarks.pb.cc                                                     \
	cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc

benchmarks_protoc_outputs_header = \
	cpp/benchmarks.pb.h                                                      \
	cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.h

benchmarks_protoc_outputs_proto2_header = \
	cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.h       \
	cpp/datasets/google_message2/benchmark_message2.pb.h                     \
	cpp/datasets/google_message3/benchmark_message3.pb.h                     \
	cpp/datasets/google_message3/benchmark_message3_1.pb.h                   \
	cpp/datasets/google_message3/benchmark_message3_2.pb.h                   \
	cpp/datasets/google_message3/benchmark_message3_3.pb.h                   \
	cpp/datasets/google_message3/benchmark_message3_4.pb.h                   \
	cpp/datasets/google_message3/benchmark_message3_5.pb.h                   \
	cpp/datasets/google_message3/benchmark_message3_6.pb.h                   \
	cpp/datasets/google_message3/benchmark_message3_7.pb.h                   \
	cpp/datasets/google_message3/benchmark_message3_8.pb.h                   \
	cpp/datasets/google_message4/benchmark_message4.pb.h                     \
	cpp/datasets/google_message4/benchmark_message4_1.pb.h                   \
	cpp/datasets/google_message4/benchmark_message4_2.pb.h                   \
	cpp/datasets/google_message4/benchmark_message4_3.pb.h

benchmarks_protoc_outputs_proto2 = \
	cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc      \
	cpp/datasets/google_message2/benchmark_message2.pb.cc                    \
	cpp/datasets/google_message3/benchmark_message3.pb.cc                    \
	cpp/datasets/google_message3/benchmark_message3_1.pb.cc                  \
	cpp/datasets/google_message3/benchmark_message3_2.pb.cc                  \
	cpp/datasets/google_message3/benchmark_message3_3.pb.cc                  \
	cpp/datasets/google_message3/benchmark_message3_4.pb.cc                  \
	cpp/datasets/google_message3/benchmark_message3_5.pb.cc                  \
	cpp/datasets/google_message3/benchmark_message3_6.pb.cc                  \
	cpp/datasets/google_message3/benchmark_message3_7.pb.cc                  \
	cpp/datasets/google_message3/benchmark_message3_8.pb.cc                  \
	cpp/datasets/google_message4/benchmark_message4.pb.cc                    \
	cpp/datasets/google_message4/benchmark_message4_1.pb.cc                  \
	cpp/datasets/google_message4/benchmark_message4_2.pb.cc                  \
	cpp/datasets/google_message4/benchmark_message4_3.pb.cc

AM_CXXFLAGS = $(NO_OPT_CXXFLAGS) $(PROTOBUF_OPT_FLAG) -Wall -Wwrite-strings -Woverloaded-virtual -Wno-sign-compare
cpp_benchmark_LDADD = $(top_srcdir)/src/libprotobuf.la $(top_srcdir)/third_party/benchmark/src/libbenchmark.a
cpp_benchmark_SOURCES = cpp/cpp_benchmark.cc
cpp_benchmark_CPPFLAGS = -I$(top_srcdir)/src -I$(srcdir)/cpp -I$(top_srcdir)/third_party/benchmark/include
nodist_cpp_benchmark_SOURCES = \
	$(benchmarks_protoc_outputs)                                             \
	$(benchmarks_protoc_outputs_proto2)                                      \
	$(benchmarks_protoc_outputs_proto2_header)                               \
	$(benchmarks_protoc_outputs_header)


############ CPP RULES END ############

############# JAVA RULES ##############
java_benchmark_testing_files = \
	java/src/main/java/com/google/protobuf/ProtoCaliperBenchmark.java

python_cpp_pkg_flags = `pkg-config --cflags --libs python3`
lib_LTLIBRARIES = libbenchmark_messages.la
libbenchmark_messages_la_SOURCES = python/python_benchmark_messages.cc
libbenchmark_messages_la_LIBADD = $(top_srcdir)/src/.libs/libprotobuf.la
libbenchmark_messages_la_LDFLAGS = -version-info 1:0:0 -export-dynamic
libbenchmark_messages_la_CPPFLAGS = -I$(top_srcdir)/src -I$(srcdir)/cpp $(python_cpp_pkg_flags)
nodist_libbenchmark_messages_la_SOURCES = \
	$(benchmarks_protoc_outputs)                                    \
	$(benchmarks_protoc_outputs_proto2)                             \
	$(benchmarks_protoc_outputs_proto2_header)                      \
	$(benchmarks_protoc_outputs_header)


############# PYTHON RULES END ##############

############# GO RULES BEGIN ##############
benchmarks_protoc_inputs_proto2_message1 = \
	datasets/google_message1/proto2/benchmark_message1_proto2.proto

benchmarks_protoc_inputs_proto2_message2 = \
	datasets/google_message2/benchmark_message2.proto

benchmarks_protoc_inputs_proto2_message3 = \
	datasets/google_message3/benchmark_message3.proto                        \
	datasets/google_message3/benchmark_message3_1.proto                      \
	datasets/google_message3/benchmark_message3_2.proto                      \
	datasets/google_message3/benchmark_message3_3.proto                      \
	datasets/google_message3/benchmark_message3_4.proto                      \
	datasets/google_message3/benchmark_message3_5.proto                      \
	datasets/google_message3/benchmark_message3_6.proto                      \
	datasets/google_message3/benchmark_message3_7.proto                      \
	datasets/google_message3/benchmark_message3_8.proto

benchmarks_protoc_inputs_proto2_message4 = \
	datasets/google_message4/benchmark_message4.proto                        \
	datasets/google_message4/benchmark_message4_1.proto                      \
	datasets/google_message4/benchmark_message4_2.proto                      \
	datasets/google_message4/benchmark_message4_3.proto


############# GO RULES END ##############

############# GOGO RULES BEGIN ############
cpp_no_group_benchmarks_protoc_outputs_header = \
	gogo/cpp_no_group/benchmarks.pb.h                                         \
	gogo/cpp_no_group/datasets/google_message1/proto3/benchmark_message1_proto3.pb.h

cpp_no_group_benchmarks_protoc_outputs = \
	gogo/cpp_no_group/benchmarks.pb.cc                                        \
	gogo/cpp_no_group/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc

cpp_no_group_benchmarks_protoc_outputs_proto2_header = \
	gogo/cpp_no_group/datasets/google_message1/proto2/benchmark_message1_proto2.pb.h \
	gogo/cpp_no_group/datasets/google_message2/benchmark_message2.pb.h        \
	gogo/cpp_no_group/datasets/google_message3/benchmark_message3.pb.h        \
	gogo/cpp_no_group/datasets/google_message3/benchmark_message3_1.pb.h      \
	gogo/cpp_no_group/datasets/google_message3/benchmark_message3_2.pb.h      \
	gogo/cpp_no_group/datasets/google_message3/benchmark_message3_3.pb.h      \
	gogo/cpp_no_group/datasets/google_message3/benchmark_message3_4.pb.h      \
	gogo/cpp_no_group/datasets/google_message3/benchmark_message3_5.pb.h      \
	gogo/cpp_no_group/datasets/google_message3/benchmark_message3_6.pb.h      \
	gogo/cpp_no_group/datasets/google_message3/benchmark_message3_7.pb.h      \
	gogo/cpp_no_group/datasets/google_message3/benchmark_message3_8.pb.h      \
	gogo/cpp_no_group/datasets/google_message4/benchmark_message4.pb.h        \
	gogo/cpp_no_group/datasets/google_message4/benchmark_message4_1.pb.h      \
	gogo/cpp_no_group/datasets/google_message4/benchmark_message4_2.pb.h      \
	gogo/cpp_no_group/datasets/google_message4/benchmark_message4_3.pb.h

cpp_no_group_benchmarks_protoc_outputs_proto2 = \
	gogo/cpp_no_group/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc \
	gogo/cpp_no_group/datasets/google_message2/benchmark_message2.pb.cc       \
	gogo/cpp_no_group/datasets/google_message3/benchmark_message3.pb.cc       \
	gogo/cpp_no_group/datasets/google_message3/benchmark_message3_1.pb.cc     \
	gogo/cpp_no_group/datasets/google_message3/benchmark_message3_2.pb.cc     \
	gogo/cpp_no_group/datasets/google_message3/benchmark_message3_3.pb.cc     \
	gogo/cpp_no_group/datasets/google_message3/benchmark_message3_4.pb.cc     \
	gogo/cpp_no_group/datasets/google_message3/benchmark_message3_5.pb.cc     \
	gogo/cpp_no_group/datasets/google_message3/benchmark_message3_6.pb.cc     \
	gogo/cpp_no_group/datasets/google_message3/benchmark_message3_7.pb.cc     \
	gogo/cpp_no_group/datasets/google_message3/benchmark_message3_8.pb.cc     \
	gogo/cpp_no_group/datasets/google_message4/benchmark_message4.pb.cc       \
	gogo/cpp_no_group/datasets/google_message4/benchmark_message4_1.pb.cc     \
	gogo/cpp_no_group/datasets/google_message4/benchmark_message4_2.pb.cc     \
	gogo/cpp_no_group/datasets/google_message4/benchmark_message4_3.pb.cc

cpp_no_group_benchmark_LDADD = $(top_srcdir)/src/libprotobuf.la $(top_srcdir)/third_party/benchmark/src/libbenchmark.a
cpp_no_group_benchmark_SOURCES = gogo/cpp_no_group/cpp_benchmark.cc
cpp_no_group_benchmark_CPPFLAGS = -I$(top_srcdir)/src -I$(srcdir)/gogo/cpp_no_group -I$(top_srcdir)/third_party/benchmark/include
nodist_cpp_no_group_benchmark_SOURCES = \
	$(cpp_no_group_benchmarks_protoc_outputs_proto2)                         \
	$(cpp_no_group_benchmarks_protoc_outputs)                                \
	$(cpp_no_group_benchmarks_protoc_outputs_header)                         \
	$(cpp_no_group_benchmarks_protoc_outputs_proto2_header)

gogo_data = $$(for data in $(all_data); do echo "tmp/gogo_data$${data\#$(srcdir)}"; done | xargs)
protoc_gen_gogoproto_LDADD = $(top_srcdir)/src/libprotobuf.la $(top_srcdir)/src/libprotoc.la
protoc_gen_gogoproto_SOURCES = util/protoc-gen-gogoproto.cc
protoc_gen_gogoproto_CPPFLAGS = -I$(top_srcdir)/src -I$(srcdir)/cpp -I$(srcdir)/util
gogo_data_scrubber_LDADD = $(top_srcdir)/src/libprotobuf.la
gogo_data_scrubber_SOURCES = util/gogo_data_scrubber.cc
gogo_data_scrubber_CPPFLAGS = -I$(top_srcdir)/src -I$(srcdir)/cpp -I$(srcdir)/util
nodist_gogo_data_scrubber_SOURCES = \
	$(benchmarks_protoc_outputs)                                             \
	$(benchmarks_protoc_outputs_proto2)                                      \
	$(benchmarks_protoc_outputs_proto2_header)                               \
	$(benchmarks_protoc_outputs_header)

protoc_gen_proto2_to_proto3_LDADD = $(top_srcdir)/src/libprotobuf.la $(top_srcdir)/src/libprotoc.la
protoc_gen_proto2_to_proto3_SOURCES = util/protoc-gen-proto2_to_proto3.cc
protoc_gen_proto2_to_proto3_CPPFLAGS = -I$(top_srcdir)/src -I$(srcdir)/cpp -I$(srcdir)/util
proto3_data_stripper_LDADD = $(top_srcdir)/src/libprotobuf.la
proto3_data_stripper_SOURCES = util/proto3_data_stripper.cc
proto3_data_stripper_CPPFLAGS = -I$(top_srcdir)/src -I$(srcdir)/cpp -I$(srcdir)/util
nodist_proto3_data_stripper_SOURCES = \
	$(benchmarks_protoc_outputs)                                             \
	$(benchmarks_protoc_outputs_proto2)                                      \
	$(benchmarks_protoc_outputs_proto2_header)                               \
	$(benchmarks_protoc_outputs_header)

full_srcdir = $$(cd $(srcdir) && pwd)
proto3_data = $$(for data in $(all_data); do echo $(full_srcdir)"/tmp/proto3_data$${data\#$(full_srcdir)}"; done | xargs)

############ JS RULE END #############
EXTRA_DIST = \
	$(benchmarks_protoc_inputs_benchmark_wrapper)				\
	$(benchmarks_protoc_inputs)						\
	$(benchmarks_protoc_inputs_proto2)					\
	google_size.proto

MAINTAINERCLEANFILES = \
	Makefile.in

CLEANFILES = \
	$(benchmarks_protoc_outputs)                                             \
	$(benchmarks_protoc_outputs_header)                                      \
	$(benchmarks_protoc_outputs_proto2)                                      \
	$(benchmarks_protoc_outputs_proto2_header)                               \
	initialize_submodule                                                     \
	make_tmp_dir                                                             \
	protoc_middleman                                                         \
	protoc_middleman2                                                        \
	javac_middleman                                                          \
	java-benchmark                                                           \
	python_cpp_proto_library                                                 \
	python-pure-python-benchmark                                             \
	python-cpp-reflection-benchmark                                          \
	python-cpp-generated-code-benchmark                                      \
	go-benchmark                                                             \
	go_protoc_middleman                                                      \
	make_tmp_dir_gogo                                                        \
	gogo_proto_middleman                                                     \
	generate_gogo_data                                                       \
	go_no_group_protoc_middleman                                             \
	go_no_group                                                              \
	go-no-group-benchmark                                                    \
	$(cpp_no_group_benchmarks_protoc_outputs_header)                         \
	$(cpp_no_group_benchmarks_protoc_outputs)                                \
	$(cpp_no_group_benchmarks_protoc_outputs_proto2_header)                  \
	$(cpp_no_group_benchmarks_protoc_outputs_proto2)                         \
	generate_all_gogo_benchmark_code                                         \
	generate-gogo-benchmark-code                                             \
	cpp_no_group_protoc_middleman                                            \
	generate_cpp_no_group_benchmark_code                                     \
	generate_gogo_benchmark_code                                             \
	gogofast_protoc_middleman                                                \
	gogofast                                                                 \
	gogofaster_protoc_middleman                                              \
	gogofaster                                                               \
	gogoslick_protoc_middleman                                               \
	gogoslick                                                                \
	gogo-benchmark                                                           \
	gogo/cpp_no_group/cpp_benchmark.*                                        \
	proto3_proto_middleman                                                   \
	generate_proto3_data                                                     \
	php-benchmark                                                            \
	php-c-benchmark                                                          \
	proto3_middleman_php                                                     \
	pbjs_preparation                                                         \
	pbjs_middleman                                                           \
	pbjs-benchmark                                                           \
	js_preparation                                                           \
	js_middleman                                                             \
	js-benchmark

all: all-am

.SUFFIXES:
.SUFFIXES: .cc .lo .o .obj
$(srcdir)/Makefile.in: # $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu benchmarks/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --gnu benchmarks/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure: # $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4): # $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):
install-binPROGRAMS: $(bin_PROGRAMS)
	@$(NORMAL_INSTALL)
	@list='$(bin_PROGRAMS)'; test -n "$(bindir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(bindir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(bindir)" || exit 1; \
	fi; \
	for p in $$list; do echo "$$p $$p"; done | \
	sed 's/$(EXEEXT)$$//' | \
	while read p p1; do if test -f $$p \
	 || test -f $$p1 \
	  ; then echo "$$p"; echo "$$p"; else :; fi; \
	done | \
	sed -e 'p;s,.*/,,;n;h' \
	    -e 's|.*|.|' \
	    -e 'p;x;s,.*/,,;s/$(EXEEXT)$$//;$(transform);s/$$/$(EXEEXT)/' | \
	sed 'N;N;N;s,\n, ,g' | \
	$(AWK) 'BEGIN { files["."] = ""; dirs["."] = 1 } \
	  { d=$$3; if (dirs[d] != 1) { print "d", d; dirs[d] = 1 } \
	    if ($$2 == $$4) files[d] = files[d] " " $$1; \
	    else { print "f", $$3 "/" $$4, $$1; } } \
	  END { for (d in files) print "f", d, files[d] }' | \
	while read type dir files; do \
	    if test "$$dir" = .; then dir=; else dir=/$$dir; fi; \
	    test -z "$$files" || { \
	    echo " $(INSTALL_PROGRAM_ENV) $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL_PROGRAM) $$files '$(DESTDIR)$(bindir)$$dir'"; \
	    $(INSTALL_PROGRAM_ENV) $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL_PROGRAM) $$files "$(DESTDIR)$(bindir)$$dir" || exit $$?; \
	    } \
	; done

uninstall-binPROGRAMS:
	@$(NORMAL_UNINSTALL)
	@list='$(bin_PROGRAMS)'; test -n "$(bindir)" || list=; \
	files=`for p in $$list; do echo "$$p"; done | \
	  sed -e 'h;s,^.*/,,;s/$(EXEEXT)$$//;$(transform)' \
	      -e 's/$$/$(EXEEXT)/' \
	`; \
	test -n "$$list" || exit 0; \
	echo " ( cd '$(DESTDIR)$(bindir)' && rm -f" $$files ")"; \
	cd "$(DESTDIR)$(bindir)" && rm -f $$files

clean-binPROGRAMS:
	@list='$(bin_PROGRAMS)'; test -n "$$list" || exit 0; \
	echo " rm -f" $$list; \
	rm -f $$list || exit $$?; \
	test -n "$(EXEEXT)" || exit 0; \
	list=`for p in $$list; do echo "$$p"; done | sed 's/$(EXEEXT)$$//'`; \
	echo " rm -f" $$list; \
	rm -f $$list

install-libLTLIBRARIES: $(lib_LTLIBRARIES)
	@$(NORMAL_INSTALL)
	@list='$(lib_LTLIBRARIES)'; test -n "$(libdir)" || list=; \
	list2=; for p in $$list; do \
	  if test -f $$p; then \
	    list2="$$list2 $$p"; \
	  else :; fi; \
	done; \
	test -z "$$list2" || { \
	  echo " $(MKDIR_P) '$(DESTDIR)$(libdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(libdir)" || exit 1; \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 '$(DESTDIR)$(libdir)'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 "$(DESTDIR)$(libdir)"; \
	}

uninstall-libLTLIBRARIES:
	@$(NORMAL_UNINSTALL)
	@list='$(lib_LTLIBRARIES)'; test -n "$(libdir)" || list=; \
	for p in $$list; do \
	  $(am__strip_dir) \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f '$(DESTDIR)$(libdir)/$$f'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f "$(DESTDIR)$(libdir)/$$f"; \
	done

clean-libLTLIBRARIES:
	-test -z "$(lib_LTLIBRARIES)" || rm -f $(lib_LTLIBRARIES)
	@list='$(lib_LTLIBRARIES)'; \
	locs=`for p in $$list; do echo $$p; done | \
	      sed 's|^[^/]*$$|.|; s|/[^/]*$$||; s|$$|/so_locations|' | \
	      sort -u`; \
	test -z "$$locs" || { \
	  echo rm -f $${locs}; \
	  rm -f $${locs}; \
	}
python/$(am__dirstamp):
	@$(MKDIR_P) python
	@: > python/$(am__dirstamp)
python/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) python/$(DEPDIR)
	@: > python/$(DEPDIR)/$(am__dirstamp)
python/libbenchmark_messages_la-python_benchmark_messages.lo:  \
	python/$(am__dirstamp) python/$(DEPDIR)/$(am__dirstamp)
cpp/$(am__dirstamp):
	@$(MKDIR_P) cpp
	@: > cpp/$(am__dirstamp)
cpp/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) cpp/$(DEPDIR)
	@: > cpp/$(DEPDIR)/$(am__dirstamp)
cpp/libbenchmark_messages_la-benchmarks.pb.lo: cpp/$(am__dirstamp) \
	cpp/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message1/proto3/$(am__dirstamp):
	@$(MKDIR_P) cpp/datasets/google_message1/proto3
	@: > cpp/datasets/google_message1/proto3/$(am__dirstamp)
cpp/datasets/google_message1/proto3/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) cpp/datasets/google_message1/proto3/$(DEPDIR)
	@: > cpp/datasets/google_message1/proto3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message1/proto3/libbenchmark_messages_la-benchmark_message1_proto3.pb.lo:  \
	cpp/datasets/google_message1/proto3/$(am__dirstamp) \
	cpp/datasets/google_message1/proto3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message1/proto2/$(am__dirstamp):
	@$(MKDIR_P) cpp/datasets/google_message1/proto2
	@: > cpp/datasets/google_message1/proto2/$(am__dirstamp)
cpp/datasets/google_message1/proto2/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) cpp/datasets/google_message1/proto2/$(DEPDIR)
	@: > cpp/datasets/google_message1/proto2/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message1/proto2/libbenchmark_messages_la-benchmark_message1_proto2.pb.lo:  \
	cpp/datasets/google_message1/proto2/$(am__dirstamp) \
	cpp/datasets/google_message1/proto2/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message2/$(am__dirstamp):
	@$(MKDIR_P) cpp/datasets/google_message2
	@: > cpp/datasets/google_message2/$(am__dirstamp)
cpp/datasets/google_message2/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) cpp/datasets/google_message2/$(DEPDIR)
	@: > cpp/datasets/google_message2/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message2/libbenchmark_messages_la-benchmark_message2.pb.lo:  \
	cpp/datasets/google_message2/$(am__dirstamp) \
	cpp/datasets/google_message2/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/$(am__dirstamp):
	@$(MKDIR_P) cpp/datasets/google_message3
	@: > cpp/datasets/google_message3/$(am__dirstamp)
cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) cpp/datasets/google_message3/$(DEPDIR)
	@: > cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3.pb.lo:  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_1.pb.lo:  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_2.pb.lo:  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_3.pb.lo:  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_4.pb.lo:  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_5.pb.lo:  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_6.pb.lo:  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_7.pb.lo:  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_8.pb.lo:  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message4/$(am__dirstamp):
	@$(MKDIR_P) cpp/datasets/google_message4
	@: > cpp/datasets/google_message4/$(am__dirstamp)
cpp/datasets/google_message4/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) cpp/datasets/google_message4/$(DEPDIR)
	@: > cpp/datasets/google_message4/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message4/libbenchmark_messages_la-benchmark_message4.pb.lo:  \
	cpp/datasets/google_message4/$(am__dirstamp) \
	cpp/datasets/google_message4/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message4/libbenchmark_messages_la-benchmark_message4_1.pb.lo:  \
	cpp/datasets/google_message4/$(am__dirstamp) \
	cpp/datasets/google_message4/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message4/libbenchmark_messages_la-benchmark_message4_2.pb.lo:  \
	cpp/datasets/google_message4/$(am__dirstamp) \
	cpp/datasets/google_message4/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message4/libbenchmark_messages_la-benchmark_message4_3.pb.lo:  \
	cpp/datasets/google_message4/$(am__dirstamp) \
	cpp/datasets/google_message4/$(DEPDIR)/$(am__dirstamp)

libbenchmark_messages.la: $(libbenchmark_messages_la_OBJECTS) $(libbenchmark_messages_la_DEPENDENCIES) $(EXTRA_libbenchmark_messages_la_DEPENDENCIES) 
	$(AM_V_CXXLD)$(libbenchmark_messages_la_LINK) -rpath $(libdir) $(libbenchmark_messages_la_OBJECTS) $(libbenchmark_messages_la_LIBADD) $(LIBS)
cpp/benchmark-cpp_benchmark.$(OBJEXT): cpp/$(am__dirstamp) \
	cpp/$(DEPDIR)/$(am__dirstamp)
cpp/benchmark-benchmarks.pb.$(OBJEXT): cpp/$(am__dirstamp) \
	cpp/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message1/proto3/benchmark-benchmark_message1_proto3.pb.$(OBJEXT):  \
	cpp/datasets/google_message1/proto3/$(am__dirstamp) \
	cpp/datasets/google_message1/proto3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message1/proto2/benchmark-benchmark_message1_proto2.pb.$(OBJEXT):  \
	cpp/datasets/google_message1/proto2/$(am__dirstamp) \
	cpp/datasets/google_message1/proto2/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message2/benchmark-benchmark_message2.pb.$(OBJEXT):  \
	cpp/datasets/google_message2/$(am__dirstamp) \
	cpp/datasets/google_message2/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/benchmark-benchmark_message3.pb.$(OBJEXT):  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/benchmark-benchmark_message3_1.pb.$(OBJEXT):  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/benchmark-benchmark_message3_2.pb.$(OBJEXT):  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/benchmark-benchmark_message3_3.pb.$(OBJEXT):  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/benchmark-benchmark_message3_4.pb.$(OBJEXT):  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/benchmark-benchmark_message3_5.pb.$(OBJEXT):  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/benchmark-benchmark_message3_6.pb.$(OBJEXT):  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/benchmark-benchmark_message3_7.pb.$(OBJEXT):  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/benchmark-benchmark_message3_8.pb.$(OBJEXT):  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message4/benchmark-benchmark_message4.pb.$(OBJEXT):  \
	cpp/datasets/google_message4/$(am__dirstamp) \
	cpp/datasets/google_message4/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message4/benchmark-benchmark_message4_1.pb.$(OBJEXT):  \
	cpp/datasets/google_message4/$(am__dirstamp) \
	cpp/datasets/google_message4/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message4/benchmark-benchmark_message4_2.pb.$(OBJEXT):  \
	cpp/datasets/google_message4/$(am__dirstamp) \
	cpp/datasets/google_message4/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message4/benchmark-benchmark_message4_3.pb.$(OBJEXT):  \
	cpp/datasets/google_message4/$(am__dirstamp) \
	cpp/datasets/google_message4/$(DEPDIR)/$(am__dirstamp)

cpp-benchmark$(EXEEXT): $(cpp_benchmark_OBJECTS) $(cpp_benchmark_DEPENDENCIES) $(EXTRA_cpp_benchmark_DEPENDENCIES) 
	@rm -f cpp-benchmark$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(cpp_benchmark_OBJECTS) $(cpp_benchmark_LDADD) $(LIBS)
gogo/cpp_no_group/$(am__dirstamp):
	@$(MKDIR_P) gogo/cpp_no_group
	@: > gogo/cpp_no_group/$(am__dirstamp)
gogo/cpp_no_group/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) gogo/cpp_no_group/$(DEPDIR)
	@: > gogo/cpp_no_group/$(DEPDIR)/$(am__dirstamp)
gogo/cpp_no_group/cpp_no_group_benchmark-cpp_benchmark.$(OBJEXT):  \
	gogo/cpp_no_group/$(am__dirstamp) \
	gogo/cpp_no_group/$(DEPDIR)/$(am__dirstamp)
gogo/cpp_no_group/datasets/google_message1/proto2/$(am__dirstamp):
	@$(MKDIR_P) gogo/cpp_no_group/datasets/google_message1/proto2
	@: > gogo/cpp_no_group/datasets/google_message1/proto2/$(am__dirstamp)
gogo/cpp_no_group/datasets/google_message1/proto2/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) gogo/cpp_no_group/datasets/google_message1/proto2/$(DEPDIR)
	@: > gogo/cpp_no_group/datasets/google_message1/proto2/$(DEPDIR)/$(am__dirstamp)
gogo/cpp_no_group/datasets/google_message1/proto2/cpp_no_group_benchmark-benchmark_message1_proto2.pb.$(OBJEXT): gogo/cpp_no_group/datasets/google_message1/proto2/$(am__dirstamp) \
	gogo/cpp_no_group/datasets/google_message1/proto2/$(DEPDIR)/$(am__dirstamp)
gogo/cpp_no_group/datasets/google_message2/$(am__dirstamp):
	@$(MKDIR_P) gogo/cpp_no_group/datasets/google_message2
	@: > gogo/cpp_no_group/datasets/google_message2/$(am__dirstamp)
gogo/cpp_no_group/datasets/google_message2/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) gogo/cpp_no_group/datasets/google_message2/$(DEPDIR)
	@: > gogo/cpp_no_group/datasets/google_message2/$(DEPDIR)/$(am__dirstamp)
gogo/cpp_no_group/datasets/google_message2/cpp_no_group_benchmark-benchmark_message2.pb.$(OBJEXT):  \
	gogo/cpp_no_group/datasets/google_message2/$(am__dirstamp) \
	gogo/cpp_no_group/datasets/google_message2/$(DEPDIR)/$(am__dirstamp)
gogo/cpp_no_group/datasets/google_message3/$(am__dirstamp):
	@$(MKDIR_P) gogo/cpp_no_group/datasets/google_message3
	@: > gogo/cpp_no_group/datasets/google_message3/$(am__dirstamp)
gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)
	@: > gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3.pb.$(OBJEXT):  \
	gogo/cpp_no_group/datasets/google_message3/$(am__dirstamp) \
	gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_1.pb.$(OBJEXT):  \
	gogo/cpp_no_group/datasets/google_message3/$(am__dirstamp) \
	gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_2.pb.$(OBJEXT):  \
	gogo/cpp_no_group/datasets/google_message3/$(am__dirstamp) \
	gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_3.pb.$(OBJEXT):  \
	gogo/cpp_no_group/datasets/google_message3/$(am__dirstamp) \
	gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_4.pb.$(OBJEXT):  \
	gogo/cpp_no_group/datasets/google_message3/$(am__dirstamp) \
	gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_5.pb.$(OBJEXT):  \
	gogo/cpp_no_group/datasets/google_message3/$(am__dirstamp) \
	gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_6.pb.$(OBJEXT):  \
	gogo/cpp_no_group/datasets/google_message3/$(am__dirstamp) \
	gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_7.pb.$(OBJEXT):  \
	gogo/cpp_no_group/datasets/google_message3/$(am__dirstamp) \
	gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_8.pb.$(OBJEXT):  \
	gogo/cpp_no_group/datasets/google_message3/$(am__dirstamp) \
	gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
gogo/cpp_no_group/datasets/google_message4/$(am__dirstamp):
	@$(MKDIR_P) gogo/cpp_no_group/datasets/google_message4
	@: > gogo/cpp_no_group/datasets/google_message4/$(am__dirstamp)
gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)
	@: > gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/$(am__dirstamp)
gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4.pb.$(OBJEXT):  \
	gogo/cpp_no_group/datasets/google_message4/$(am__dirstamp) \
	gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/$(am__dirstamp)
gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_1.pb.$(OBJEXT):  \
	gogo/cpp_no_group/datasets/google_message4/$(am__dirstamp) \
	gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/$(am__dirstamp)
gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_2.pb.$(OBJEXT):  \
	gogo/cpp_no_group/datasets/google_message4/$(am__dirstamp) \
	gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/$(am__dirstamp)
gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_3.pb.$(OBJEXT):  \
	gogo/cpp_no_group/datasets/google_message4/$(am__dirstamp) \
	gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/$(am__dirstamp)
gogo/cpp_no_group/cpp_no_group_benchmark-benchmarks.pb.$(OBJEXT):  \
	gogo/cpp_no_group/$(am__dirstamp) \
	gogo/cpp_no_group/$(DEPDIR)/$(am__dirstamp)
gogo/cpp_no_group/datasets/google_message1/proto3/$(am__dirstamp):
	@$(MKDIR_P) gogo/cpp_no_group/datasets/google_message1/proto3
	@: > gogo/cpp_no_group/datasets/google_message1/proto3/$(am__dirstamp)
gogo/cpp_no_group/datasets/google_message1/proto3/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) gogo/cpp_no_group/datasets/google_message1/proto3/$(DEPDIR)
	@: > gogo/cpp_no_group/datasets/google_message1/proto3/$(DEPDIR)/$(am__dirstamp)
gogo/cpp_no_group/datasets/google_message1/proto3/cpp_no_group_benchmark-benchmark_message1_proto3.pb.$(OBJEXT): gogo/cpp_no_group/datasets/google_message1/proto3/$(am__dirstamp) \
	gogo/cpp_no_group/datasets/google_message1/proto3/$(DEPDIR)/$(am__dirstamp)

cpp-no-group-benchmark$(EXEEXT): $(cpp_no_group_benchmark_OBJECTS) $(cpp_no_group_benchmark_DEPENDENCIES) $(EXTRA_cpp_no_group_benchmark_DEPENDENCIES) 
	@rm -f cpp-no-group-benchmark$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(cpp_no_group_benchmark_OBJECTS) $(cpp_no_group_benchmark_LDADD) $(LIBS)
util/$(am__dirstamp):
	@$(MKDIR_P) util
	@: > util/$(am__dirstamp)
util/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) util/$(DEPDIR)
	@: > util/$(DEPDIR)/$(am__dirstamp)
util/gogo_data_scrubber-gogo_data_scrubber.$(OBJEXT):  \
	util/$(am__dirstamp) util/$(DEPDIR)/$(am__dirstamp)
cpp/gogo_data_scrubber-benchmarks.pb.$(OBJEXT): cpp/$(am__dirstamp) \
	cpp/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message1/proto3/gogo_data_scrubber-benchmark_message1_proto3.pb.$(OBJEXT):  \
	cpp/datasets/google_message1/proto3/$(am__dirstamp) \
	cpp/datasets/google_message1/proto3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message1/proto2/gogo_data_scrubber-benchmark_message1_proto2.pb.$(OBJEXT):  \
	cpp/datasets/google_message1/proto2/$(am__dirstamp) \
	cpp/datasets/google_message1/proto2/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message2/gogo_data_scrubber-benchmark_message2.pb.$(OBJEXT):  \
	cpp/datasets/google_message2/$(am__dirstamp) \
	cpp/datasets/google_message2/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3.pb.$(OBJEXT):  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_1.pb.$(OBJEXT):  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_2.pb.$(OBJEXT):  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_3.pb.$(OBJEXT):  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_4.pb.$(OBJEXT):  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_5.pb.$(OBJEXT):  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_6.pb.$(OBJEXT):  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_7.pb.$(OBJEXT):  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_8.pb.$(OBJEXT):  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4.pb.$(OBJEXT):  \
	cpp/datasets/google_message4/$(am__dirstamp) \
	cpp/datasets/google_message4/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_1.pb.$(OBJEXT):  \
	cpp/datasets/google_message4/$(am__dirstamp) \
	cpp/datasets/google_message4/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_2.pb.$(OBJEXT):  \
	cpp/datasets/google_message4/$(am__dirstamp) \
	cpp/datasets/google_message4/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_3.pb.$(OBJEXT):  \
	cpp/datasets/google_message4/$(am__dirstamp) \
	cpp/datasets/google_message4/$(DEPDIR)/$(am__dirstamp)

gogo-data-scrubber$(EXEEXT): $(gogo_data_scrubber_OBJECTS) $(gogo_data_scrubber_DEPENDENCIES) $(EXTRA_gogo_data_scrubber_DEPENDENCIES) 
	@rm -f gogo-data-scrubber$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(gogo_data_scrubber_OBJECTS) $(gogo_data_scrubber_LDADD) $(LIBS)
util/proto3_data_stripper-proto3_data_stripper.$(OBJEXT):  \
	util/$(am__dirstamp) util/$(DEPDIR)/$(am__dirstamp)
cpp/proto3_data_stripper-benchmarks.pb.$(OBJEXT): cpp/$(am__dirstamp) \
	cpp/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message1/proto3/proto3_data_stripper-benchmark_message1_proto3.pb.$(OBJEXT):  \
	cpp/datasets/google_message1/proto3/$(am__dirstamp) \
	cpp/datasets/google_message1/proto3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message1/proto2/proto3_data_stripper-benchmark_message1_proto2.pb.$(OBJEXT):  \
	cpp/datasets/google_message1/proto2/$(am__dirstamp) \
	cpp/datasets/google_message1/proto2/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message2/proto3_data_stripper-benchmark_message2.pb.$(OBJEXT):  \
	cpp/datasets/google_message2/$(am__dirstamp) \
	cpp/datasets/google_message2/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3.pb.$(OBJEXT):  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_1.pb.$(OBJEXT):  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_2.pb.$(OBJEXT):  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_3.pb.$(OBJEXT):  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_4.pb.$(OBJEXT):  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_5.pb.$(OBJEXT):  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_6.pb.$(OBJEXT):  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_7.pb.$(OBJEXT):  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_8.pb.$(OBJEXT):  \
	cpp/datasets/google_message3/$(am__dirstamp) \
	cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4.pb.$(OBJEXT):  \
	cpp/datasets/google_message4/$(am__dirstamp) \
	cpp/datasets/google_message4/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_1.pb.$(OBJEXT):  \
	cpp/datasets/google_message4/$(am__dirstamp) \
	cpp/datasets/google_message4/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_2.pb.$(OBJEXT):  \
	cpp/datasets/google_message4/$(am__dirstamp) \
	cpp/datasets/google_message4/$(DEPDIR)/$(am__dirstamp)
cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_3.pb.$(OBJEXT):  \
	cpp/datasets/google_message4/$(am__dirstamp) \
	cpp/datasets/google_message4/$(DEPDIR)/$(am__dirstamp)

proto3-data-stripper$(EXEEXT): $(proto3_data_stripper_OBJECTS) $(proto3_data_stripper_DEPENDENCIES) $(EXTRA_proto3_data_stripper_DEPENDENCIES) 
	@rm -f proto3-data-stripper$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(proto3_data_stripper_OBJECTS) $(proto3_data_stripper_LDADD) $(LIBS)
util/protoc_gen_gogoproto-protoc-gen-gogoproto.$(OBJEXT):  \
	util/$(am__dirstamp) util/$(DEPDIR)/$(am__dirstamp)

protoc-gen-gogoproto$(EXEEXT): $(protoc_gen_gogoproto_OBJECTS) $(protoc_gen_gogoproto_DEPENDENCIES) $(EXTRA_protoc_gen_gogoproto_DEPENDENCIES) 
	@rm -f protoc-gen-gogoproto$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(protoc_gen_gogoproto_OBJECTS) $(protoc_gen_gogoproto_LDADD) $(LIBS)
util/protoc_gen_proto2_to_proto3-protoc-gen-proto2_to_proto3.$(OBJEXT):  \
	util/$(am__dirstamp) util/$(DEPDIR)/$(am__dirstamp)

protoc-gen-proto2_to_proto3$(EXEEXT): $(protoc_gen_proto2_to_proto3_OBJECTS) $(protoc_gen_proto2_to_proto3_DEPENDENCIES) $(EXTRA_protoc_gen_proto2_to_proto3_DEPENDENCIES) 
	@rm -f protoc-gen-proto2_to_proto3$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(protoc_gen_proto2_to_proto3_OBJECTS) $(protoc_gen_proto2_to_proto3_LDADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)
	-rm -f cpp/*.$(OBJEXT)
	-rm -f cpp/*.lo
	-rm -f cpp/datasets/google_message1/proto2/*.$(OBJEXT)
	-rm -f cpp/datasets/google_message1/proto2/*.lo
	-rm -f cpp/datasets/google_message1/proto3/*.$(OBJEXT)
	-rm -f cpp/datasets/google_message1/proto3/*.lo
	-rm -f cpp/datasets/google_message2/*.$(OBJEXT)
	-rm -f cpp/datasets/google_message2/*.lo
	-rm -f cpp/datasets/google_message3/*.$(OBJEXT)
	-rm -f cpp/datasets/google_message3/*.lo
	-rm -f cpp/datasets/google_message4/*.$(OBJEXT)
	-rm -f cpp/datasets/google_message4/*.lo
	-rm -f gogo/cpp_no_group/*.$(OBJEXT)
	-rm -f gogo/cpp_no_group/datasets/google_message1/proto2/*.$(OBJEXT)
	-rm -f gogo/cpp_no_group/datasets/google_message1/proto3/*.$(OBJEXT)
	-rm -f gogo/cpp_no_group/datasets/google_message2/*.$(OBJEXT)
	-rm -f gogo/cpp_no_group/datasets/google_message3/*.$(OBJEXT)
	-rm -f gogo/cpp_no_group/datasets/google_message4/*.$(OBJEXT)
	-rm -f python/*.$(OBJEXT)
	-rm -f python/*.lo
	-rm -f util/*.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

include cpp/$(DEPDIR)/benchmark-benchmarks.pb.Po # am--include-marker
include cpp/$(DEPDIR)/benchmark-cpp_benchmark.Po # am--include-marker
include cpp/$(DEPDIR)/gogo_data_scrubber-benchmarks.pb.Po # am--include-marker
include cpp/$(DEPDIR)/libbenchmark_messages_la-benchmarks.pb.Plo # am--include-marker
include cpp/$(DEPDIR)/proto3_data_stripper-benchmarks.pb.Po # am--include-marker
include cpp/datasets/google_message1/proto2/$(DEPDIR)/benchmark-benchmark_message1_proto2.pb.Po # am--include-marker
include cpp/datasets/google_message1/proto2/$(DEPDIR)/gogo_data_scrubber-benchmark_message1_proto2.pb.Po # am--include-marker
include cpp/datasets/google_message1/proto2/$(DEPDIR)/libbenchmark_messages_la-benchmark_message1_proto2.pb.Plo # am--include-marker
include cpp/datasets/google_message1/proto2/$(DEPDIR)/proto3_data_stripper-benchmark_message1_proto2.pb.Po # am--include-marker
include cpp/datasets/google_message1/proto3/$(DEPDIR)/benchmark-benchmark_message1_proto3.pb.Po # am--include-marker
include cpp/datasets/google_message1/proto3/$(DEPDIR)/gogo_data_scrubber-benchmark_message1_proto3.pb.Po # am--include-marker
include cpp/datasets/google_message1/proto3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message1_proto3.pb.Plo # am--include-marker
include cpp/datasets/google_message1/proto3/$(DEPDIR)/proto3_data_stripper-benchmark_message1_proto3.pb.Po # am--include-marker
include cpp/datasets/google_message2/$(DEPDIR)/benchmark-benchmark_message2.pb.Po # am--include-marker
include cpp/datasets/google_message2/$(DEPDIR)/gogo_data_scrubber-benchmark_message2.pb.Po # am--include-marker
include cpp/datasets/google_message2/$(DEPDIR)/libbenchmark_messages_la-benchmark_message2.pb.Plo # am--include-marker
include cpp/datasets/google_message2/$(DEPDIR)/proto3_data_stripper-benchmark_message2.pb.Po # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3.pb.Po # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_1.pb.Po # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_2.pb.Po # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_3.pb.Po # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_4.pb.Po # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_5.pb.Po # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_6.pb.Po # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_7.pb.Po # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_8.pb.Po # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3.pb.Po # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_1.pb.Po # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_2.pb.Po # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_3.pb.Po # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_4.pb.Po # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_5.pb.Po # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_6.pb.Po # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_7.pb.Po # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_8.pb.Po # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3.pb.Plo # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_1.pb.Plo # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_2.pb.Plo # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_3.pb.Plo # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_4.pb.Plo # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_5.pb.Plo # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_6.pb.Plo # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_7.pb.Plo # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_8.pb.Plo # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3.pb.Po # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_1.pb.Po # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_2.pb.Po # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_3.pb.Po # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_4.pb.Po # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_5.pb.Po # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_6.pb.Po # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_7.pb.Po # am--include-marker
include cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_8.pb.Po # am--include-marker
include cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4.pb.Po # am--include-marker
include cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4_1.pb.Po # am--include-marker
include cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4_2.pb.Po # am--include-marker
include cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4_3.pb.Po # am--include-marker
include cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4.pb.Po # am--include-marker
include cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4_1.pb.Po # am--include-marker
include cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4_2.pb.Po # am--include-marker
include cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4_3.pb.Po # am--include-marker
include cpp/datasets/google_message4/$(DEPDIR)/libbenchmark_messages_la-benchmark_message4.pb.Plo # am--include-marker
include cpp/datasets/google_message4/$(DEPDIR)/libbenchmark_messages_la-benchmark_message4_1.pb.Plo # am--include-marker
include cpp/datasets/google_message4/$(DEPDIR)/libbenchmark_messages_la-benchmark_message4_2.pb.Plo # am--include-marker
include cpp/datasets/google_message4/$(DEPDIR)/libbenchmark_messages_la-benchmark_message4_3.pb.Plo # am--include-marker
include cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4.pb.Po # am--include-marker
include cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4_1.pb.Po # am--include-marker
include cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4_2.pb.Po # am--include-marker
include cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4_3.pb.Po # am--include-marker
include gogo/cpp_no_group/$(DEPDIR)/cpp_no_group_benchmark-benchmarks.pb.Po # am--include-marker
include gogo/cpp_no_group/$(DEPDIR)/cpp_no_group_benchmark-cpp_benchmark.Po # am--include-marker
include gogo/cpp_no_group/datasets/google_message1/proto2/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message1_proto2.pb.Po # am--include-marker
include gogo/cpp_no_group/datasets/google_message1/proto3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message1_proto3.pb.Po # am--include-marker
include gogo/cpp_no_group/datasets/google_message2/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message2.pb.Po # am--include-marker
include gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3.pb.Po # am--include-marker
include gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_1.pb.Po # am--include-marker
include gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_2.pb.Po # am--include-marker
include gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_3.pb.Po # am--include-marker
include gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_4.pb.Po # am--include-marker
include gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_5.pb.Po # am--include-marker
include gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_6.pb.Po # am--include-marker
include gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_7.pb.Po # am--include-marker
include gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_8.pb.Po # am--include-marker
include gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4.pb.Po # am--include-marker
include gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4_1.pb.Po # am--include-marker
include gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4_2.pb.Po # am--include-marker
include gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4_3.pb.Po # am--include-marker
include python/$(DEPDIR)/libbenchmark_messages_la-python_benchmark_messages.Plo # am--include-marker
include util/$(DEPDIR)/gogo_data_scrubber-gogo_data_scrubber.Po # am--include-marker
include util/$(DEPDIR)/proto3_data_stripper-proto3_data_stripper.Po # am--include-marker
include util/$(DEPDIR)/protoc_gen_gogoproto-protoc-gen-gogoproto.Po # am--include-marker
include util/$(DEPDIR)/protoc_gen_proto2_to_proto3-protoc-gen-proto2_to_proto3.Po # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.cc.o:
	$(AM_V_CXX)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
	$(CXXCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
	$(am__mv) $$depbase.Tpo $$depbase.Po
#	$(AM_V_CXX)source='$<' object='$@' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXXCOMPILE) -c -o $@ $<

.cc.obj:
	$(AM_V_CXX)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
	$(CXXCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
	$(am__mv) $$depbase.Tpo $$depbase.Po
#	$(AM_V_CXX)source='$<' object='$@' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXXCOMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.cc.lo:
	$(AM_V_CXX)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
	$(LTCXXCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
	$(am__mv) $$depbase.Tpo $$depbase.Plo
#	$(AM_V_CXX)source='$<' object='$@' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LTCXXCOMPILE) -c -o $@ $<

python/libbenchmark_messages_la-python_benchmark_messages.lo: python/python_benchmark_messages.cc
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT python/libbenchmark_messages_la-python_benchmark_messages.lo -MD -MP -MF python/$(DEPDIR)/libbenchmark_messages_la-python_benchmark_messages.Tpo -c -o python/libbenchmark_messages_la-python_benchmark_messages.lo `test -f 'python/python_benchmark_messages.cc' || echo '$(srcdir)/'`python/python_benchmark_messages.cc
	$(AM_V_at)$(am__mv) python/$(DEPDIR)/libbenchmark_messages_la-python_benchmark_messages.Tpo python/$(DEPDIR)/libbenchmark_messages_la-python_benchmark_messages.Plo
#	$(AM_V_CXX)source='python/python_benchmark_messages.cc' object='python/libbenchmark_messages_la-python_benchmark_messages.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o python/libbenchmark_messages_la-python_benchmark_messages.lo `test -f 'python/python_benchmark_messages.cc' || echo '$(srcdir)/'`python/python_benchmark_messages.cc

cpp/libbenchmark_messages_la-benchmarks.pb.lo: cpp/benchmarks.pb.cc
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/libbenchmark_messages_la-benchmarks.pb.lo -MD -MP -MF cpp/$(DEPDIR)/libbenchmark_messages_la-benchmarks.pb.Tpo -c -o cpp/libbenchmark_messages_la-benchmarks.pb.lo `test -f 'cpp/benchmarks.pb.cc' || echo '$(srcdir)/'`cpp/benchmarks.pb.cc
	$(AM_V_at)$(am__mv) cpp/$(DEPDIR)/libbenchmark_messages_la-benchmarks.pb.Tpo cpp/$(DEPDIR)/libbenchmark_messages_la-benchmarks.pb.Plo
#	$(AM_V_CXX)source='cpp/benchmarks.pb.cc' object='cpp/libbenchmark_messages_la-benchmarks.pb.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/libbenchmark_messages_la-benchmarks.pb.lo `test -f 'cpp/benchmarks.pb.cc' || echo '$(srcdir)/'`cpp/benchmarks.pb.cc

cpp/datasets/google_message1/proto3/libbenchmark_messages_la-benchmark_message1_proto3.pb.lo: cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message1/proto3/libbenchmark_messages_la-benchmark_message1_proto3.pb.lo -MD -MP -MF cpp/datasets/google_message1/proto3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message1_proto3.pb.Tpo -c -o cpp/datasets/google_message1/proto3/libbenchmark_messages_la-benchmark_message1_proto3.pb.lo `test -f 'cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message1/proto3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message1_proto3.pb.Tpo cpp/datasets/google_message1/proto3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message1_proto3.pb.Plo
#	$(AM_V_CXX)source='cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc' object='cpp/datasets/google_message1/proto3/libbenchmark_messages_la-benchmark_message1_proto3.pb.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message1/proto3/libbenchmark_messages_la-benchmark_message1_proto3.pb.lo `test -f 'cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc

cpp/datasets/google_message1/proto2/libbenchmark_messages_la-benchmark_message1_proto2.pb.lo: cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message1/proto2/libbenchmark_messages_la-benchmark_message1_proto2.pb.lo -MD -MP -MF cpp/datasets/google_message1/proto2/$(DEPDIR)/libbenchmark_messages_la-benchmark_message1_proto2.pb.Tpo -c -o cpp/datasets/google_message1/proto2/libbenchmark_messages_la-benchmark_message1_proto2.pb.lo `test -f 'cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message1/proto2/$(DEPDIR)/libbenchmark_messages_la-benchmark_message1_proto2.pb.Tpo cpp/datasets/google_message1/proto2/$(DEPDIR)/libbenchmark_messages_la-benchmark_message1_proto2.pb.Plo
#	$(AM_V_CXX)source='cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc' object='cpp/datasets/google_message1/proto2/libbenchmark_messages_la-benchmark_message1_proto2.pb.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message1/proto2/libbenchmark_messages_la-benchmark_message1_proto2.pb.lo `test -f 'cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc

cpp/datasets/google_message2/libbenchmark_messages_la-benchmark_message2.pb.lo: cpp/datasets/google_message2/benchmark_message2.pb.cc
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message2/libbenchmark_messages_la-benchmark_message2.pb.lo -MD -MP -MF cpp/datasets/google_message2/$(DEPDIR)/libbenchmark_messages_la-benchmark_message2.pb.Tpo -c -o cpp/datasets/google_message2/libbenchmark_messages_la-benchmark_message2.pb.lo `test -f 'cpp/datasets/google_message2/benchmark_message2.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message2/benchmark_message2.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message2/$(DEPDIR)/libbenchmark_messages_la-benchmark_message2.pb.Tpo cpp/datasets/google_message2/$(DEPDIR)/libbenchmark_messages_la-benchmark_message2.pb.Plo
#	$(AM_V_CXX)source='cpp/datasets/google_message2/benchmark_message2.pb.cc' object='cpp/datasets/google_message2/libbenchmark_messages_la-benchmark_message2.pb.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message2/libbenchmark_messages_la-benchmark_message2.pb.lo `test -f 'cpp/datasets/google_message2/benchmark_message2.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message2/benchmark_message2.pb.cc

cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3.pb.lo: cpp/datasets/google_message3/benchmark_message3.pb.cc
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3.pb.lo -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3.pb.Tpo -c -o cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3.pb.lo `test -f 'cpp/datasets/google_message3/benchmark_message3.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3.pb.Plo
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3.pb.cc' object='cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3.pb.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3.pb.lo `test -f 'cpp/datasets/google_message3/benchmark_message3.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3.pb.cc

cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_1.pb.lo: cpp/datasets/google_message3/benchmark_message3_1.pb.cc
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_1.pb.lo -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_1.pb.Tpo -c -o cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_1.pb.lo `test -f 'cpp/datasets/google_message3/benchmark_message3_1.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_1.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_1.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_1.pb.Plo
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_1.pb.cc' object='cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_1.pb.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_1.pb.lo `test -f 'cpp/datasets/google_message3/benchmark_message3_1.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_1.pb.cc

cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_2.pb.lo: cpp/datasets/google_message3/benchmark_message3_2.pb.cc
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_2.pb.lo -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_2.pb.Tpo -c -o cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_2.pb.lo `test -f 'cpp/datasets/google_message3/benchmark_message3_2.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_2.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_2.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_2.pb.Plo
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_2.pb.cc' object='cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_2.pb.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_2.pb.lo `test -f 'cpp/datasets/google_message3/benchmark_message3_2.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_2.pb.cc

cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_3.pb.lo: cpp/datasets/google_message3/benchmark_message3_3.pb.cc
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_3.pb.lo -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_3.pb.Tpo -c -o cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_3.pb.lo `test -f 'cpp/datasets/google_message3/benchmark_message3_3.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_3.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_3.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_3.pb.Plo
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_3.pb.cc' object='cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_3.pb.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_3.pb.lo `test -f 'cpp/datasets/google_message3/benchmark_message3_3.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_3.pb.cc

cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_4.pb.lo: cpp/datasets/google_message3/benchmark_message3_4.pb.cc
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_4.pb.lo -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_4.pb.Tpo -c -o cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_4.pb.lo `test -f 'cpp/datasets/google_message3/benchmark_message3_4.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_4.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_4.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_4.pb.Plo
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_4.pb.cc' object='cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_4.pb.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_4.pb.lo `test -f 'cpp/datasets/google_message3/benchmark_message3_4.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_4.pb.cc

cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_5.pb.lo: cpp/datasets/google_message3/benchmark_message3_5.pb.cc
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_5.pb.lo -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_5.pb.Tpo -c -o cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_5.pb.lo `test -f 'cpp/datasets/google_message3/benchmark_message3_5.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_5.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_5.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_5.pb.Plo
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_5.pb.cc' object='cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_5.pb.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_5.pb.lo `test -f 'cpp/datasets/google_message3/benchmark_message3_5.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_5.pb.cc

cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_6.pb.lo: cpp/datasets/google_message3/benchmark_message3_6.pb.cc
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_6.pb.lo -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_6.pb.Tpo -c -o cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_6.pb.lo `test -f 'cpp/datasets/google_message3/benchmark_message3_6.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_6.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_6.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_6.pb.Plo
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_6.pb.cc' object='cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_6.pb.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_6.pb.lo `test -f 'cpp/datasets/google_message3/benchmark_message3_6.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_6.pb.cc

cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_7.pb.lo: cpp/datasets/google_message3/benchmark_message3_7.pb.cc
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_7.pb.lo -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_7.pb.Tpo -c -o cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_7.pb.lo `test -f 'cpp/datasets/google_message3/benchmark_message3_7.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_7.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_7.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_7.pb.Plo
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_7.pb.cc' object='cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_7.pb.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_7.pb.lo `test -f 'cpp/datasets/google_message3/benchmark_message3_7.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_7.pb.cc

cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_8.pb.lo: cpp/datasets/google_message3/benchmark_message3_8.pb.cc
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_8.pb.lo -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_8.pb.Tpo -c -o cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_8.pb.lo `test -f 'cpp/datasets/google_message3/benchmark_message3_8.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_8.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_8.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_8.pb.Plo
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_8.pb.cc' object='cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_8.pb.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/libbenchmark_messages_la-benchmark_message3_8.pb.lo `test -f 'cpp/datasets/google_message3/benchmark_message3_8.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_8.pb.cc

cpp/datasets/google_message4/libbenchmark_messages_la-benchmark_message4.pb.lo: cpp/datasets/google_message4/benchmark_message4.pb.cc
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message4/libbenchmark_messages_la-benchmark_message4.pb.lo -MD -MP -MF cpp/datasets/google_message4/$(DEPDIR)/libbenchmark_messages_la-benchmark_message4.pb.Tpo -c -o cpp/datasets/google_message4/libbenchmark_messages_la-benchmark_message4.pb.lo `test -f 'cpp/datasets/google_message4/benchmark_message4.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message4/benchmark_message4.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message4/$(DEPDIR)/libbenchmark_messages_la-benchmark_message4.pb.Tpo cpp/datasets/google_message4/$(DEPDIR)/libbenchmark_messages_la-benchmark_message4.pb.Plo
#	$(AM_V_CXX)source='cpp/datasets/google_message4/benchmark_message4.pb.cc' object='cpp/datasets/google_message4/libbenchmark_messages_la-benchmark_message4.pb.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message4/libbenchmark_messages_la-benchmark_message4.pb.lo `test -f 'cpp/datasets/google_message4/benchmark_message4.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message4/benchmark_message4.pb.cc

cpp/datasets/google_message4/libbenchmark_messages_la-benchmark_message4_1.pb.lo: cpp/datasets/google_message4/benchmark_message4_1.pb.cc
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message4/libbenchmark_messages_la-benchmark_message4_1.pb.lo -MD -MP -MF cpp/datasets/google_message4/$(DEPDIR)/libbenchmark_messages_la-benchmark_message4_1.pb.Tpo -c -o cpp/datasets/google_message4/libbenchmark_messages_la-benchmark_message4_1.pb.lo `test -f 'cpp/datasets/google_message4/benchmark_message4_1.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message4/benchmark_message4_1.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message4/$(DEPDIR)/libbenchmark_messages_la-benchmark_message4_1.pb.Tpo cpp/datasets/google_message4/$(DEPDIR)/libbenchmark_messages_la-benchmark_message4_1.pb.Plo
#	$(AM_V_CXX)source='cpp/datasets/google_message4/benchmark_message4_1.pb.cc' object='cpp/datasets/google_message4/libbenchmark_messages_la-benchmark_message4_1.pb.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message4/libbenchmark_messages_la-benchmark_message4_1.pb.lo `test -f 'cpp/datasets/google_message4/benchmark_message4_1.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message4/benchmark_message4_1.pb.cc

cpp/datasets/google_message4/libbenchmark_messages_la-benchmark_message4_2.pb.lo: cpp/datasets/google_message4/benchmark_message4_2.pb.cc
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message4/libbenchmark_messages_la-benchmark_message4_2.pb.lo -MD -MP -MF cpp/datasets/google_message4/$(DEPDIR)/libbenchmark_messages_la-benchmark_message4_2.pb.Tpo -c -o cpp/datasets/google_message4/libbenchmark_messages_la-benchmark_message4_2.pb.lo `test -f 'cpp/datasets/google_message4/benchmark_message4_2.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message4/benchmark_message4_2.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message4/$(DEPDIR)/libbenchmark_messages_la-benchmark_message4_2.pb.Tpo cpp/datasets/google_message4/$(DEPDIR)/libbenchmark_messages_la-benchmark_message4_2.pb.Plo
#	$(AM_V_CXX)source='cpp/datasets/google_message4/benchmark_message4_2.pb.cc' object='cpp/datasets/google_message4/libbenchmark_messages_la-benchmark_message4_2.pb.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message4/libbenchmark_messages_la-benchmark_message4_2.pb.lo `test -f 'cpp/datasets/google_message4/benchmark_message4_2.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message4/benchmark_message4_2.pb.cc

cpp/datasets/google_message4/libbenchmark_messages_la-benchmark_message4_3.pb.lo: cpp/datasets/google_message4/benchmark_message4_3.pb.cc
	$(AM_V_CXX)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message4/libbenchmark_messages_la-benchmark_message4_3.pb.lo -MD -MP -MF cpp/datasets/google_message4/$(DEPDIR)/libbenchmark_messages_la-benchmark_message4_3.pb.Tpo -c -o cpp/datasets/google_message4/libbenchmark_messages_la-benchmark_message4_3.pb.lo `test -f 'cpp/datasets/google_message4/benchmark_message4_3.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message4/benchmark_message4_3.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message4/$(DEPDIR)/libbenchmark_messages_la-benchmark_message4_3.pb.Tpo cpp/datasets/google_message4/$(DEPDIR)/libbenchmark_messages_la-benchmark_message4_3.pb.Plo
#	$(AM_V_CXX)source='cpp/datasets/google_message4/benchmark_message4_3.pb.cc' object='cpp/datasets/google_message4/libbenchmark_messages_la-benchmark_message4_3.pb.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libbenchmark_messages_la_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message4/libbenchmark_messages_la-benchmark_message4_3.pb.lo `test -f 'cpp/datasets/google_message4/benchmark_message4_3.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message4/benchmark_message4_3.pb.cc

cpp/benchmark-cpp_benchmark.o: cpp/cpp_benchmark.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/benchmark-cpp_benchmark.o -MD -MP -MF cpp/$(DEPDIR)/benchmark-cpp_benchmark.Tpo -c -o cpp/benchmark-cpp_benchmark.o `test -f 'cpp/cpp_benchmark.cc' || echo '$(srcdir)/'`cpp/cpp_benchmark.cc
	$(AM_V_at)$(am__mv) cpp/$(DEPDIR)/benchmark-cpp_benchmark.Tpo cpp/$(DEPDIR)/benchmark-cpp_benchmark.Po
#	$(AM_V_CXX)source='cpp/cpp_benchmark.cc' object='cpp/benchmark-cpp_benchmark.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/benchmark-cpp_benchmark.o `test -f 'cpp/cpp_benchmark.cc' || echo '$(srcdir)/'`cpp/cpp_benchmark.cc

cpp/benchmark-cpp_benchmark.obj: cpp/cpp_benchmark.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/benchmark-cpp_benchmark.obj -MD -MP -MF cpp/$(DEPDIR)/benchmark-cpp_benchmark.Tpo -c -o cpp/benchmark-cpp_benchmark.obj `if test -f 'cpp/cpp_benchmark.cc'; then $(CYGPATH_W) 'cpp/cpp_benchmark.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/cpp_benchmark.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/$(DEPDIR)/benchmark-cpp_benchmark.Tpo cpp/$(DEPDIR)/benchmark-cpp_benchmark.Po
#	$(AM_V_CXX)source='cpp/cpp_benchmark.cc' object='cpp/benchmark-cpp_benchmark.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/benchmark-cpp_benchmark.obj `if test -f 'cpp/cpp_benchmark.cc'; then $(CYGPATH_W) 'cpp/cpp_benchmark.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/cpp_benchmark.cc'; fi`

cpp/benchmark-benchmarks.pb.o: cpp/benchmarks.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/benchmark-benchmarks.pb.o -MD -MP -MF cpp/$(DEPDIR)/benchmark-benchmarks.pb.Tpo -c -o cpp/benchmark-benchmarks.pb.o `test -f 'cpp/benchmarks.pb.cc' || echo '$(srcdir)/'`cpp/benchmarks.pb.cc
	$(AM_V_at)$(am__mv) cpp/$(DEPDIR)/benchmark-benchmarks.pb.Tpo cpp/$(DEPDIR)/benchmark-benchmarks.pb.Po
#	$(AM_V_CXX)source='cpp/benchmarks.pb.cc' object='cpp/benchmark-benchmarks.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/benchmark-benchmarks.pb.o `test -f 'cpp/benchmarks.pb.cc' || echo '$(srcdir)/'`cpp/benchmarks.pb.cc

cpp/benchmark-benchmarks.pb.obj: cpp/benchmarks.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/benchmark-benchmarks.pb.obj -MD -MP -MF cpp/$(DEPDIR)/benchmark-benchmarks.pb.Tpo -c -o cpp/benchmark-benchmarks.pb.obj `if test -f 'cpp/benchmarks.pb.cc'; then $(CYGPATH_W) 'cpp/benchmarks.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/benchmarks.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/$(DEPDIR)/benchmark-benchmarks.pb.Tpo cpp/$(DEPDIR)/benchmark-benchmarks.pb.Po
#	$(AM_V_CXX)source='cpp/benchmarks.pb.cc' object='cpp/benchmark-benchmarks.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/benchmark-benchmarks.pb.obj `if test -f 'cpp/benchmarks.pb.cc'; then $(CYGPATH_W) 'cpp/benchmarks.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/benchmarks.pb.cc'; fi`

cpp/datasets/google_message1/proto3/benchmark-benchmark_message1_proto3.pb.o: cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message1/proto3/benchmark-benchmark_message1_proto3.pb.o -MD -MP -MF cpp/datasets/google_message1/proto3/$(DEPDIR)/benchmark-benchmark_message1_proto3.pb.Tpo -c -o cpp/datasets/google_message1/proto3/benchmark-benchmark_message1_proto3.pb.o `test -f 'cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message1/proto3/$(DEPDIR)/benchmark-benchmark_message1_proto3.pb.Tpo cpp/datasets/google_message1/proto3/$(DEPDIR)/benchmark-benchmark_message1_proto3.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc' object='cpp/datasets/google_message1/proto3/benchmark-benchmark_message1_proto3.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message1/proto3/benchmark-benchmark_message1_proto3.pb.o `test -f 'cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc

cpp/datasets/google_message1/proto3/benchmark-benchmark_message1_proto3.pb.obj: cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message1/proto3/benchmark-benchmark_message1_proto3.pb.obj -MD -MP -MF cpp/datasets/google_message1/proto3/$(DEPDIR)/benchmark-benchmark_message1_proto3.pb.Tpo -c -o cpp/datasets/google_message1/proto3/benchmark-benchmark_message1_proto3.pb.obj `if test -f 'cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message1/proto3/$(DEPDIR)/benchmark-benchmark_message1_proto3.pb.Tpo cpp/datasets/google_message1/proto3/$(DEPDIR)/benchmark-benchmark_message1_proto3.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc' object='cpp/datasets/google_message1/proto3/benchmark-benchmark_message1_proto3.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message1/proto3/benchmark-benchmark_message1_proto3.pb.obj `if test -f 'cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc'; fi`

cpp/datasets/google_message1/proto2/benchmark-benchmark_message1_proto2.pb.o: cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message1/proto2/benchmark-benchmark_message1_proto2.pb.o -MD -MP -MF cpp/datasets/google_message1/proto2/$(DEPDIR)/benchmark-benchmark_message1_proto2.pb.Tpo -c -o cpp/datasets/google_message1/proto2/benchmark-benchmark_message1_proto2.pb.o `test -f 'cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message1/proto2/$(DEPDIR)/benchmark-benchmark_message1_proto2.pb.Tpo cpp/datasets/google_message1/proto2/$(DEPDIR)/benchmark-benchmark_message1_proto2.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc' object='cpp/datasets/google_message1/proto2/benchmark-benchmark_message1_proto2.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message1/proto2/benchmark-benchmark_message1_proto2.pb.o `test -f 'cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc

cpp/datasets/google_message1/proto2/benchmark-benchmark_message1_proto2.pb.obj: cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message1/proto2/benchmark-benchmark_message1_proto2.pb.obj -MD -MP -MF cpp/datasets/google_message1/proto2/$(DEPDIR)/benchmark-benchmark_message1_proto2.pb.Tpo -c -o cpp/datasets/google_message1/proto2/benchmark-benchmark_message1_proto2.pb.obj `if test -f 'cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message1/proto2/$(DEPDIR)/benchmark-benchmark_message1_proto2.pb.Tpo cpp/datasets/google_message1/proto2/$(DEPDIR)/benchmark-benchmark_message1_proto2.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc' object='cpp/datasets/google_message1/proto2/benchmark-benchmark_message1_proto2.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message1/proto2/benchmark-benchmark_message1_proto2.pb.obj `if test -f 'cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc'; fi`

cpp/datasets/google_message2/benchmark-benchmark_message2.pb.o: cpp/datasets/google_message2/benchmark_message2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message2/benchmark-benchmark_message2.pb.o -MD -MP -MF cpp/datasets/google_message2/$(DEPDIR)/benchmark-benchmark_message2.pb.Tpo -c -o cpp/datasets/google_message2/benchmark-benchmark_message2.pb.o `test -f 'cpp/datasets/google_message2/benchmark_message2.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message2/benchmark_message2.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message2/$(DEPDIR)/benchmark-benchmark_message2.pb.Tpo cpp/datasets/google_message2/$(DEPDIR)/benchmark-benchmark_message2.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message2/benchmark_message2.pb.cc' object='cpp/datasets/google_message2/benchmark-benchmark_message2.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message2/benchmark-benchmark_message2.pb.o `test -f 'cpp/datasets/google_message2/benchmark_message2.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message2/benchmark_message2.pb.cc

cpp/datasets/google_message2/benchmark-benchmark_message2.pb.obj: cpp/datasets/google_message2/benchmark_message2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message2/benchmark-benchmark_message2.pb.obj -MD -MP -MF cpp/datasets/google_message2/$(DEPDIR)/benchmark-benchmark_message2.pb.Tpo -c -o cpp/datasets/google_message2/benchmark-benchmark_message2.pb.obj `if test -f 'cpp/datasets/google_message2/benchmark_message2.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message2/benchmark_message2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message2/benchmark_message2.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message2/$(DEPDIR)/benchmark-benchmark_message2.pb.Tpo cpp/datasets/google_message2/$(DEPDIR)/benchmark-benchmark_message2.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message2/benchmark_message2.pb.cc' object='cpp/datasets/google_message2/benchmark-benchmark_message2.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message2/benchmark-benchmark_message2.pb.obj `if test -f 'cpp/datasets/google_message2/benchmark_message2.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message2/benchmark_message2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message2/benchmark_message2.pb.cc'; fi`

cpp/datasets/google_message3/benchmark-benchmark_message3.pb.o: cpp/datasets/google_message3/benchmark_message3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/benchmark-benchmark_message3.pb.o -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3.pb.Tpo -c -o cpp/datasets/google_message3/benchmark-benchmark_message3.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3.pb.cc' object='cpp/datasets/google_message3/benchmark-benchmark_message3.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/benchmark-benchmark_message3.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3.pb.cc

cpp/datasets/google_message3/benchmark-benchmark_message3.pb.obj: cpp/datasets/google_message3/benchmark_message3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/benchmark-benchmark_message3.pb.obj -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3.pb.Tpo -c -o cpp/datasets/google_message3/benchmark-benchmark_message3.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3.pb.cc' object='cpp/datasets/google_message3/benchmark-benchmark_message3.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/benchmark-benchmark_message3.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3.pb.cc'; fi`

cpp/datasets/google_message3/benchmark-benchmark_message3_1.pb.o: cpp/datasets/google_message3/benchmark_message3_1.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/benchmark-benchmark_message3_1.pb.o -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_1.pb.Tpo -c -o cpp/datasets/google_message3/benchmark-benchmark_message3_1.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_1.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_1.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_1.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_1.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_1.pb.cc' object='cpp/datasets/google_message3/benchmark-benchmark_message3_1.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/benchmark-benchmark_message3_1.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_1.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_1.pb.cc

cpp/datasets/google_message3/benchmark-benchmark_message3_1.pb.obj: cpp/datasets/google_message3/benchmark_message3_1.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/benchmark-benchmark_message3_1.pb.obj -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_1.pb.Tpo -c -o cpp/datasets/google_message3/benchmark-benchmark_message3_1.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_1.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_1.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_1.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_1.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_1.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_1.pb.cc' object='cpp/datasets/google_message3/benchmark-benchmark_message3_1.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/benchmark-benchmark_message3_1.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_1.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_1.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_1.pb.cc'; fi`

cpp/datasets/google_message3/benchmark-benchmark_message3_2.pb.o: cpp/datasets/google_message3/benchmark_message3_2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/benchmark-benchmark_message3_2.pb.o -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_2.pb.Tpo -c -o cpp/datasets/google_message3/benchmark-benchmark_message3_2.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_2.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_2.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_2.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_2.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_2.pb.cc' object='cpp/datasets/google_message3/benchmark-benchmark_message3_2.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/benchmark-benchmark_message3_2.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_2.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_2.pb.cc

cpp/datasets/google_message3/benchmark-benchmark_message3_2.pb.obj: cpp/datasets/google_message3/benchmark_message3_2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/benchmark-benchmark_message3_2.pb.obj -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_2.pb.Tpo -c -o cpp/datasets/google_message3/benchmark-benchmark_message3_2.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_2.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_2.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_2.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_2.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_2.pb.cc' object='cpp/datasets/google_message3/benchmark-benchmark_message3_2.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/benchmark-benchmark_message3_2.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_2.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_2.pb.cc'; fi`

cpp/datasets/google_message3/benchmark-benchmark_message3_3.pb.o: cpp/datasets/google_message3/benchmark_message3_3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/benchmark-benchmark_message3_3.pb.o -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_3.pb.Tpo -c -o cpp/datasets/google_message3/benchmark-benchmark_message3_3.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_3.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_3.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_3.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_3.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_3.pb.cc' object='cpp/datasets/google_message3/benchmark-benchmark_message3_3.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/benchmark-benchmark_message3_3.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_3.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_3.pb.cc

cpp/datasets/google_message3/benchmark-benchmark_message3_3.pb.obj: cpp/datasets/google_message3/benchmark_message3_3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/benchmark-benchmark_message3_3.pb.obj -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_3.pb.Tpo -c -o cpp/datasets/google_message3/benchmark-benchmark_message3_3.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_3.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_3.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_3.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_3.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_3.pb.cc' object='cpp/datasets/google_message3/benchmark-benchmark_message3_3.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/benchmark-benchmark_message3_3.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_3.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_3.pb.cc'; fi`

cpp/datasets/google_message3/benchmark-benchmark_message3_4.pb.o: cpp/datasets/google_message3/benchmark_message3_4.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/benchmark-benchmark_message3_4.pb.o -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_4.pb.Tpo -c -o cpp/datasets/google_message3/benchmark-benchmark_message3_4.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_4.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_4.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_4.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_4.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_4.pb.cc' object='cpp/datasets/google_message3/benchmark-benchmark_message3_4.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/benchmark-benchmark_message3_4.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_4.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_4.pb.cc

cpp/datasets/google_message3/benchmark-benchmark_message3_4.pb.obj: cpp/datasets/google_message3/benchmark_message3_4.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/benchmark-benchmark_message3_4.pb.obj -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_4.pb.Tpo -c -o cpp/datasets/google_message3/benchmark-benchmark_message3_4.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_4.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_4.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_4.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_4.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_4.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_4.pb.cc' object='cpp/datasets/google_message3/benchmark-benchmark_message3_4.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/benchmark-benchmark_message3_4.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_4.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_4.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_4.pb.cc'; fi`

cpp/datasets/google_message3/benchmark-benchmark_message3_5.pb.o: cpp/datasets/google_message3/benchmark_message3_5.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/benchmark-benchmark_message3_5.pb.o -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_5.pb.Tpo -c -o cpp/datasets/google_message3/benchmark-benchmark_message3_5.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_5.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_5.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_5.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_5.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_5.pb.cc' object='cpp/datasets/google_message3/benchmark-benchmark_message3_5.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/benchmark-benchmark_message3_5.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_5.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_5.pb.cc

cpp/datasets/google_message3/benchmark-benchmark_message3_5.pb.obj: cpp/datasets/google_message3/benchmark_message3_5.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/benchmark-benchmark_message3_5.pb.obj -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_5.pb.Tpo -c -o cpp/datasets/google_message3/benchmark-benchmark_message3_5.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_5.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_5.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_5.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_5.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_5.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_5.pb.cc' object='cpp/datasets/google_message3/benchmark-benchmark_message3_5.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/benchmark-benchmark_message3_5.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_5.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_5.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_5.pb.cc'; fi`

cpp/datasets/google_message3/benchmark-benchmark_message3_6.pb.o: cpp/datasets/google_message3/benchmark_message3_6.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/benchmark-benchmark_message3_6.pb.o -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_6.pb.Tpo -c -o cpp/datasets/google_message3/benchmark-benchmark_message3_6.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_6.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_6.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_6.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_6.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_6.pb.cc' object='cpp/datasets/google_message3/benchmark-benchmark_message3_6.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/benchmark-benchmark_message3_6.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_6.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_6.pb.cc

cpp/datasets/google_message3/benchmark-benchmark_message3_6.pb.obj: cpp/datasets/google_message3/benchmark_message3_6.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/benchmark-benchmark_message3_6.pb.obj -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_6.pb.Tpo -c -o cpp/datasets/google_message3/benchmark-benchmark_message3_6.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_6.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_6.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_6.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_6.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_6.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_6.pb.cc' object='cpp/datasets/google_message3/benchmark-benchmark_message3_6.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/benchmark-benchmark_message3_6.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_6.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_6.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_6.pb.cc'; fi`

cpp/datasets/google_message3/benchmark-benchmark_message3_7.pb.o: cpp/datasets/google_message3/benchmark_message3_7.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/benchmark-benchmark_message3_7.pb.o -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_7.pb.Tpo -c -o cpp/datasets/google_message3/benchmark-benchmark_message3_7.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_7.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_7.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_7.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_7.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_7.pb.cc' object='cpp/datasets/google_message3/benchmark-benchmark_message3_7.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/benchmark-benchmark_message3_7.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_7.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_7.pb.cc

cpp/datasets/google_message3/benchmark-benchmark_message3_7.pb.obj: cpp/datasets/google_message3/benchmark_message3_7.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/benchmark-benchmark_message3_7.pb.obj -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_7.pb.Tpo -c -o cpp/datasets/google_message3/benchmark-benchmark_message3_7.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_7.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_7.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_7.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_7.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_7.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_7.pb.cc' object='cpp/datasets/google_message3/benchmark-benchmark_message3_7.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/benchmark-benchmark_message3_7.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_7.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_7.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_7.pb.cc'; fi`

cpp/datasets/google_message3/benchmark-benchmark_message3_8.pb.o: cpp/datasets/google_message3/benchmark_message3_8.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/benchmark-benchmark_message3_8.pb.o -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_8.pb.Tpo -c -o cpp/datasets/google_message3/benchmark-benchmark_message3_8.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_8.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_8.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_8.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_8.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_8.pb.cc' object='cpp/datasets/google_message3/benchmark-benchmark_message3_8.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/benchmark-benchmark_message3_8.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_8.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_8.pb.cc

cpp/datasets/google_message3/benchmark-benchmark_message3_8.pb.obj: cpp/datasets/google_message3/benchmark_message3_8.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/benchmark-benchmark_message3_8.pb.obj -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_8.pb.Tpo -c -o cpp/datasets/google_message3/benchmark-benchmark_message3_8.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_8.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_8.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_8.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_8.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_8.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_8.pb.cc' object='cpp/datasets/google_message3/benchmark-benchmark_message3_8.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/benchmark-benchmark_message3_8.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_8.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_8.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_8.pb.cc'; fi`

cpp/datasets/google_message4/benchmark-benchmark_message4.pb.o: cpp/datasets/google_message4/benchmark_message4.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message4/benchmark-benchmark_message4.pb.o -MD -MP -MF cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4.pb.Tpo -c -o cpp/datasets/google_message4/benchmark-benchmark_message4.pb.o `test -f 'cpp/datasets/google_message4/benchmark_message4.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message4/benchmark_message4.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4.pb.Tpo cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message4/benchmark_message4.pb.cc' object='cpp/datasets/google_message4/benchmark-benchmark_message4.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message4/benchmark-benchmark_message4.pb.o `test -f 'cpp/datasets/google_message4/benchmark_message4.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message4/benchmark_message4.pb.cc

cpp/datasets/google_message4/benchmark-benchmark_message4.pb.obj: cpp/datasets/google_message4/benchmark_message4.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message4/benchmark-benchmark_message4.pb.obj -MD -MP -MF cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4.pb.Tpo -c -o cpp/datasets/google_message4/benchmark-benchmark_message4.pb.obj `if test -f 'cpp/datasets/google_message4/benchmark_message4.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message4/benchmark_message4.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message4/benchmark_message4.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4.pb.Tpo cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message4/benchmark_message4.pb.cc' object='cpp/datasets/google_message4/benchmark-benchmark_message4.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message4/benchmark-benchmark_message4.pb.obj `if test -f 'cpp/datasets/google_message4/benchmark_message4.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message4/benchmark_message4.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message4/benchmark_message4.pb.cc'; fi`

cpp/datasets/google_message4/benchmark-benchmark_message4_1.pb.o: cpp/datasets/google_message4/benchmark_message4_1.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message4/benchmark-benchmark_message4_1.pb.o -MD -MP -MF cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4_1.pb.Tpo -c -o cpp/datasets/google_message4/benchmark-benchmark_message4_1.pb.o `test -f 'cpp/datasets/google_message4/benchmark_message4_1.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message4/benchmark_message4_1.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4_1.pb.Tpo cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4_1.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message4/benchmark_message4_1.pb.cc' object='cpp/datasets/google_message4/benchmark-benchmark_message4_1.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message4/benchmark-benchmark_message4_1.pb.o `test -f 'cpp/datasets/google_message4/benchmark_message4_1.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message4/benchmark_message4_1.pb.cc

cpp/datasets/google_message4/benchmark-benchmark_message4_1.pb.obj: cpp/datasets/google_message4/benchmark_message4_1.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message4/benchmark-benchmark_message4_1.pb.obj -MD -MP -MF cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4_1.pb.Tpo -c -o cpp/datasets/google_message4/benchmark-benchmark_message4_1.pb.obj `if test -f 'cpp/datasets/google_message4/benchmark_message4_1.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message4/benchmark_message4_1.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message4/benchmark_message4_1.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4_1.pb.Tpo cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4_1.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message4/benchmark_message4_1.pb.cc' object='cpp/datasets/google_message4/benchmark-benchmark_message4_1.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message4/benchmark-benchmark_message4_1.pb.obj `if test -f 'cpp/datasets/google_message4/benchmark_message4_1.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message4/benchmark_message4_1.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message4/benchmark_message4_1.pb.cc'; fi`

cpp/datasets/google_message4/benchmark-benchmark_message4_2.pb.o: cpp/datasets/google_message4/benchmark_message4_2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message4/benchmark-benchmark_message4_2.pb.o -MD -MP -MF cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4_2.pb.Tpo -c -o cpp/datasets/google_message4/benchmark-benchmark_message4_2.pb.o `test -f 'cpp/datasets/google_message4/benchmark_message4_2.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message4/benchmark_message4_2.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4_2.pb.Tpo cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4_2.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message4/benchmark_message4_2.pb.cc' object='cpp/datasets/google_message4/benchmark-benchmark_message4_2.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message4/benchmark-benchmark_message4_2.pb.o `test -f 'cpp/datasets/google_message4/benchmark_message4_2.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message4/benchmark_message4_2.pb.cc

cpp/datasets/google_message4/benchmark-benchmark_message4_2.pb.obj: cpp/datasets/google_message4/benchmark_message4_2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message4/benchmark-benchmark_message4_2.pb.obj -MD -MP -MF cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4_2.pb.Tpo -c -o cpp/datasets/google_message4/benchmark-benchmark_message4_2.pb.obj `if test -f 'cpp/datasets/google_message4/benchmark_message4_2.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message4/benchmark_message4_2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message4/benchmark_message4_2.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4_2.pb.Tpo cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4_2.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message4/benchmark_message4_2.pb.cc' object='cpp/datasets/google_message4/benchmark-benchmark_message4_2.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message4/benchmark-benchmark_message4_2.pb.obj `if test -f 'cpp/datasets/google_message4/benchmark_message4_2.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message4/benchmark_message4_2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message4/benchmark_message4_2.pb.cc'; fi`

cpp/datasets/google_message4/benchmark-benchmark_message4_3.pb.o: cpp/datasets/google_message4/benchmark_message4_3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message4/benchmark-benchmark_message4_3.pb.o -MD -MP -MF cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4_3.pb.Tpo -c -o cpp/datasets/google_message4/benchmark-benchmark_message4_3.pb.o `test -f 'cpp/datasets/google_message4/benchmark_message4_3.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message4/benchmark_message4_3.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4_3.pb.Tpo cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4_3.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message4/benchmark_message4_3.pb.cc' object='cpp/datasets/google_message4/benchmark-benchmark_message4_3.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message4/benchmark-benchmark_message4_3.pb.o `test -f 'cpp/datasets/google_message4/benchmark_message4_3.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message4/benchmark_message4_3.pb.cc

cpp/datasets/google_message4/benchmark-benchmark_message4_3.pb.obj: cpp/datasets/google_message4/benchmark_message4_3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message4/benchmark-benchmark_message4_3.pb.obj -MD -MP -MF cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4_3.pb.Tpo -c -o cpp/datasets/google_message4/benchmark-benchmark_message4_3.pb.obj `if test -f 'cpp/datasets/google_message4/benchmark_message4_3.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message4/benchmark_message4_3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message4/benchmark_message4_3.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4_3.pb.Tpo cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4_3.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message4/benchmark_message4_3.pb.cc' object='cpp/datasets/google_message4/benchmark-benchmark_message4_3.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message4/benchmark-benchmark_message4_3.pb.obj `if test -f 'cpp/datasets/google_message4/benchmark_message4_3.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message4/benchmark_message4_3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message4/benchmark_message4_3.pb.cc'; fi`

gogo/cpp_no_group/cpp_no_group_benchmark-cpp_benchmark.o: gogo/cpp_no_group/cpp_benchmark.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/cpp_no_group_benchmark-cpp_benchmark.o -MD -MP -MF gogo/cpp_no_group/$(DEPDIR)/cpp_no_group_benchmark-cpp_benchmark.Tpo -c -o gogo/cpp_no_group/cpp_no_group_benchmark-cpp_benchmark.o `test -f 'gogo/cpp_no_group/cpp_benchmark.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/cpp_benchmark.cc
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/$(DEPDIR)/cpp_no_group_benchmark-cpp_benchmark.Tpo gogo/cpp_no_group/$(DEPDIR)/cpp_no_group_benchmark-cpp_benchmark.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/cpp_benchmark.cc' object='gogo/cpp_no_group/cpp_no_group_benchmark-cpp_benchmark.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/cpp_no_group_benchmark-cpp_benchmark.o `test -f 'gogo/cpp_no_group/cpp_benchmark.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/cpp_benchmark.cc

gogo/cpp_no_group/cpp_no_group_benchmark-cpp_benchmark.obj: gogo/cpp_no_group/cpp_benchmark.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/cpp_no_group_benchmark-cpp_benchmark.obj -MD -MP -MF gogo/cpp_no_group/$(DEPDIR)/cpp_no_group_benchmark-cpp_benchmark.Tpo -c -o gogo/cpp_no_group/cpp_no_group_benchmark-cpp_benchmark.obj `if test -f 'gogo/cpp_no_group/cpp_benchmark.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/cpp_benchmark.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/cpp_benchmark.cc'; fi`
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/$(DEPDIR)/cpp_no_group_benchmark-cpp_benchmark.Tpo gogo/cpp_no_group/$(DEPDIR)/cpp_no_group_benchmark-cpp_benchmark.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/cpp_benchmark.cc' object='gogo/cpp_no_group/cpp_no_group_benchmark-cpp_benchmark.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/cpp_no_group_benchmark-cpp_benchmark.obj `if test -f 'gogo/cpp_no_group/cpp_benchmark.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/cpp_benchmark.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/cpp_benchmark.cc'; fi`

gogo/cpp_no_group/datasets/google_message1/proto2/cpp_no_group_benchmark-benchmark_message1_proto2.pb.o: gogo/cpp_no_group/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/datasets/google_message1/proto2/cpp_no_group_benchmark-benchmark_message1_proto2.pb.o -MD -MP -MF gogo/cpp_no_group/datasets/google_message1/proto2/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message1_proto2.pb.Tpo -c -o gogo/cpp_no_group/datasets/google_message1/proto2/cpp_no_group_benchmark-benchmark_message1_proto2.pb.o `test -f 'gogo/cpp_no_group/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/datasets/google_message1/proto2/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message1_proto2.pb.Tpo gogo/cpp_no_group/datasets/google_message1/proto2/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message1_proto2.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc' object='gogo/cpp_no_group/datasets/google_message1/proto2/cpp_no_group_benchmark-benchmark_message1_proto2.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/datasets/google_message1/proto2/cpp_no_group_benchmark-benchmark_message1_proto2.pb.o `test -f 'gogo/cpp_no_group/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc

gogo/cpp_no_group/datasets/google_message1/proto2/cpp_no_group_benchmark-benchmark_message1_proto2.pb.obj: gogo/cpp_no_group/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/datasets/google_message1/proto2/cpp_no_group_benchmark-benchmark_message1_proto2.pb.obj -MD -MP -MF gogo/cpp_no_group/datasets/google_message1/proto2/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message1_proto2.pb.Tpo -c -o gogo/cpp_no_group/datasets/google_message1/proto2/cpp_no_group_benchmark-benchmark_message1_proto2.pb.obj `if test -f 'gogo/cpp_no_group/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc'; fi`
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/datasets/google_message1/proto2/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message1_proto2.pb.Tpo gogo/cpp_no_group/datasets/google_message1/proto2/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message1_proto2.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc' object='gogo/cpp_no_group/datasets/google_message1/proto2/cpp_no_group_benchmark-benchmark_message1_proto2.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/datasets/google_message1/proto2/cpp_no_group_benchmark-benchmark_message1_proto2.pb.obj `if test -f 'gogo/cpp_no_group/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc'; fi`

gogo/cpp_no_group/datasets/google_message2/cpp_no_group_benchmark-benchmark_message2.pb.o: gogo/cpp_no_group/datasets/google_message2/benchmark_message2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/datasets/google_message2/cpp_no_group_benchmark-benchmark_message2.pb.o -MD -MP -MF gogo/cpp_no_group/datasets/google_message2/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message2.pb.Tpo -c -o gogo/cpp_no_group/datasets/google_message2/cpp_no_group_benchmark-benchmark_message2.pb.o `test -f 'gogo/cpp_no_group/datasets/google_message2/benchmark_message2.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/datasets/google_message2/benchmark_message2.pb.cc
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/datasets/google_message2/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message2.pb.Tpo gogo/cpp_no_group/datasets/google_message2/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message2.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/datasets/google_message2/benchmark_message2.pb.cc' object='gogo/cpp_no_group/datasets/google_message2/cpp_no_group_benchmark-benchmark_message2.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/datasets/google_message2/cpp_no_group_benchmark-benchmark_message2.pb.o `test -f 'gogo/cpp_no_group/datasets/google_message2/benchmark_message2.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/datasets/google_message2/benchmark_message2.pb.cc

gogo/cpp_no_group/datasets/google_message2/cpp_no_group_benchmark-benchmark_message2.pb.obj: gogo/cpp_no_group/datasets/google_message2/benchmark_message2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/datasets/google_message2/cpp_no_group_benchmark-benchmark_message2.pb.obj -MD -MP -MF gogo/cpp_no_group/datasets/google_message2/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message2.pb.Tpo -c -o gogo/cpp_no_group/datasets/google_message2/cpp_no_group_benchmark-benchmark_message2.pb.obj `if test -f 'gogo/cpp_no_group/datasets/google_message2/benchmark_message2.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/datasets/google_message2/benchmark_message2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/datasets/google_message2/benchmark_message2.pb.cc'; fi`
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/datasets/google_message2/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message2.pb.Tpo gogo/cpp_no_group/datasets/google_message2/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message2.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/datasets/google_message2/benchmark_message2.pb.cc' object='gogo/cpp_no_group/datasets/google_message2/cpp_no_group_benchmark-benchmark_message2.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/datasets/google_message2/cpp_no_group_benchmark-benchmark_message2.pb.obj `if test -f 'gogo/cpp_no_group/datasets/google_message2/benchmark_message2.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/datasets/google_message2/benchmark_message2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/datasets/google_message2/benchmark_message2.pb.cc'; fi`

gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3.pb.o: gogo/cpp_no_group/datasets/google_message3/benchmark_message3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3.pb.o -MD -MP -MF gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3.pb.Tpo -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3.pb.o `test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/datasets/google_message3/benchmark_message3.pb.cc
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3.pb.Tpo gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/datasets/google_message3/benchmark_message3.pb.cc' object='gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3.pb.o `test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/datasets/google_message3/benchmark_message3.pb.cc

gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3.pb.obj: gogo/cpp_no_group/datasets/google_message3/benchmark_message3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3.pb.obj -MD -MP -MF gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3.pb.Tpo -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3.pb.obj `if test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/datasets/google_message3/benchmark_message3.pb.cc'; fi`
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3.pb.Tpo gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/datasets/google_message3/benchmark_message3.pb.cc' object='gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3.pb.obj `if test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/datasets/google_message3/benchmark_message3.pb.cc'; fi`

gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_1.pb.o: gogo/cpp_no_group/datasets/google_message3/benchmark_message3_1.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_1.pb.o -MD -MP -MF gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_1.pb.Tpo -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_1.pb.o `test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_1.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/datasets/google_message3/benchmark_message3_1.pb.cc
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_1.pb.Tpo gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_1.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/datasets/google_message3/benchmark_message3_1.pb.cc' object='gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_1.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_1.pb.o `test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_1.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/datasets/google_message3/benchmark_message3_1.pb.cc

gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_1.pb.obj: gogo/cpp_no_group/datasets/google_message3/benchmark_message3_1.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_1.pb.obj -MD -MP -MF gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_1.pb.Tpo -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_1.pb.obj `if test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_1.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_1.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/datasets/google_message3/benchmark_message3_1.pb.cc'; fi`
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_1.pb.Tpo gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_1.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/datasets/google_message3/benchmark_message3_1.pb.cc' object='gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_1.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_1.pb.obj `if test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_1.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_1.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/datasets/google_message3/benchmark_message3_1.pb.cc'; fi`

gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_2.pb.o: gogo/cpp_no_group/datasets/google_message3/benchmark_message3_2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_2.pb.o -MD -MP -MF gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_2.pb.Tpo -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_2.pb.o `test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_2.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/datasets/google_message3/benchmark_message3_2.pb.cc
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_2.pb.Tpo gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_2.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/datasets/google_message3/benchmark_message3_2.pb.cc' object='gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_2.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_2.pb.o `test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_2.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/datasets/google_message3/benchmark_message3_2.pb.cc

gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_2.pb.obj: gogo/cpp_no_group/datasets/google_message3/benchmark_message3_2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_2.pb.obj -MD -MP -MF gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_2.pb.Tpo -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_2.pb.obj `if test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_2.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/datasets/google_message3/benchmark_message3_2.pb.cc'; fi`
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_2.pb.Tpo gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_2.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/datasets/google_message3/benchmark_message3_2.pb.cc' object='gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_2.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_2.pb.obj `if test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_2.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/datasets/google_message3/benchmark_message3_2.pb.cc'; fi`

gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_3.pb.o: gogo/cpp_no_group/datasets/google_message3/benchmark_message3_3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_3.pb.o -MD -MP -MF gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_3.pb.Tpo -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_3.pb.o `test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_3.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/datasets/google_message3/benchmark_message3_3.pb.cc
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_3.pb.Tpo gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_3.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/datasets/google_message3/benchmark_message3_3.pb.cc' object='gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_3.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_3.pb.o `test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_3.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/datasets/google_message3/benchmark_message3_3.pb.cc

gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_3.pb.obj: gogo/cpp_no_group/datasets/google_message3/benchmark_message3_3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_3.pb.obj -MD -MP -MF gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_3.pb.Tpo -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_3.pb.obj `if test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_3.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/datasets/google_message3/benchmark_message3_3.pb.cc'; fi`
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_3.pb.Tpo gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_3.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/datasets/google_message3/benchmark_message3_3.pb.cc' object='gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_3.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_3.pb.obj `if test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_3.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/datasets/google_message3/benchmark_message3_3.pb.cc'; fi`

gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_4.pb.o: gogo/cpp_no_group/datasets/google_message3/benchmark_message3_4.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_4.pb.o -MD -MP -MF gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_4.pb.Tpo -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_4.pb.o `test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_4.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/datasets/google_message3/benchmark_message3_4.pb.cc
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_4.pb.Tpo gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_4.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/datasets/google_message3/benchmark_message3_4.pb.cc' object='gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_4.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_4.pb.o `test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_4.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/datasets/google_message3/benchmark_message3_4.pb.cc

gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_4.pb.obj: gogo/cpp_no_group/datasets/google_message3/benchmark_message3_4.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_4.pb.obj -MD -MP -MF gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_4.pb.Tpo -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_4.pb.obj `if test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_4.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_4.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/datasets/google_message3/benchmark_message3_4.pb.cc'; fi`
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_4.pb.Tpo gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_4.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/datasets/google_message3/benchmark_message3_4.pb.cc' object='gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_4.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_4.pb.obj `if test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_4.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_4.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/datasets/google_message3/benchmark_message3_4.pb.cc'; fi`

gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_5.pb.o: gogo/cpp_no_group/datasets/google_message3/benchmark_message3_5.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_5.pb.o -MD -MP -MF gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_5.pb.Tpo -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_5.pb.o `test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_5.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/datasets/google_message3/benchmark_message3_5.pb.cc
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_5.pb.Tpo gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_5.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/datasets/google_message3/benchmark_message3_5.pb.cc' object='gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_5.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_5.pb.o `test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_5.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/datasets/google_message3/benchmark_message3_5.pb.cc

gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_5.pb.obj: gogo/cpp_no_group/datasets/google_message3/benchmark_message3_5.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_5.pb.obj -MD -MP -MF gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_5.pb.Tpo -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_5.pb.obj `if test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_5.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_5.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/datasets/google_message3/benchmark_message3_5.pb.cc'; fi`
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_5.pb.Tpo gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_5.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/datasets/google_message3/benchmark_message3_5.pb.cc' object='gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_5.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_5.pb.obj `if test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_5.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_5.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/datasets/google_message3/benchmark_message3_5.pb.cc'; fi`

gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_6.pb.o: gogo/cpp_no_group/datasets/google_message3/benchmark_message3_6.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_6.pb.o -MD -MP -MF gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_6.pb.Tpo -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_6.pb.o `test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_6.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/datasets/google_message3/benchmark_message3_6.pb.cc
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_6.pb.Tpo gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_6.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/datasets/google_message3/benchmark_message3_6.pb.cc' object='gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_6.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_6.pb.o `test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_6.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/datasets/google_message3/benchmark_message3_6.pb.cc

gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_6.pb.obj: gogo/cpp_no_group/datasets/google_message3/benchmark_message3_6.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_6.pb.obj -MD -MP -MF gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_6.pb.Tpo -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_6.pb.obj `if test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_6.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_6.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/datasets/google_message3/benchmark_message3_6.pb.cc'; fi`
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_6.pb.Tpo gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_6.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/datasets/google_message3/benchmark_message3_6.pb.cc' object='gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_6.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_6.pb.obj `if test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_6.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_6.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/datasets/google_message3/benchmark_message3_6.pb.cc'; fi`

gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_7.pb.o: gogo/cpp_no_group/datasets/google_message3/benchmark_message3_7.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_7.pb.o -MD -MP -MF gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_7.pb.Tpo -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_7.pb.o `test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_7.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/datasets/google_message3/benchmark_message3_7.pb.cc
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_7.pb.Tpo gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_7.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/datasets/google_message3/benchmark_message3_7.pb.cc' object='gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_7.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_7.pb.o `test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_7.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/datasets/google_message3/benchmark_message3_7.pb.cc

gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_7.pb.obj: gogo/cpp_no_group/datasets/google_message3/benchmark_message3_7.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_7.pb.obj -MD -MP -MF gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_7.pb.Tpo -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_7.pb.obj `if test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_7.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_7.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/datasets/google_message3/benchmark_message3_7.pb.cc'; fi`
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_7.pb.Tpo gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_7.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/datasets/google_message3/benchmark_message3_7.pb.cc' object='gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_7.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_7.pb.obj `if test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_7.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_7.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/datasets/google_message3/benchmark_message3_7.pb.cc'; fi`

gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_8.pb.o: gogo/cpp_no_group/datasets/google_message3/benchmark_message3_8.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_8.pb.o -MD -MP -MF gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_8.pb.Tpo -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_8.pb.o `test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_8.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/datasets/google_message3/benchmark_message3_8.pb.cc
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_8.pb.Tpo gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_8.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/datasets/google_message3/benchmark_message3_8.pb.cc' object='gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_8.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_8.pb.o `test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_8.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/datasets/google_message3/benchmark_message3_8.pb.cc

gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_8.pb.obj: gogo/cpp_no_group/datasets/google_message3/benchmark_message3_8.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_8.pb.obj -MD -MP -MF gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_8.pb.Tpo -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_8.pb.obj `if test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_8.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_8.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/datasets/google_message3/benchmark_message3_8.pb.cc'; fi`
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_8.pb.Tpo gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_8.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/datasets/google_message3/benchmark_message3_8.pb.cc' object='gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_8.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/datasets/google_message3/cpp_no_group_benchmark-benchmark_message3_8.pb.obj `if test -f 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_8.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/datasets/google_message3/benchmark_message3_8.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/datasets/google_message3/benchmark_message3_8.pb.cc'; fi`

gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4.pb.o: gogo/cpp_no_group/datasets/google_message4/benchmark_message4.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4.pb.o -MD -MP -MF gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4.pb.Tpo -c -o gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4.pb.o `test -f 'gogo/cpp_no_group/datasets/google_message4/benchmark_message4.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/datasets/google_message4/benchmark_message4.pb.cc
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4.pb.Tpo gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/datasets/google_message4/benchmark_message4.pb.cc' object='gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4.pb.o `test -f 'gogo/cpp_no_group/datasets/google_message4/benchmark_message4.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/datasets/google_message4/benchmark_message4.pb.cc

gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4.pb.obj: gogo/cpp_no_group/datasets/google_message4/benchmark_message4.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4.pb.obj -MD -MP -MF gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4.pb.Tpo -c -o gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4.pb.obj `if test -f 'gogo/cpp_no_group/datasets/google_message4/benchmark_message4.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/datasets/google_message4/benchmark_message4.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/datasets/google_message4/benchmark_message4.pb.cc'; fi`
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4.pb.Tpo gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/datasets/google_message4/benchmark_message4.pb.cc' object='gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4.pb.obj `if test -f 'gogo/cpp_no_group/datasets/google_message4/benchmark_message4.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/datasets/google_message4/benchmark_message4.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/datasets/google_message4/benchmark_message4.pb.cc'; fi`

gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_1.pb.o: gogo/cpp_no_group/datasets/google_message4/benchmark_message4_1.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_1.pb.o -MD -MP -MF gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4_1.pb.Tpo -c -o gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_1.pb.o `test -f 'gogo/cpp_no_group/datasets/google_message4/benchmark_message4_1.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/datasets/google_message4/benchmark_message4_1.pb.cc
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4_1.pb.Tpo gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4_1.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/datasets/google_message4/benchmark_message4_1.pb.cc' object='gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_1.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_1.pb.o `test -f 'gogo/cpp_no_group/datasets/google_message4/benchmark_message4_1.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/datasets/google_message4/benchmark_message4_1.pb.cc

gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_1.pb.obj: gogo/cpp_no_group/datasets/google_message4/benchmark_message4_1.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_1.pb.obj -MD -MP -MF gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4_1.pb.Tpo -c -o gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_1.pb.obj `if test -f 'gogo/cpp_no_group/datasets/google_message4/benchmark_message4_1.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/datasets/google_message4/benchmark_message4_1.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/datasets/google_message4/benchmark_message4_1.pb.cc'; fi`
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4_1.pb.Tpo gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4_1.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/datasets/google_message4/benchmark_message4_1.pb.cc' object='gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_1.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_1.pb.obj `if test -f 'gogo/cpp_no_group/datasets/google_message4/benchmark_message4_1.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/datasets/google_message4/benchmark_message4_1.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/datasets/google_message4/benchmark_message4_1.pb.cc'; fi`

gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_2.pb.o: gogo/cpp_no_group/datasets/google_message4/benchmark_message4_2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_2.pb.o -MD -MP -MF gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4_2.pb.Tpo -c -o gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_2.pb.o `test -f 'gogo/cpp_no_group/datasets/google_message4/benchmark_message4_2.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/datasets/google_message4/benchmark_message4_2.pb.cc
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4_2.pb.Tpo gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4_2.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/datasets/google_message4/benchmark_message4_2.pb.cc' object='gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_2.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_2.pb.o `test -f 'gogo/cpp_no_group/datasets/google_message4/benchmark_message4_2.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/datasets/google_message4/benchmark_message4_2.pb.cc

gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_2.pb.obj: gogo/cpp_no_group/datasets/google_message4/benchmark_message4_2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_2.pb.obj -MD -MP -MF gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4_2.pb.Tpo -c -o gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_2.pb.obj `if test -f 'gogo/cpp_no_group/datasets/google_message4/benchmark_message4_2.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/datasets/google_message4/benchmark_message4_2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/datasets/google_message4/benchmark_message4_2.pb.cc'; fi`
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4_2.pb.Tpo gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4_2.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/datasets/google_message4/benchmark_message4_2.pb.cc' object='gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_2.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_2.pb.obj `if test -f 'gogo/cpp_no_group/datasets/google_message4/benchmark_message4_2.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/datasets/google_message4/benchmark_message4_2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/datasets/google_message4/benchmark_message4_2.pb.cc'; fi`

gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_3.pb.o: gogo/cpp_no_group/datasets/google_message4/benchmark_message4_3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_3.pb.o -MD -MP -MF gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4_3.pb.Tpo -c -o gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_3.pb.o `test -f 'gogo/cpp_no_group/datasets/google_message4/benchmark_message4_3.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/datasets/google_message4/benchmark_message4_3.pb.cc
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4_3.pb.Tpo gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4_3.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/datasets/google_message4/benchmark_message4_3.pb.cc' object='gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_3.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_3.pb.o `test -f 'gogo/cpp_no_group/datasets/google_message4/benchmark_message4_3.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/datasets/google_message4/benchmark_message4_3.pb.cc

gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_3.pb.obj: gogo/cpp_no_group/datasets/google_message4/benchmark_message4_3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_3.pb.obj -MD -MP -MF gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4_3.pb.Tpo -c -o gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_3.pb.obj `if test -f 'gogo/cpp_no_group/datasets/google_message4/benchmark_message4_3.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/datasets/google_message4/benchmark_message4_3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/datasets/google_message4/benchmark_message4_3.pb.cc'; fi`
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4_3.pb.Tpo gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4_3.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/datasets/google_message4/benchmark_message4_3.pb.cc' object='gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_3.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/datasets/google_message4/cpp_no_group_benchmark-benchmark_message4_3.pb.obj `if test -f 'gogo/cpp_no_group/datasets/google_message4/benchmark_message4_3.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/datasets/google_message4/benchmark_message4_3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/datasets/google_message4/benchmark_message4_3.pb.cc'; fi`

gogo/cpp_no_group/cpp_no_group_benchmark-benchmarks.pb.o: gogo/cpp_no_group/benchmarks.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/cpp_no_group_benchmark-benchmarks.pb.o -MD -MP -MF gogo/cpp_no_group/$(DEPDIR)/cpp_no_group_benchmark-benchmarks.pb.Tpo -c -o gogo/cpp_no_group/cpp_no_group_benchmark-benchmarks.pb.o `test -f 'gogo/cpp_no_group/benchmarks.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/benchmarks.pb.cc
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/$(DEPDIR)/cpp_no_group_benchmark-benchmarks.pb.Tpo gogo/cpp_no_group/$(DEPDIR)/cpp_no_group_benchmark-benchmarks.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/benchmarks.pb.cc' object='gogo/cpp_no_group/cpp_no_group_benchmark-benchmarks.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/cpp_no_group_benchmark-benchmarks.pb.o `test -f 'gogo/cpp_no_group/benchmarks.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/benchmarks.pb.cc

gogo/cpp_no_group/cpp_no_group_benchmark-benchmarks.pb.obj: gogo/cpp_no_group/benchmarks.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/cpp_no_group_benchmark-benchmarks.pb.obj -MD -MP -MF gogo/cpp_no_group/$(DEPDIR)/cpp_no_group_benchmark-benchmarks.pb.Tpo -c -o gogo/cpp_no_group/cpp_no_group_benchmark-benchmarks.pb.obj `if test -f 'gogo/cpp_no_group/benchmarks.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/benchmarks.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/benchmarks.pb.cc'; fi`
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/$(DEPDIR)/cpp_no_group_benchmark-benchmarks.pb.Tpo gogo/cpp_no_group/$(DEPDIR)/cpp_no_group_benchmark-benchmarks.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/benchmarks.pb.cc' object='gogo/cpp_no_group/cpp_no_group_benchmark-benchmarks.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/cpp_no_group_benchmark-benchmarks.pb.obj `if test -f 'gogo/cpp_no_group/benchmarks.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/benchmarks.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/benchmarks.pb.cc'; fi`

gogo/cpp_no_group/datasets/google_message1/proto3/cpp_no_group_benchmark-benchmark_message1_proto3.pb.o: gogo/cpp_no_group/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/datasets/google_message1/proto3/cpp_no_group_benchmark-benchmark_message1_proto3.pb.o -MD -MP -MF gogo/cpp_no_group/datasets/google_message1/proto3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message1_proto3.pb.Tpo -c -o gogo/cpp_no_group/datasets/google_message1/proto3/cpp_no_group_benchmark-benchmark_message1_proto3.pb.o `test -f 'gogo/cpp_no_group/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/datasets/google_message1/proto3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message1_proto3.pb.Tpo gogo/cpp_no_group/datasets/google_message1/proto3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message1_proto3.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc' object='gogo/cpp_no_group/datasets/google_message1/proto3/cpp_no_group_benchmark-benchmark_message1_proto3.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/datasets/google_message1/proto3/cpp_no_group_benchmark-benchmark_message1_proto3.pb.o `test -f 'gogo/cpp_no_group/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc' || echo '$(srcdir)/'`gogo/cpp_no_group/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc

gogo/cpp_no_group/datasets/google_message1/proto3/cpp_no_group_benchmark-benchmark_message1_proto3.pb.obj: gogo/cpp_no_group/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT gogo/cpp_no_group/datasets/google_message1/proto3/cpp_no_group_benchmark-benchmark_message1_proto3.pb.obj -MD -MP -MF gogo/cpp_no_group/datasets/google_message1/proto3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message1_proto3.pb.Tpo -c -o gogo/cpp_no_group/datasets/google_message1/proto3/cpp_no_group_benchmark-benchmark_message1_proto3.pb.obj `if test -f 'gogo/cpp_no_group/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc'; fi`
	$(AM_V_at)$(am__mv) gogo/cpp_no_group/datasets/google_message1/proto3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message1_proto3.pb.Tpo gogo/cpp_no_group/datasets/google_message1/proto3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message1_proto3.pb.Po
#	$(AM_V_CXX)source='gogo/cpp_no_group/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc' object='gogo/cpp_no_group/datasets/google_message1/proto3/cpp_no_group_benchmark-benchmark_message1_proto3.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(cpp_no_group_benchmark_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o gogo/cpp_no_group/datasets/google_message1/proto3/cpp_no_group_benchmark-benchmark_message1_proto3.pb.obj `if test -f 'gogo/cpp_no_group/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc'; then $(CYGPATH_W) 'gogo/cpp_no_group/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/gogo/cpp_no_group/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc'; fi`

util/gogo_data_scrubber-gogo_data_scrubber.o: util/gogo_data_scrubber.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT util/gogo_data_scrubber-gogo_data_scrubber.o -MD -MP -MF util/$(DEPDIR)/gogo_data_scrubber-gogo_data_scrubber.Tpo -c -o util/gogo_data_scrubber-gogo_data_scrubber.o `test -f 'util/gogo_data_scrubber.cc' || echo '$(srcdir)/'`util/gogo_data_scrubber.cc
	$(AM_V_at)$(am__mv) util/$(DEPDIR)/gogo_data_scrubber-gogo_data_scrubber.Tpo util/$(DEPDIR)/gogo_data_scrubber-gogo_data_scrubber.Po
#	$(AM_V_CXX)source='util/gogo_data_scrubber.cc' object='util/gogo_data_scrubber-gogo_data_scrubber.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o util/gogo_data_scrubber-gogo_data_scrubber.o `test -f 'util/gogo_data_scrubber.cc' || echo '$(srcdir)/'`util/gogo_data_scrubber.cc

util/gogo_data_scrubber-gogo_data_scrubber.obj: util/gogo_data_scrubber.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT util/gogo_data_scrubber-gogo_data_scrubber.obj -MD -MP -MF util/$(DEPDIR)/gogo_data_scrubber-gogo_data_scrubber.Tpo -c -o util/gogo_data_scrubber-gogo_data_scrubber.obj `if test -f 'util/gogo_data_scrubber.cc'; then $(CYGPATH_W) 'util/gogo_data_scrubber.cc'; else $(CYGPATH_W) '$(srcdir)/util/gogo_data_scrubber.cc'; fi`
	$(AM_V_at)$(am__mv) util/$(DEPDIR)/gogo_data_scrubber-gogo_data_scrubber.Tpo util/$(DEPDIR)/gogo_data_scrubber-gogo_data_scrubber.Po
#	$(AM_V_CXX)source='util/gogo_data_scrubber.cc' object='util/gogo_data_scrubber-gogo_data_scrubber.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o util/gogo_data_scrubber-gogo_data_scrubber.obj `if test -f 'util/gogo_data_scrubber.cc'; then $(CYGPATH_W) 'util/gogo_data_scrubber.cc'; else $(CYGPATH_W) '$(srcdir)/util/gogo_data_scrubber.cc'; fi`

cpp/gogo_data_scrubber-benchmarks.pb.o: cpp/benchmarks.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/gogo_data_scrubber-benchmarks.pb.o -MD -MP -MF cpp/$(DEPDIR)/gogo_data_scrubber-benchmarks.pb.Tpo -c -o cpp/gogo_data_scrubber-benchmarks.pb.o `test -f 'cpp/benchmarks.pb.cc' || echo '$(srcdir)/'`cpp/benchmarks.pb.cc
	$(AM_V_at)$(am__mv) cpp/$(DEPDIR)/gogo_data_scrubber-benchmarks.pb.Tpo cpp/$(DEPDIR)/gogo_data_scrubber-benchmarks.pb.Po
#	$(AM_V_CXX)source='cpp/benchmarks.pb.cc' object='cpp/gogo_data_scrubber-benchmarks.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/gogo_data_scrubber-benchmarks.pb.o `test -f 'cpp/benchmarks.pb.cc' || echo '$(srcdir)/'`cpp/benchmarks.pb.cc

cpp/gogo_data_scrubber-benchmarks.pb.obj: cpp/benchmarks.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/gogo_data_scrubber-benchmarks.pb.obj -MD -MP -MF cpp/$(DEPDIR)/gogo_data_scrubber-benchmarks.pb.Tpo -c -o cpp/gogo_data_scrubber-benchmarks.pb.obj `if test -f 'cpp/benchmarks.pb.cc'; then $(CYGPATH_W) 'cpp/benchmarks.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/benchmarks.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/$(DEPDIR)/gogo_data_scrubber-benchmarks.pb.Tpo cpp/$(DEPDIR)/gogo_data_scrubber-benchmarks.pb.Po
#	$(AM_V_CXX)source='cpp/benchmarks.pb.cc' object='cpp/gogo_data_scrubber-benchmarks.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/gogo_data_scrubber-benchmarks.pb.obj `if test -f 'cpp/benchmarks.pb.cc'; then $(CYGPATH_W) 'cpp/benchmarks.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/benchmarks.pb.cc'; fi`

cpp/datasets/google_message1/proto3/gogo_data_scrubber-benchmark_message1_proto3.pb.o: cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message1/proto3/gogo_data_scrubber-benchmark_message1_proto3.pb.o -MD -MP -MF cpp/datasets/google_message1/proto3/$(DEPDIR)/gogo_data_scrubber-benchmark_message1_proto3.pb.Tpo -c -o cpp/datasets/google_message1/proto3/gogo_data_scrubber-benchmark_message1_proto3.pb.o `test -f 'cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message1/proto3/$(DEPDIR)/gogo_data_scrubber-benchmark_message1_proto3.pb.Tpo cpp/datasets/google_message1/proto3/$(DEPDIR)/gogo_data_scrubber-benchmark_message1_proto3.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc' object='cpp/datasets/google_message1/proto3/gogo_data_scrubber-benchmark_message1_proto3.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message1/proto3/gogo_data_scrubber-benchmark_message1_proto3.pb.o `test -f 'cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc

cpp/datasets/google_message1/proto3/gogo_data_scrubber-benchmark_message1_proto3.pb.obj: cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message1/proto3/gogo_data_scrubber-benchmark_message1_proto3.pb.obj -MD -MP -MF cpp/datasets/google_message1/proto3/$(DEPDIR)/gogo_data_scrubber-benchmark_message1_proto3.pb.Tpo -c -o cpp/datasets/google_message1/proto3/gogo_data_scrubber-benchmark_message1_proto3.pb.obj `if test -f 'cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message1/proto3/$(DEPDIR)/gogo_data_scrubber-benchmark_message1_proto3.pb.Tpo cpp/datasets/google_message1/proto3/$(DEPDIR)/gogo_data_scrubber-benchmark_message1_proto3.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc' object='cpp/datasets/google_message1/proto3/gogo_data_scrubber-benchmark_message1_proto3.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message1/proto3/gogo_data_scrubber-benchmark_message1_proto3.pb.obj `if test -f 'cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc'; fi`

cpp/datasets/google_message1/proto2/gogo_data_scrubber-benchmark_message1_proto2.pb.o: cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message1/proto2/gogo_data_scrubber-benchmark_message1_proto2.pb.o -MD -MP -MF cpp/datasets/google_message1/proto2/$(DEPDIR)/gogo_data_scrubber-benchmark_message1_proto2.pb.Tpo -c -o cpp/datasets/google_message1/proto2/gogo_data_scrubber-benchmark_message1_proto2.pb.o `test -f 'cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message1/proto2/$(DEPDIR)/gogo_data_scrubber-benchmark_message1_proto2.pb.Tpo cpp/datasets/google_message1/proto2/$(DEPDIR)/gogo_data_scrubber-benchmark_message1_proto2.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc' object='cpp/datasets/google_message1/proto2/gogo_data_scrubber-benchmark_message1_proto2.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message1/proto2/gogo_data_scrubber-benchmark_message1_proto2.pb.o `test -f 'cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc

cpp/datasets/google_message1/proto2/gogo_data_scrubber-benchmark_message1_proto2.pb.obj: cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message1/proto2/gogo_data_scrubber-benchmark_message1_proto2.pb.obj -MD -MP -MF cpp/datasets/google_message1/proto2/$(DEPDIR)/gogo_data_scrubber-benchmark_message1_proto2.pb.Tpo -c -o cpp/datasets/google_message1/proto2/gogo_data_scrubber-benchmark_message1_proto2.pb.obj `if test -f 'cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message1/proto2/$(DEPDIR)/gogo_data_scrubber-benchmark_message1_proto2.pb.Tpo cpp/datasets/google_message1/proto2/$(DEPDIR)/gogo_data_scrubber-benchmark_message1_proto2.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc' object='cpp/datasets/google_message1/proto2/gogo_data_scrubber-benchmark_message1_proto2.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message1/proto2/gogo_data_scrubber-benchmark_message1_proto2.pb.obj `if test -f 'cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc'; fi`

cpp/datasets/google_message2/gogo_data_scrubber-benchmark_message2.pb.o: cpp/datasets/google_message2/benchmark_message2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message2/gogo_data_scrubber-benchmark_message2.pb.o -MD -MP -MF cpp/datasets/google_message2/$(DEPDIR)/gogo_data_scrubber-benchmark_message2.pb.Tpo -c -o cpp/datasets/google_message2/gogo_data_scrubber-benchmark_message2.pb.o `test -f 'cpp/datasets/google_message2/benchmark_message2.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message2/benchmark_message2.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message2/$(DEPDIR)/gogo_data_scrubber-benchmark_message2.pb.Tpo cpp/datasets/google_message2/$(DEPDIR)/gogo_data_scrubber-benchmark_message2.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message2/benchmark_message2.pb.cc' object='cpp/datasets/google_message2/gogo_data_scrubber-benchmark_message2.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message2/gogo_data_scrubber-benchmark_message2.pb.o `test -f 'cpp/datasets/google_message2/benchmark_message2.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message2/benchmark_message2.pb.cc

cpp/datasets/google_message2/gogo_data_scrubber-benchmark_message2.pb.obj: cpp/datasets/google_message2/benchmark_message2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message2/gogo_data_scrubber-benchmark_message2.pb.obj -MD -MP -MF cpp/datasets/google_message2/$(DEPDIR)/gogo_data_scrubber-benchmark_message2.pb.Tpo -c -o cpp/datasets/google_message2/gogo_data_scrubber-benchmark_message2.pb.obj `if test -f 'cpp/datasets/google_message2/benchmark_message2.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message2/benchmark_message2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message2/benchmark_message2.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message2/$(DEPDIR)/gogo_data_scrubber-benchmark_message2.pb.Tpo cpp/datasets/google_message2/$(DEPDIR)/gogo_data_scrubber-benchmark_message2.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message2/benchmark_message2.pb.cc' object='cpp/datasets/google_message2/gogo_data_scrubber-benchmark_message2.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message2/gogo_data_scrubber-benchmark_message2.pb.obj `if test -f 'cpp/datasets/google_message2/benchmark_message2.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message2/benchmark_message2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message2/benchmark_message2.pb.cc'; fi`

cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3.pb.o: cpp/datasets/google_message3/benchmark_message3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3.pb.o -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3.pb.Tpo -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3.pb.cc' object='cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3.pb.cc

cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3.pb.obj: cpp/datasets/google_message3/benchmark_message3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3.pb.obj -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3.pb.Tpo -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3.pb.cc' object='cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3.pb.cc'; fi`

cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_1.pb.o: cpp/datasets/google_message3/benchmark_message3_1.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_1.pb.o -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_1.pb.Tpo -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_1.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_1.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_1.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_1.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_1.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_1.pb.cc' object='cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_1.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_1.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_1.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_1.pb.cc

cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_1.pb.obj: cpp/datasets/google_message3/benchmark_message3_1.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_1.pb.obj -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_1.pb.Tpo -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_1.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_1.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_1.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_1.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_1.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_1.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_1.pb.cc' object='cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_1.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_1.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_1.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_1.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_1.pb.cc'; fi`

cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_2.pb.o: cpp/datasets/google_message3/benchmark_message3_2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_2.pb.o -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_2.pb.Tpo -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_2.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_2.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_2.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_2.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_2.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_2.pb.cc' object='cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_2.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_2.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_2.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_2.pb.cc

cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_2.pb.obj: cpp/datasets/google_message3/benchmark_message3_2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_2.pb.obj -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_2.pb.Tpo -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_2.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_2.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_2.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_2.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_2.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_2.pb.cc' object='cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_2.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_2.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_2.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_2.pb.cc'; fi`

cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_3.pb.o: cpp/datasets/google_message3/benchmark_message3_3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_3.pb.o -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_3.pb.Tpo -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_3.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_3.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_3.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_3.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_3.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_3.pb.cc' object='cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_3.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_3.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_3.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_3.pb.cc

cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_3.pb.obj: cpp/datasets/google_message3/benchmark_message3_3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_3.pb.obj -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_3.pb.Tpo -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_3.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_3.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_3.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_3.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_3.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_3.pb.cc' object='cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_3.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_3.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_3.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_3.pb.cc'; fi`

cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_4.pb.o: cpp/datasets/google_message3/benchmark_message3_4.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_4.pb.o -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_4.pb.Tpo -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_4.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_4.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_4.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_4.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_4.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_4.pb.cc' object='cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_4.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_4.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_4.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_4.pb.cc

cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_4.pb.obj: cpp/datasets/google_message3/benchmark_message3_4.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_4.pb.obj -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_4.pb.Tpo -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_4.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_4.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_4.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_4.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_4.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_4.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_4.pb.cc' object='cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_4.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_4.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_4.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_4.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_4.pb.cc'; fi`

cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_5.pb.o: cpp/datasets/google_message3/benchmark_message3_5.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_5.pb.o -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_5.pb.Tpo -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_5.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_5.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_5.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_5.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_5.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_5.pb.cc' object='cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_5.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_5.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_5.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_5.pb.cc

cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_5.pb.obj: cpp/datasets/google_message3/benchmark_message3_5.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_5.pb.obj -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_5.pb.Tpo -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_5.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_5.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_5.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_5.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_5.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_5.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_5.pb.cc' object='cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_5.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_5.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_5.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_5.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_5.pb.cc'; fi`

cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_6.pb.o: cpp/datasets/google_message3/benchmark_message3_6.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_6.pb.o -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_6.pb.Tpo -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_6.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_6.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_6.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_6.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_6.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_6.pb.cc' object='cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_6.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_6.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_6.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_6.pb.cc

cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_6.pb.obj: cpp/datasets/google_message3/benchmark_message3_6.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_6.pb.obj -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_6.pb.Tpo -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_6.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_6.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_6.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_6.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_6.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_6.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_6.pb.cc' object='cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_6.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_6.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_6.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_6.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_6.pb.cc'; fi`

cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_7.pb.o: cpp/datasets/google_message3/benchmark_message3_7.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_7.pb.o -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_7.pb.Tpo -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_7.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_7.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_7.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_7.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_7.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_7.pb.cc' object='cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_7.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_7.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_7.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_7.pb.cc

cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_7.pb.obj: cpp/datasets/google_message3/benchmark_message3_7.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_7.pb.obj -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_7.pb.Tpo -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_7.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_7.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_7.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_7.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_7.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_7.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_7.pb.cc' object='cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_7.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_7.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_7.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_7.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_7.pb.cc'; fi`

cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_8.pb.o: cpp/datasets/google_message3/benchmark_message3_8.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_8.pb.o -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_8.pb.Tpo -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_8.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_8.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_8.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_8.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_8.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_8.pb.cc' object='cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_8.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_8.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_8.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_8.pb.cc

cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_8.pb.obj: cpp/datasets/google_message3/benchmark_message3_8.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_8.pb.obj -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_8.pb.Tpo -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_8.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_8.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_8.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_8.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_8.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_8.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_8.pb.cc' object='cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_8.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/gogo_data_scrubber-benchmark_message3_8.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_8.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_8.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_8.pb.cc'; fi`

cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4.pb.o: cpp/datasets/google_message4/benchmark_message4.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4.pb.o -MD -MP -MF cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4.pb.Tpo -c -o cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4.pb.o `test -f 'cpp/datasets/google_message4/benchmark_message4.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message4/benchmark_message4.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4.pb.Tpo cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message4/benchmark_message4.pb.cc' object='cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4.pb.o `test -f 'cpp/datasets/google_message4/benchmark_message4.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message4/benchmark_message4.pb.cc

cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4.pb.obj: cpp/datasets/google_message4/benchmark_message4.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4.pb.obj -MD -MP -MF cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4.pb.Tpo -c -o cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4.pb.obj `if test -f 'cpp/datasets/google_message4/benchmark_message4.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message4/benchmark_message4.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message4/benchmark_message4.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4.pb.Tpo cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message4/benchmark_message4.pb.cc' object='cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4.pb.obj `if test -f 'cpp/datasets/google_message4/benchmark_message4.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message4/benchmark_message4.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message4/benchmark_message4.pb.cc'; fi`

cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_1.pb.o: cpp/datasets/google_message4/benchmark_message4_1.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_1.pb.o -MD -MP -MF cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4_1.pb.Tpo -c -o cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_1.pb.o `test -f 'cpp/datasets/google_message4/benchmark_message4_1.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message4/benchmark_message4_1.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4_1.pb.Tpo cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4_1.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message4/benchmark_message4_1.pb.cc' object='cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_1.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_1.pb.o `test -f 'cpp/datasets/google_message4/benchmark_message4_1.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message4/benchmark_message4_1.pb.cc

cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_1.pb.obj: cpp/datasets/google_message4/benchmark_message4_1.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_1.pb.obj -MD -MP -MF cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4_1.pb.Tpo -c -o cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_1.pb.obj `if test -f 'cpp/datasets/google_message4/benchmark_message4_1.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message4/benchmark_message4_1.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message4/benchmark_message4_1.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4_1.pb.Tpo cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4_1.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message4/benchmark_message4_1.pb.cc' object='cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_1.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_1.pb.obj `if test -f 'cpp/datasets/google_message4/benchmark_message4_1.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message4/benchmark_message4_1.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message4/benchmark_message4_1.pb.cc'; fi`

cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_2.pb.o: cpp/datasets/google_message4/benchmark_message4_2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_2.pb.o -MD -MP -MF cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4_2.pb.Tpo -c -o cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_2.pb.o `test -f 'cpp/datasets/google_message4/benchmark_message4_2.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message4/benchmark_message4_2.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4_2.pb.Tpo cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4_2.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message4/benchmark_message4_2.pb.cc' object='cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_2.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_2.pb.o `test -f 'cpp/datasets/google_message4/benchmark_message4_2.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message4/benchmark_message4_2.pb.cc

cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_2.pb.obj: cpp/datasets/google_message4/benchmark_message4_2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_2.pb.obj -MD -MP -MF cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4_2.pb.Tpo -c -o cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_2.pb.obj `if test -f 'cpp/datasets/google_message4/benchmark_message4_2.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message4/benchmark_message4_2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message4/benchmark_message4_2.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4_2.pb.Tpo cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4_2.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message4/benchmark_message4_2.pb.cc' object='cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_2.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_2.pb.obj `if test -f 'cpp/datasets/google_message4/benchmark_message4_2.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message4/benchmark_message4_2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message4/benchmark_message4_2.pb.cc'; fi`

cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_3.pb.o: cpp/datasets/google_message4/benchmark_message4_3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_3.pb.o -MD -MP -MF cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4_3.pb.Tpo -c -o cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_3.pb.o `test -f 'cpp/datasets/google_message4/benchmark_message4_3.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message4/benchmark_message4_3.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4_3.pb.Tpo cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4_3.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message4/benchmark_message4_3.pb.cc' object='cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_3.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_3.pb.o `test -f 'cpp/datasets/google_message4/benchmark_message4_3.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message4/benchmark_message4_3.pb.cc

cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_3.pb.obj: cpp/datasets/google_message4/benchmark_message4_3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_3.pb.obj -MD -MP -MF cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4_3.pb.Tpo -c -o cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_3.pb.obj `if test -f 'cpp/datasets/google_message4/benchmark_message4_3.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message4/benchmark_message4_3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message4/benchmark_message4_3.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4_3.pb.Tpo cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4_3.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message4/benchmark_message4_3.pb.cc' object='cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_3.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(gogo_data_scrubber_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message4/gogo_data_scrubber-benchmark_message4_3.pb.obj `if test -f 'cpp/datasets/google_message4/benchmark_message4_3.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message4/benchmark_message4_3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message4/benchmark_message4_3.pb.cc'; fi`

util/proto3_data_stripper-proto3_data_stripper.o: util/proto3_data_stripper.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT util/proto3_data_stripper-proto3_data_stripper.o -MD -MP -MF util/$(DEPDIR)/proto3_data_stripper-proto3_data_stripper.Tpo -c -o util/proto3_data_stripper-proto3_data_stripper.o `test -f 'util/proto3_data_stripper.cc' || echo '$(srcdir)/'`util/proto3_data_stripper.cc
	$(AM_V_at)$(am__mv) util/$(DEPDIR)/proto3_data_stripper-proto3_data_stripper.Tpo util/$(DEPDIR)/proto3_data_stripper-proto3_data_stripper.Po
#	$(AM_V_CXX)source='util/proto3_data_stripper.cc' object='util/proto3_data_stripper-proto3_data_stripper.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o util/proto3_data_stripper-proto3_data_stripper.o `test -f 'util/proto3_data_stripper.cc' || echo '$(srcdir)/'`util/proto3_data_stripper.cc

util/proto3_data_stripper-proto3_data_stripper.obj: util/proto3_data_stripper.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT util/proto3_data_stripper-proto3_data_stripper.obj -MD -MP -MF util/$(DEPDIR)/proto3_data_stripper-proto3_data_stripper.Tpo -c -o util/proto3_data_stripper-proto3_data_stripper.obj `if test -f 'util/proto3_data_stripper.cc'; then $(CYGPATH_W) 'util/proto3_data_stripper.cc'; else $(CYGPATH_W) '$(srcdir)/util/proto3_data_stripper.cc'; fi`
	$(AM_V_at)$(am__mv) util/$(DEPDIR)/proto3_data_stripper-proto3_data_stripper.Tpo util/$(DEPDIR)/proto3_data_stripper-proto3_data_stripper.Po
#	$(AM_V_CXX)source='util/proto3_data_stripper.cc' object='util/proto3_data_stripper-proto3_data_stripper.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o util/proto3_data_stripper-proto3_data_stripper.obj `if test -f 'util/proto3_data_stripper.cc'; then $(CYGPATH_W) 'util/proto3_data_stripper.cc'; else $(CYGPATH_W) '$(srcdir)/util/proto3_data_stripper.cc'; fi`

cpp/proto3_data_stripper-benchmarks.pb.o: cpp/benchmarks.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/proto3_data_stripper-benchmarks.pb.o -MD -MP -MF cpp/$(DEPDIR)/proto3_data_stripper-benchmarks.pb.Tpo -c -o cpp/proto3_data_stripper-benchmarks.pb.o `test -f 'cpp/benchmarks.pb.cc' || echo '$(srcdir)/'`cpp/benchmarks.pb.cc
	$(AM_V_at)$(am__mv) cpp/$(DEPDIR)/proto3_data_stripper-benchmarks.pb.Tpo cpp/$(DEPDIR)/proto3_data_stripper-benchmarks.pb.Po
#	$(AM_V_CXX)source='cpp/benchmarks.pb.cc' object='cpp/proto3_data_stripper-benchmarks.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/proto3_data_stripper-benchmarks.pb.o `test -f 'cpp/benchmarks.pb.cc' || echo '$(srcdir)/'`cpp/benchmarks.pb.cc

cpp/proto3_data_stripper-benchmarks.pb.obj: cpp/benchmarks.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/proto3_data_stripper-benchmarks.pb.obj -MD -MP -MF cpp/$(DEPDIR)/proto3_data_stripper-benchmarks.pb.Tpo -c -o cpp/proto3_data_stripper-benchmarks.pb.obj `if test -f 'cpp/benchmarks.pb.cc'; then $(CYGPATH_W) 'cpp/benchmarks.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/benchmarks.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/$(DEPDIR)/proto3_data_stripper-benchmarks.pb.Tpo cpp/$(DEPDIR)/proto3_data_stripper-benchmarks.pb.Po
#	$(AM_V_CXX)source='cpp/benchmarks.pb.cc' object='cpp/proto3_data_stripper-benchmarks.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/proto3_data_stripper-benchmarks.pb.obj `if test -f 'cpp/benchmarks.pb.cc'; then $(CYGPATH_W) 'cpp/benchmarks.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/benchmarks.pb.cc'; fi`

cpp/datasets/google_message1/proto3/proto3_data_stripper-benchmark_message1_proto3.pb.o: cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message1/proto3/proto3_data_stripper-benchmark_message1_proto3.pb.o -MD -MP -MF cpp/datasets/google_message1/proto3/$(DEPDIR)/proto3_data_stripper-benchmark_message1_proto3.pb.Tpo -c -o cpp/datasets/google_message1/proto3/proto3_data_stripper-benchmark_message1_proto3.pb.o `test -f 'cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message1/proto3/$(DEPDIR)/proto3_data_stripper-benchmark_message1_proto3.pb.Tpo cpp/datasets/google_message1/proto3/$(DEPDIR)/proto3_data_stripper-benchmark_message1_proto3.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc' object='cpp/datasets/google_message1/proto3/proto3_data_stripper-benchmark_message1_proto3.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message1/proto3/proto3_data_stripper-benchmark_message1_proto3.pb.o `test -f 'cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc

cpp/datasets/google_message1/proto3/proto3_data_stripper-benchmark_message1_proto3.pb.obj: cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message1/proto3/proto3_data_stripper-benchmark_message1_proto3.pb.obj -MD -MP -MF cpp/datasets/google_message1/proto3/$(DEPDIR)/proto3_data_stripper-benchmark_message1_proto3.pb.Tpo -c -o cpp/datasets/google_message1/proto3/proto3_data_stripper-benchmark_message1_proto3.pb.obj `if test -f 'cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message1/proto3/$(DEPDIR)/proto3_data_stripper-benchmark_message1_proto3.pb.Tpo cpp/datasets/google_message1/proto3/$(DEPDIR)/proto3_data_stripper-benchmark_message1_proto3.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc' object='cpp/datasets/google_message1/proto3/proto3_data_stripper-benchmark_message1_proto3.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message1/proto3/proto3_data_stripper-benchmark_message1_proto3.pb.obj `if test -f 'cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message1/proto3/benchmark_message1_proto3.pb.cc'; fi`

cpp/datasets/google_message1/proto2/proto3_data_stripper-benchmark_message1_proto2.pb.o: cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message1/proto2/proto3_data_stripper-benchmark_message1_proto2.pb.o -MD -MP -MF cpp/datasets/google_message1/proto2/$(DEPDIR)/proto3_data_stripper-benchmark_message1_proto2.pb.Tpo -c -o cpp/datasets/google_message1/proto2/proto3_data_stripper-benchmark_message1_proto2.pb.o `test -f 'cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message1/proto2/$(DEPDIR)/proto3_data_stripper-benchmark_message1_proto2.pb.Tpo cpp/datasets/google_message1/proto2/$(DEPDIR)/proto3_data_stripper-benchmark_message1_proto2.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc' object='cpp/datasets/google_message1/proto2/proto3_data_stripper-benchmark_message1_proto2.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message1/proto2/proto3_data_stripper-benchmark_message1_proto2.pb.o `test -f 'cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc

cpp/datasets/google_message1/proto2/proto3_data_stripper-benchmark_message1_proto2.pb.obj: cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message1/proto2/proto3_data_stripper-benchmark_message1_proto2.pb.obj -MD -MP -MF cpp/datasets/google_message1/proto2/$(DEPDIR)/proto3_data_stripper-benchmark_message1_proto2.pb.Tpo -c -o cpp/datasets/google_message1/proto2/proto3_data_stripper-benchmark_message1_proto2.pb.obj `if test -f 'cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message1/proto2/$(DEPDIR)/proto3_data_stripper-benchmark_message1_proto2.pb.Tpo cpp/datasets/google_message1/proto2/$(DEPDIR)/proto3_data_stripper-benchmark_message1_proto2.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc' object='cpp/datasets/google_message1/proto2/proto3_data_stripper-benchmark_message1_proto2.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message1/proto2/proto3_data_stripper-benchmark_message1_proto2.pb.obj `if test -f 'cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message1/proto2/benchmark_message1_proto2.pb.cc'; fi`

cpp/datasets/google_message2/proto3_data_stripper-benchmark_message2.pb.o: cpp/datasets/google_message2/benchmark_message2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message2/proto3_data_stripper-benchmark_message2.pb.o -MD -MP -MF cpp/datasets/google_message2/$(DEPDIR)/proto3_data_stripper-benchmark_message2.pb.Tpo -c -o cpp/datasets/google_message2/proto3_data_stripper-benchmark_message2.pb.o `test -f 'cpp/datasets/google_message2/benchmark_message2.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message2/benchmark_message2.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message2/$(DEPDIR)/proto3_data_stripper-benchmark_message2.pb.Tpo cpp/datasets/google_message2/$(DEPDIR)/proto3_data_stripper-benchmark_message2.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message2/benchmark_message2.pb.cc' object='cpp/datasets/google_message2/proto3_data_stripper-benchmark_message2.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message2/proto3_data_stripper-benchmark_message2.pb.o `test -f 'cpp/datasets/google_message2/benchmark_message2.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message2/benchmark_message2.pb.cc

cpp/datasets/google_message2/proto3_data_stripper-benchmark_message2.pb.obj: cpp/datasets/google_message2/benchmark_message2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message2/proto3_data_stripper-benchmark_message2.pb.obj -MD -MP -MF cpp/datasets/google_message2/$(DEPDIR)/proto3_data_stripper-benchmark_message2.pb.Tpo -c -o cpp/datasets/google_message2/proto3_data_stripper-benchmark_message2.pb.obj `if test -f 'cpp/datasets/google_message2/benchmark_message2.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message2/benchmark_message2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message2/benchmark_message2.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message2/$(DEPDIR)/proto3_data_stripper-benchmark_message2.pb.Tpo cpp/datasets/google_message2/$(DEPDIR)/proto3_data_stripper-benchmark_message2.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message2/benchmark_message2.pb.cc' object='cpp/datasets/google_message2/proto3_data_stripper-benchmark_message2.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message2/proto3_data_stripper-benchmark_message2.pb.obj `if test -f 'cpp/datasets/google_message2/benchmark_message2.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message2/benchmark_message2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message2/benchmark_message2.pb.cc'; fi`

cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3.pb.o: cpp/datasets/google_message3/benchmark_message3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3.pb.o -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3.pb.Tpo -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3.pb.cc' object='cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3.pb.cc

cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3.pb.obj: cpp/datasets/google_message3/benchmark_message3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3.pb.obj -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3.pb.Tpo -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3.pb.cc' object='cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3.pb.cc'; fi`

cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_1.pb.o: cpp/datasets/google_message3/benchmark_message3_1.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_1.pb.o -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_1.pb.Tpo -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_1.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_1.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_1.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_1.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_1.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_1.pb.cc' object='cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_1.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_1.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_1.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_1.pb.cc

cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_1.pb.obj: cpp/datasets/google_message3/benchmark_message3_1.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_1.pb.obj -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_1.pb.Tpo -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_1.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_1.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_1.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_1.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_1.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_1.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_1.pb.cc' object='cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_1.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_1.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_1.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_1.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_1.pb.cc'; fi`

cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_2.pb.o: cpp/datasets/google_message3/benchmark_message3_2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_2.pb.o -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_2.pb.Tpo -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_2.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_2.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_2.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_2.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_2.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_2.pb.cc' object='cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_2.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_2.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_2.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_2.pb.cc

cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_2.pb.obj: cpp/datasets/google_message3/benchmark_message3_2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_2.pb.obj -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_2.pb.Tpo -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_2.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_2.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_2.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_2.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_2.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_2.pb.cc' object='cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_2.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_2.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_2.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_2.pb.cc'; fi`

cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_3.pb.o: cpp/datasets/google_message3/benchmark_message3_3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_3.pb.o -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_3.pb.Tpo -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_3.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_3.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_3.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_3.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_3.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_3.pb.cc' object='cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_3.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_3.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_3.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_3.pb.cc

cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_3.pb.obj: cpp/datasets/google_message3/benchmark_message3_3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_3.pb.obj -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_3.pb.Tpo -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_3.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_3.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_3.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_3.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_3.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_3.pb.cc' object='cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_3.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_3.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_3.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_3.pb.cc'; fi`

cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_4.pb.o: cpp/datasets/google_message3/benchmark_message3_4.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_4.pb.o -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_4.pb.Tpo -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_4.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_4.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_4.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_4.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_4.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_4.pb.cc' object='cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_4.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_4.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_4.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_4.pb.cc

cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_4.pb.obj: cpp/datasets/google_message3/benchmark_message3_4.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_4.pb.obj -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_4.pb.Tpo -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_4.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_4.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_4.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_4.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_4.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_4.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_4.pb.cc' object='cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_4.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_4.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_4.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_4.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_4.pb.cc'; fi`

cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_5.pb.o: cpp/datasets/google_message3/benchmark_message3_5.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_5.pb.o -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_5.pb.Tpo -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_5.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_5.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_5.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_5.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_5.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_5.pb.cc' object='cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_5.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_5.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_5.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_5.pb.cc

cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_5.pb.obj: cpp/datasets/google_message3/benchmark_message3_5.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_5.pb.obj -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_5.pb.Tpo -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_5.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_5.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_5.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_5.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_5.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_5.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_5.pb.cc' object='cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_5.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_5.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_5.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_5.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_5.pb.cc'; fi`

cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_6.pb.o: cpp/datasets/google_message3/benchmark_message3_6.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_6.pb.o -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_6.pb.Tpo -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_6.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_6.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_6.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_6.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_6.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_6.pb.cc' object='cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_6.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_6.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_6.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_6.pb.cc

cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_6.pb.obj: cpp/datasets/google_message3/benchmark_message3_6.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_6.pb.obj -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_6.pb.Tpo -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_6.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_6.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_6.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_6.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_6.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_6.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_6.pb.cc' object='cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_6.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_6.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_6.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_6.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_6.pb.cc'; fi`

cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_7.pb.o: cpp/datasets/google_message3/benchmark_message3_7.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_7.pb.o -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_7.pb.Tpo -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_7.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_7.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_7.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_7.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_7.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_7.pb.cc' object='cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_7.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_7.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_7.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_7.pb.cc

cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_7.pb.obj: cpp/datasets/google_message3/benchmark_message3_7.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_7.pb.obj -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_7.pb.Tpo -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_7.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_7.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_7.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_7.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_7.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_7.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_7.pb.cc' object='cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_7.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_7.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_7.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_7.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_7.pb.cc'; fi`

cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_8.pb.o: cpp/datasets/google_message3/benchmark_message3_8.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_8.pb.o -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_8.pb.Tpo -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_8.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_8.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_8.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_8.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_8.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_8.pb.cc' object='cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_8.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_8.pb.o `test -f 'cpp/datasets/google_message3/benchmark_message3_8.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message3/benchmark_message3_8.pb.cc

cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_8.pb.obj: cpp/datasets/google_message3/benchmark_message3_8.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_8.pb.obj -MD -MP -MF cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_8.pb.Tpo -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_8.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_8.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_8.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_8.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_8.pb.Tpo cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_8.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message3/benchmark_message3_8.pb.cc' object='cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_8.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message3/proto3_data_stripper-benchmark_message3_8.pb.obj `if test -f 'cpp/datasets/google_message3/benchmark_message3_8.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message3/benchmark_message3_8.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message3/benchmark_message3_8.pb.cc'; fi`

cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4.pb.o: cpp/datasets/google_message4/benchmark_message4.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4.pb.o -MD -MP -MF cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4.pb.Tpo -c -o cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4.pb.o `test -f 'cpp/datasets/google_message4/benchmark_message4.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message4/benchmark_message4.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4.pb.Tpo cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message4/benchmark_message4.pb.cc' object='cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4.pb.o `test -f 'cpp/datasets/google_message4/benchmark_message4.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message4/benchmark_message4.pb.cc

cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4.pb.obj: cpp/datasets/google_message4/benchmark_message4.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4.pb.obj -MD -MP -MF cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4.pb.Tpo -c -o cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4.pb.obj `if test -f 'cpp/datasets/google_message4/benchmark_message4.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message4/benchmark_message4.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message4/benchmark_message4.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4.pb.Tpo cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message4/benchmark_message4.pb.cc' object='cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4.pb.obj `if test -f 'cpp/datasets/google_message4/benchmark_message4.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message4/benchmark_message4.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message4/benchmark_message4.pb.cc'; fi`

cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_1.pb.o: cpp/datasets/google_message4/benchmark_message4_1.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_1.pb.o -MD -MP -MF cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4_1.pb.Tpo -c -o cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_1.pb.o `test -f 'cpp/datasets/google_message4/benchmark_message4_1.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message4/benchmark_message4_1.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4_1.pb.Tpo cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4_1.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message4/benchmark_message4_1.pb.cc' object='cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_1.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_1.pb.o `test -f 'cpp/datasets/google_message4/benchmark_message4_1.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message4/benchmark_message4_1.pb.cc

cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_1.pb.obj: cpp/datasets/google_message4/benchmark_message4_1.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_1.pb.obj -MD -MP -MF cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4_1.pb.Tpo -c -o cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_1.pb.obj `if test -f 'cpp/datasets/google_message4/benchmark_message4_1.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message4/benchmark_message4_1.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message4/benchmark_message4_1.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4_1.pb.Tpo cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4_1.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message4/benchmark_message4_1.pb.cc' object='cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_1.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_1.pb.obj `if test -f 'cpp/datasets/google_message4/benchmark_message4_1.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message4/benchmark_message4_1.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message4/benchmark_message4_1.pb.cc'; fi`

cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_2.pb.o: cpp/datasets/google_message4/benchmark_message4_2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_2.pb.o -MD -MP -MF cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4_2.pb.Tpo -c -o cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_2.pb.o `test -f 'cpp/datasets/google_message4/benchmark_message4_2.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message4/benchmark_message4_2.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4_2.pb.Tpo cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4_2.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message4/benchmark_message4_2.pb.cc' object='cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_2.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_2.pb.o `test -f 'cpp/datasets/google_message4/benchmark_message4_2.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message4/benchmark_message4_2.pb.cc

cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_2.pb.obj: cpp/datasets/google_message4/benchmark_message4_2.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_2.pb.obj -MD -MP -MF cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4_2.pb.Tpo -c -o cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_2.pb.obj `if test -f 'cpp/datasets/google_message4/benchmark_message4_2.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message4/benchmark_message4_2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message4/benchmark_message4_2.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4_2.pb.Tpo cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4_2.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message4/benchmark_message4_2.pb.cc' object='cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_2.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_2.pb.obj `if test -f 'cpp/datasets/google_message4/benchmark_message4_2.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message4/benchmark_message4_2.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message4/benchmark_message4_2.pb.cc'; fi`

cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_3.pb.o: cpp/datasets/google_message4/benchmark_message4_3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_3.pb.o -MD -MP -MF cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4_3.pb.Tpo -c -o cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_3.pb.o `test -f 'cpp/datasets/google_message4/benchmark_message4_3.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message4/benchmark_message4_3.pb.cc
	$(AM_V_at)$(am__mv) cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4_3.pb.Tpo cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4_3.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message4/benchmark_message4_3.pb.cc' object='cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_3.pb.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_3.pb.o `test -f 'cpp/datasets/google_message4/benchmark_message4_3.pb.cc' || echo '$(srcdir)/'`cpp/datasets/google_message4/benchmark_message4_3.pb.cc

cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_3.pb.obj: cpp/datasets/google_message4/benchmark_message4_3.pb.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_3.pb.obj -MD -MP -MF cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4_3.pb.Tpo -c -o cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_3.pb.obj `if test -f 'cpp/datasets/google_message4/benchmark_message4_3.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message4/benchmark_message4_3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message4/benchmark_message4_3.pb.cc'; fi`
	$(AM_V_at)$(am__mv) cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4_3.pb.Tpo cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4_3.pb.Po
#	$(AM_V_CXX)source='cpp/datasets/google_message4/benchmark_message4_3.pb.cc' object='cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_3.pb.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(proto3_data_stripper_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o cpp/datasets/google_message4/proto3_data_stripper-benchmark_message4_3.pb.obj `if test -f 'cpp/datasets/google_message4/benchmark_message4_3.pb.cc'; then $(CYGPATH_W) 'cpp/datasets/google_message4/benchmark_message4_3.pb.cc'; else $(CYGPATH_W) '$(srcdir)/cpp/datasets/google_message4/benchmark_message4_3.pb.cc'; fi`

util/protoc_gen_gogoproto-protoc-gen-gogoproto.o: util/protoc-gen-gogoproto.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(protoc_gen_gogoproto_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT util/protoc_gen_gogoproto-protoc-gen-gogoproto.o -MD -MP -MF util/$(DEPDIR)/protoc_gen_gogoproto-protoc-gen-gogoproto.Tpo -c -o util/protoc_gen_gogoproto-protoc-gen-gogoproto.o `test -f 'util/protoc-gen-gogoproto.cc' || echo '$(srcdir)/'`util/protoc-gen-gogoproto.cc
	$(AM_V_at)$(am__mv) util/$(DEPDIR)/protoc_gen_gogoproto-protoc-gen-gogoproto.Tpo util/$(DEPDIR)/protoc_gen_gogoproto-protoc-gen-gogoproto.Po
#	$(AM_V_CXX)source='util/protoc-gen-gogoproto.cc' object='util/protoc_gen_gogoproto-protoc-gen-gogoproto.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(protoc_gen_gogoproto_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o util/protoc_gen_gogoproto-protoc-gen-gogoproto.o `test -f 'util/protoc-gen-gogoproto.cc' || echo '$(srcdir)/'`util/protoc-gen-gogoproto.cc

util/protoc_gen_gogoproto-protoc-gen-gogoproto.obj: util/protoc-gen-gogoproto.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(protoc_gen_gogoproto_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT util/protoc_gen_gogoproto-protoc-gen-gogoproto.obj -MD -MP -MF util/$(DEPDIR)/protoc_gen_gogoproto-protoc-gen-gogoproto.Tpo -c -o util/protoc_gen_gogoproto-protoc-gen-gogoproto.obj `if test -f 'util/protoc-gen-gogoproto.cc'; then $(CYGPATH_W) 'util/protoc-gen-gogoproto.cc'; else $(CYGPATH_W) '$(srcdir)/util/protoc-gen-gogoproto.cc'; fi`
	$(AM_V_at)$(am__mv) util/$(DEPDIR)/protoc_gen_gogoproto-protoc-gen-gogoproto.Tpo util/$(DEPDIR)/protoc_gen_gogoproto-protoc-gen-gogoproto.Po
#	$(AM_V_CXX)source='util/protoc-gen-gogoproto.cc' object='util/protoc_gen_gogoproto-protoc-gen-gogoproto.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(protoc_gen_gogoproto_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o util/protoc_gen_gogoproto-protoc-gen-gogoproto.obj `if test -f 'util/protoc-gen-gogoproto.cc'; then $(CYGPATH_W) 'util/protoc-gen-gogoproto.cc'; else $(CYGPATH_W) '$(srcdir)/util/protoc-gen-gogoproto.cc'; fi`

util/protoc_gen_proto2_to_proto3-protoc-gen-proto2_to_proto3.o: util/protoc-gen-proto2_to_proto3.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(protoc_gen_proto2_to_proto3_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT util/protoc_gen_proto2_to_proto3-protoc-gen-proto2_to_proto3.o -MD -MP -MF util/$(DEPDIR)/protoc_gen_proto2_to_proto3-protoc-gen-proto2_to_proto3.Tpo -c -o util/protoc_gen_proto2_to_proto3-protoc-gen-proto2_to_proto3.o `test -f 'util/protoc-gen-proto2_to_proto3.cc' || echo '$(srcdir)/'`util/protoc-gen-proto2_to_proto3.cc
	$(AM_V_at)$(am__mv) util/$(DEPDIR)/protoc_gen_proto2_to_proto3-protoc-gen-proto2_to_proto3.Tpo util/$(DEPDIR)/protoc_gen_proto2_to_proto3-protoc-gen-proto2_to_proto3.Po
#	$(AM_V_CXX)source='util/protoc-gen-proto2_to_proto3.cc' object='util/protoc_gen_proto2_to_proto3-protoc-gen-proto2_to_proto3.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(protoc_gen_proto2_to_proto3_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o util/protoc_gen_proto2_to_proto3-protoc-gen-proto2_to_proto3.o `test -f 'util/protoc-gen-proto2_to_proto3.cc' || echo '$(srcdir)/'`util/protoc-gen-proto2_to_proto3.cc

util/protoc_gen_proto2_to_proto3-protoc-gen-proto2_to_proto3.obj: util/protoc-gen-proto2_to_proto3.cc
	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(protoc_gen_proto2_to_proto3_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -MT util/protoc_gen_proto2_to_proto3-protoc-gen-proto2_to_proto3.obj -MD -MP -MF util/$(DEPDIR)/protoc_gen_proto2_to_proto3-protoc-gen-proto2_to_proto3.Tpo -c -o util/protoc_gen_proto2_to_proto3-protoc-gen-proto2_to_proto3.obj `if test -f 'util/protoc-gen-proto2_to_proto3.cc'; then $(CYGPATH_W) 'util/protoc-gen-proto2_to_proto3.cc'; else $(CYGPATH_W) '$(srcdir)/util/protoc-gen-proto2_to_proto3.cc'; fi`
	$(AM_V_at)$(am__mv) util/$(DEPDIR)/protoc_gen_proto2_to_proto3-protoc-gen-proto2_to_proto3.Tpo util/$(DEPDIR)/protoc_gen_proto2_to_proto3-protoc-gen-proto2_to_proto3.Po
#	$(AM_V_CXX)source='util/protoc-gen-proto2_to_proto3.cc' object='util/protoc_gen_proto2_to_proto3-protoc-gen-proto2_to_proto3.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) \
#	$(AM_V_CXX_no)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(protoc_gen_proto2_to_proto3_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS) -c -o util/protoc_gen_proto2_to_proto3-protoc-gen-proto2_to_proto3.obj `if test -f 'util/protoc-gen-proto2_to_proto3.cc'; then $(CYGPATH_W) 'util/protoc-gen-proto2_to_proto3.cc'; else $(CYGPATH_W) '$(srcdir)/util/protoc-gen-proto2_to_proto3.cc'; fi`

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
	-rm -rf cpp/.libs cpp/_libs
	-rm -rf cpp/datasets/google_message1/proto2/.libs cpp/datasets/google_message1/proto2/_libs
	-rm -rf cpp/datasets/google_message1/proto3/.libs cpp/datasets/google_message1/proto3/_libs
	-rm -rf cpp/datasets/google_message2/.libs cpp/datasets/google_message2/_libs
	-rm -rf cpp/datasets/google_message3/.libs cpp/datasets/google_message3/_libs
	-rm -rf cpp/datasets/google_message4/.libs cpp/datasets/google_message4/_libs
	-rm -rf python/.libs python/_libs

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-am
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-am

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscopelist: cscopelist-am

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags
distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-am
all-am: Makefile $(PROGRAMS) $(LTLIBRARIES)
install-binPROGRAMS: install-libLTLIBRARIES

installdirs:
	for dir in "$(DESTDIR)$(bindir)" "$(DESTDIR)$(libdir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:
	-test -z "$(CLEANFILES)" || rm -f $(CLEANFILES)

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)
	-rm -f cpp/$(DEPDIR)/$(am__dirstamp)
	-rm -f cpp/$(am__dirstamp)
	-rm -f cpp/datasets/google_message1/proto2/$(DEPDIR)/$(am__dirstamp)
	-rm -f cpp/datasets/google_message1/proto2/$(am__dirstamp)
	-rm -f cpp/datasets/google_message1/proto3/$(DEPDIR)/$(am__dirstamp)
	-rm -f cpp/datasets/google_message1/proto3/$(am__dirstamp)
	-rm -f cpp/datasets/google_message2/$(DEPDIR)/$(am__dirstamp)
	-rm -f cpp/datasets/google_message2/$(am__dirstamp)
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
	-rm -f cpp/datasets/google_message3/$(am__dirstamp)
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/$(am__dirstamp)
	-rm -f cpp/datasets/google_message4/$(am__dirstamp)
	-rm -f gogo/cpp_no_group/$(DEPDIR)/$(am__dirstamp)
	-rm -f gogo/cpp_no_group/$(am__dirstamp)
	-rm -f gogo/cpp_no_group/datasets/google_message1/proto2/$(DEPDIR)/$(am__dirstamp)
	-rm -f gogo/cpp_no_group/datasets/google_message1/proto2/$(am__dirstamp)
	-rm -f gogo/cpp_no_group/datasets/google_message1/proto3/$(DEPDIR)/$(am__dirstamp)
	-rm -f gogo/cpp_no_group/datasets/google_message1/proto3/$(am__dirstamp)
	-rm -f gogo/cpp_no_group/datasets/google_message2/$(DEPDIR)/$(am__dirstamp)
	-rm -f gogo/cpp_no_group/datasets/google_message2/$(am__dirstamp)
	-rm -f gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/$(am__dirstamp)
	-rm -f gogo/cpp_no_group/datasets/google_message3/$(am__dirstamp)
	-rm -f gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/$(am__dirstamp)
	-rm -f gogo/cpp_no_group/datasets/google_message4/$(am__dirstamp)
	-rm -f python/$(DEPDIR)/$(am__dirstamp)
	-rm -f python/$(am__dirstamp)
	-rm -f util/$(DEPDIR)/$(am__dirstamp)
	-rm -f util/$(am__dirstamp)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
	-test -z "$(MAINTAINERCLEANFILES)" || rm -f $(MAINTAINERCLEANFILES)
clean: clean-am

clean-am: clean-binPROGRAMS clean-generic clean-libLTLIBRARIES \
	clean-libtool clean-local mostlyclean-am

distclean: distclean-am
		-rm -f cpp/$(DEPDIR)/benchmark-benchmarks.pb.Po
	-rm -f cpp/$(DEPDIR)/benchmark-cpp_benchmark.Po
	-rm -f cpp/$(DEPDIR)/gogo_data_scrubber-benchmarks.pb.Po
	-rm -f cpp/$(DEPDIR)/libbenchmark_messages_la-benchmarks.pb.Plo
	-rm -f cpp/$(DEPDIR)/proto3_data_stripper-benchmarks.pb.Po
	-rm -f cpp/datasets/google_message1/proto2/$(DEPDIR)/benchmark-benchmark_message1_proto2.pb.Po
	-rm -f cpp/datasets/google_message1/proto2/$(DEPDIR)/gogo_data_scrubber-benchmark_message1_proto2.pb.Po
	-rm -f cpp/datasets/google_message1/proto2/$(DEPDIR)/libbenchmark_messages_la-benchmark_message1_proto2.pb.Plo
	-rm -f cpp/datasets/google_message1/proto2/$(DEPDIR)/proto3_data_stripper-benchmark_message1_proto2.pb.Po
	-rm -f cpp/datasets/google_message1/proto3/$(DEPDIR)/benchmark-benchmark_message1_proto3.pb.Po
	-rm -f cpp/datasets/google_message1/proto3/$(DEPDIR)/gogo_data_scrubber-benchmark_message1_proto3.pb.Po
	-rm -f cpp/datasets/google_message1/proto3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message1_proto3.pb.Plo
	-rm -f cpp/datasets/google_message1/proto3/$(DEPDIR)/proto3_data_stripper-benchmark_message1_proto3.pb.Po
	-rm -f cpp/datasets/google_message2/$(DEPDIR)/benchmark-benchmark_message2.pb.Po
	-rm -f cpp/datasets/google_message2/$(DEPDIR)/gogo_data_scrubber-benchmark_message2.pb.Po
	-rm -f cpp/datasets/google_message2/$(DEPDIR)/libbenchmark_messages_la-benchmark_message2.pb.Plo
	-rm -f cpp/datasets/google_message2/$(DEPDIR)/proto3_data_stripper-benchmark_message2.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_1.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_2.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_3.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_4.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_5.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_6.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_7.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_8.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_1.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_2.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_3.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_4.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_5.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_6.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_7.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_8.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3.pb.Plo
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_1.pb.Plo
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_2.pb.Plo
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_3.pb.Plo
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_4.pb.Plo
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_5.pb.Plo
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_6.pb.Plo
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_7.pb.Plo
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_8.pb.Plo
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_1.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_2.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_3.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_4.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_5.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_6.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_7.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_8.pb.Po
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4.pb.Po
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4_1.pb.Po
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4_2.pb.Po
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4_3.pb.Po
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4.pb.Po
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4_1.pb.Po
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4_2.pb.Po
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4_3.pb.Po
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/libbenchmark_messages_la-benchmark_message4.pb.Plo
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/libbenchmark_messages_la-benchmark_message4_1.pb.Plo
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/libbenchmark_messages_la-benchmark_message4_2.pb.Plo
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/libbenchmark_messages_la-benchmark_message4_3.pb.Plo
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4.pb.Po
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4_1.pb.Po
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4_2.pb.Po
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4_3.pb.Po
	-rm -f gogo/cpp_no_group/$(DEPDIR)/cpp_no_group_benchmark-benchmarks.pb.Po
	-rm -f gogo/cpp_no_group/$(DEPDIR)/cpp_no_group_benchmark-cpp_benchmark.Po
	-rm -f gogo/cpp_no_group/datasets/google_message1/proto2/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message1_proto2.pb.Po
	-rm -f gogo/cpp_no_group/datasets/google_message1/proto3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message1_proto3.pb.Po
	-rm -f gogo/cpp_no_group/datasets/google_message2/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message2.pb.Po
	-rm -f gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3.pb.Po
	-rm -f gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_1.pb.Po
	-rm -f gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_2.pb.Po
	-rm -f gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_3.pb.Po
	-rm -f gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_4.pb.Po
	-rm -f gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_5.pb.Po
	-rm -f gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_6.pb.Po
	-rm -f gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_7.pb.Po
	-rm -f gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_8.pb.Po
	-rm -f gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4.pb.Po
	-rm -f gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4_1.pb.Po
	-rm -f gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4_2.pb.Po
	-rm -f gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4_3.pb.Po
	-rm -f python/$(DEPDIR)/libbenchmark_messages_la-python_benchmark_messages.Plo
	-rm -f util/$(DEPDIR)/gogo_data_scrubber-gogo_data_scrubber.Po
	-rm -f util/$(DEPDIR)/proto3_data_stripper-proto3_data_stripper.Po
	-rm -f util/$(DEPDIR)/protoc_gen_gogoproto-protoc-gen-gogoproto.Po
	-rm -f util/$(DEPDIR)/protoc_gen_proto2_to_proto3-protoc-gen-proto2_to_proto3.Po
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am:

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am: install-binPROGRAMS install-libLTLIBRARIES

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
		-rm -f cpp/$(DEPDIR)/benchmark-benchmarks.pb.Po
	-rm -f cpp/$(DEPDIR)/benchmark-cpp_benchmark.Po
	-rm -f cpp/$(DEPDIR)/gogo_data_scrubber-benchmarks.pb.Po
	-rm -f cpp/$(DEPDIR)/libbenchmark_messages_la-benchmarks.pb.Plo
	-rm -f cpp/$(DEPDIR)/proto3_data_stripper-benchmarks.pb.Po
	-rm -f cpp/datasets/google_message1/proto2/$(DEPDIR)/benchmark-benchmark_message1_proto2.pb.Po
	-rm -f cpp/datasets/google_message1/proto2/$(DEPDIR)/gogo_data_scrubber-benchmark_message1_proto2.pb.Po
	-rm -f cpp/datasets/google_message1/proto2/$(DEPDIR)/libbenchmark_messages_la-benchmark_message1_proto2.pb.Plo
	-rm -f cpp/datasets/google_message1/proto2/$(DEPDIR)/proto3_data_stripper-benchmark_message1_proto2.pb.Po
	-rm -f cpp/datasets/google_message1/proto3/$(DEPDIR)/benchmark-benchmark_message1_proto3.pb.Po
	-rm -f cpp/datasets/google_message1/proto3/$(DEPDIR)/gogo_data_scrubber-benchmark_message1_proto3.pb.Po
	-rm -f cpp/datasets/google_message1/proto3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message1_proto3.pb.Plo
	-rm -f cpp/datasets/google_message1/proto3/$(DEPDIR)/proto3_data_stripper-benchmark_message1_proto3.pb.Po
	-rm -f cpp/datasets/google_message2/$(DEPDIR)/benchmark-benchmark_message2.pb.Po
	-rm -f cpp/datasets/google_message2/$(DEPDIR)/gogo_data_scrubber-benchmark_message2.pb.Po
	-rm -f cpp/datasets/google_message2/$(DEPDIR)/libbenchmark_messages_la-benchmark_message2.pb.Plo
	-rm -f cpp/datasets/google_message2/$(DEPDIR)/proto3_data_stripper-benchmark_message2.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_1.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_2.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_3.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_4.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_5.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_6.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_7.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/benchmark-benchmark_message3_8.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_1.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_2.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_3.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_4.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_5.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_6.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_7.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/gogo_data_scrubber-benchmark_message3_8.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3.pb.Plo
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_1.pb.Plo
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_2.pb.Plo
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_3.pb.Plo
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_4.pb.Plo
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_5.pb.Plo
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_6.pb.Plo
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_7.pb.Plo
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/libbenchmark_messages_la-benchmark_message3_8.pb.Plo
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_1.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_2.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_3.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_4.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_5.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_6.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_7.pb.Po
	-rm -f cpp/datasets/google_message3/$(DEPDIR)/proto3_data_stripper-benchmark_message3_8.pb.Po
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4.pb.Po
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4_1.pb.Po
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4_2.pb.Po
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/benchmark-benchmark_message4_3.pb.Po
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4.pb.Po
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4_1.pb.Po
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4_2.pb.Po
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/gogo_data_scrubber-benchmark_message4_3.pb.Po
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/libbenchmark_messages_la-benchmark_message4.pb.Plo
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/libbenchmark_messages_la-benchmark_message4_1.pb.Plo
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/libbenchmark_messages_la-benchmark_message4_2.pb.Plo
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/libbenchmark_messages_la-benchmark_message4_3.pb.Plo
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4.pb.Po
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4_1.pb.Po
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4_2.pb.Po
	-rm -f cpp/datasets/google_message4/$(DEPDIR)/proto3_data_stripper-benchmark_message4_3.pb.Po
	-rm -f gogo/cpp_no_group/$(DEPDIR)/cpp_no_group_benchmark-benchmarks.pb.Po
	-rm -f gogo/cpp_no_group/$(DEPDIR)/cpp_no_group_benchmark-cpp_benchmark.Po
	-rm -f gogo/cpp_no_group/datasets/google_message1/proto2/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message1_proto2.pb.Po
	-rm -f gogo/cpp_no_group/datasets/google_message1/proto3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message1_proto3.pb.Po
	-rm -f gogo/cpp_no_group/datasets/google_message2/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message2.pb.Po
	-rm -f gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3.pb.Po
	-rm -f gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_1.pb.Po
	-rm -f gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_2.pb.Po
	-rm -f gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_3.pb.Po
	-rm -f gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_4.pb.Po
	-rm -f gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_5.pb.Po
	-rm -f gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_6.pb.Po
	-rm -f gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_7.pb.Po
	-rm -f gogo/cpp_no_group/datasets/google_message3/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message3_8.pb.Po
	-rm -f gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4.pb.Po
	-rm -f gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4_1.pb.Po
	-rm -f gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4_2.pb.Po
	-rm -f gogo/cpp_no_group/datasets/google_message4/$(DEPDIR)/cpp_no_group_benchmark-benchmark_message4_3.pb.Po
	-rm -f python/$(DEPDIR)/libbenchmark_messages_la-python_benchmark_messages.Plo
	-rm -f util/$(DEPDIR)/gogo_data_scrubber-gogo_data_scrubber.Po
	-rm -f util/$(DEPDIR)/proto3_data_stripper-proto3_data_stripper.Po
	-rm -f util/$(DEPDIR)/protoc_gen_gogoproto-protoc-gen-gogoproto.Po
	-rm -f util/$(DEPDIR)/protoc_gen_proto2_to_proto3-protoc-gen-proto2_to_proto3.Po
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-binPROGRAMS uninstall-libLTLIBRARIES

.MAKE: install-am install-strip

.PHONY: CTAGS GTAGS TAGS all all-am am--depfiles check check-am clean \
	clean-binPROGRAMS clean-generic clean-libLTLIBRARIES \
	clean-libtool clean-local cscopelist-am ctags ctags-am \
	distclean distclean-compile distclean-generic \
	distclean-libtool distclean-tags distdir dvi dvi-am html \
	html-am info info-am install install-am install-binPROGRAMS \
	install-data install-data-am install-dvi install-dvi-am \
	install-exec install-exec-am install-html install-html-am \
	install-info install-info-am install-libLTLIBRARIES \
	install-man install-pdf install-pdf-am install-ps \
	install-ps-am install-strip installcheck installcheck-am \
	installdirs maintainer-clean maintainer-clean-generic \
	mostlyclean mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool pdf pdf-am ps ps-am tags tags-am uninstall \
	uninstall-am uninstall-binPROGRAMS uninstall-libLTLIBRARIES

.PRECIOUS: Makefile


make_tmp_dir:
	mkdir -p 'tmp/java/src/main/java'
	touch make_tmp_dir

# We have to cd to $(srcdir) before executing protoc because $(protoc_inputs) is
# relative to srcdir, which may not be the same as the current directory when
# building out-of-tree.
protoc_middleman: make_tmp_dir $(top_srcdir)/src/protoc$(EXEEXT) $(benchmarks_protoc_inputs) $(well_known_type_protoc_inputs) $(benchmarks_protoc_inputs_benchmark_wrapper)
	oldpwd=`pwd` && ( cd $(srcdir) && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$(top_srcdir)/src --cpp_out=$$oldpwd/cpp --java_out=$$oldpwd/tmp/java/src/main/java --python_out=$$oldpwd/tmp $(benchmarks_protoc_inputs) $(benchmarks_protoc_inputs_benchmark_wrapper) )
	touch protoc_middleman

protoc_middleman2:  make_tmp_dir $(top_srcdir)/src/protoc$(EXEEXT) $(benchmarks_protoc_inputs_proto2) $(well_known_type_protoc_inputs)
	oldpwd=`pwd` && ( cd $(srcdir) && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$(top_srcdir)/src --cpp_out=$$oldpwd/cpp --java_out=$$oldpwd/tmp/java/src/main/java --python_out=$$oldpwd/tmp $(benchmarks_protoc_inputs_proto2) )
	touch protoc_middleman2

$(benchmarks_protoc_outputs): protoc_middleman
$(benchmarks_protoc_outputs_header): protoc_middleman
$(benchmarks_protoc_outputs_proto2): protoc_middleman2
$(benchmarks_protoc_outputs_proto2_header): protoc_middleman2

initialize_submodule:
	oldpwd=`pwd`
	cd $(top_srcdir) && git submodule update --init -r third_party/benchmark && \
		cd third_party/benchmark && cmake -DCMAKE_BUILD_TYPE=Release && make
	cd $$oldpwd
	touch initialize_submodule

$(top_srcdir)/third_party/benchmark/src/libbenchmark.a: initialize_submodule
# Explicit deps because BUILT_SOURCES are only done before a "make all/check"
# so a direct "make test_cpp" could fail if parallel enough.
# See: https://www.gnu.org/software/automake/manual/html_node/Built-Sources-Example.html#Recording-Dependencies-manually
cpp/cpp_benchmark-cpp_benchmark.$(OBJEXT): $(benchmarks_protoc_outputs) $(benchmarks_protoc_outputs_proto2) $(benchmarks_protoc_outputs_header) $(benchmarks_protoc_outputs_proto2_header) $(top_srcdir)/src/libprotobuf.la $(top_srcdir)/third_party/benchmark/src/libbenchmark.a
cpp/benchmark-cpp_benchmark.$(OBJEXT): $(benchmarks_protoc_outputs) $(benchmarks_protoc_outputs_proto2) $(benchmarks_protoc_outputs_header) $(benchmarks_protoc_outputs_proto2_header) $(top_srcdir)/src/libprotobuf.la $(top_srcdir)/third_party/benchmark/src/libbenchmark.a

cpp: protoc_middleman protoc_middleman2 cpp-benchmark initialize_submodule
	./cpp-benchmark $(all_data)

javac_middleman: $(java_benchmark_testing_files) protoc_middleman protoc_middleman2
	cp -r $(srcdir)/java tmp
	mkdir -p tmp/java/lib
	cp $(top_srcdir)/java/core/target/*.jar tmp/java/lib/protobuf-java.jar
	cd tmp/java && mvn clean compile assembly:single -Dprotobuf.version=$(PACKAGE_VERSION) && cd ../..
	@touch javac_middleman

java-benchmark: javac_middleman
	@echo "Writing shortcut script java-benchmark..."
	@echo '#! /bin/bash' > java-benchmark
	@echo 'all_data=""' >> java-benchmark
	@echo 'conf=()' >> java-benchmark
	@echo 'data_files=""' >> java-benchmark
	@echo 'for arg in $$@; do if [[ $${arg:0:1} == "-" ]]; then conf+=($$arg); else data_files+="$$arg,"; fi; done' >> java-benchmark
	@echo 'java -cp '\"tmp/java/target/*:$(top_srcdir)/java/core/target/*:$(top_srcdir)/java/util/target/*\"" \\" >>java-benchmark
	@echo '   com.google.caliper.runner.CaliperMain com.google.protobuf.ProtoCaliperBenchmark -i runtime '"\\"  >> java-benchmark
	@echo '   -b serializeToByteArray,serializeToMemoryStream,deserializeFromByteArray,deserializeFromMemoryStream '"\\" >> java-benchmark
	@echo '   -DdataFile=$${data_files:0:-1} $${conf[*]}' >> java-benchmark
	@chmod +x java-benchmark

java: protoc_middleman protoc_middleman2 java-benchmark
	./java-benchmark $(all_data)

############# JAVA RULES END ##############

############# PYTHON RULES ##############

python_add_init: protoc_middleman protoc_middleman2
	all_file=`find tmp -type f -regex '.*\.py'` &&                   \
	for file in $${all_file[@]}; do                                  \
		path="$${file%/*}";                                            \
		while true; do                                                 \
			touch "$$path/__init__.py" && chmod +x "$$path/__init__.py"; \
			if [[ $$path != *"/"* ]]; then break; fi;                    \
			path=$${path%/*};                                            \
		done                                                           \
	done
libbenchmark_messages_la-python_benchmark_messages.$(OBJEXT): $(benchmarks_protoc_outputs_header) $(benchmarks_protoc_outputs_proto2_header) $(benchmarks_protoc_outputs) $(benchmarks_protoc_outputs_proto2)

python-pure-python-benchmark: python_add_init
	@echo "Writing shortcut script python-pure-python-benchmark..."
	@echo '#! /bin/bash' > python-pure-python-benchmark
	@echo export LD_LIBRARY_PATH=$(top_srcdir)/src/.libs >> python-pure-python-benchmark
	@echo export DYLD_LIBRARY_PATH=$(top_srcdir)/src/.libs >> python-pure-python-benchmark
	@echo export PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=\'python\' >> python-pure-python-benchmark
	@echo cp $(srcdir)/python/py_benchmark.py tmp >> python-pure-python-benchmark
	@echo python3 tmp/py_benchmark.py '$$@' >> python-pure-python-benchmark
	@chmod +x python-pure-python-benchmark

python-cpp-reflection-benchmark: python_add_init
	@echo "Writing shortcut script python-cpp-reflection-benchmark..."
	@echo '#! /bin/bash' > python-cpp-reflection-benchmark
	@echo export LD_LIBRARY_PATH=$(top_srcdir)/src/.libs >> python-cpp-reflection-benchmark
	@echo export DYLD_LIBRARY_PATH=$(top_srcdir)/src/.libs >> python-cpp-reflection-benchmark
	@echo export PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=\'cpp\' >> python-cpp-reflection-benchmark
	@echo cp $(srcdir)/python/py_benchmark.py tmp >> python-cpp-reflection-benchmark
	@echo python3 tmp/py_benchmark.py '$$@' >> python-cpp-reflection-benchmark
	@chmod +x python-cpp-reflection-benchmark

python-cpp-generated-code-benchmark: python_add_init libbenchmark_messages.la
	@echo "Writing shortcut script python-cpp-generated-code-benchmark..."
	@echo '#! /bin/bash' > python-cpp-generated-code-benchmark
	@echo export LD_LIBRARY_PATH=$(top_srcdir)/src/.libs >> python-cpp-generated-code-benchmark
	@echo export DYLD_LIBRARY_PATH=$(top_srcdir)/src/.libs >> python-cpp-generated-code-benchmark
	@echo export PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=\'cpp\' >> python-cpp-generated-code-benchmark
	@echo cp $(srcdir)/python/py_benchmark.py tmp >> python-cpp-generated-code-benchmark
	@echo python3 tmp/py_benchmark.py --cpp_generated '$$@' >> python-cpp-generated-code-benchmark
	@chmod +x python-cpp-generated-code-benchmark

python-pure-python: python-pure-python-benchmark
	./python-pure-python-benchmark $(all_data)

python-cpp-reflection: python-cpp-reflection-benchmark
	./python-cpp-reflection-benchmark $(all_data)

python-cpp-generated-code: python-cpp-generated-code-benchmark
	./python-cpp-generated-code-benchmark $(all_data)

go_protoc_middleman: make_tmp_dir $(top_srcdir)/src/protoc$(EXEEXT) $(benchmarks_protoc_inputs) $(well_known_type_protoc_inputs) $(benchmarks_protoc_inputs_proto2_message1) $(benchmarks_protoc_inputs_proto2_message2) $(benchmarks_protoc_inputs_proto2_message3) $(benchmarks_protoc_inputs_proto2_message4) $(well_known_type_protoc_inputs)
	oldpwd=`pwd` && ( cd $(srcdir) && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$(top_srcdir)/src --go_out=$$oldpwd/tmp $(benchmarks_protoc_inputs) )
	oldpwd=`pwd` && ( cd $(srcdir) && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$(top_srcdir)/src --go_out=$$oldpwd/tmp $(benchmarks_protoc_inputs_benchmark_wrapper) )
	oldpwd=`pwd` && ( cd $(srcdir) && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$(top_srcdir)/src --go_out=$$oldpwd/tmp $(benchmarks_protoc_inputs_proto2_message1) )
	oldpwd=`pwd` && ( cd $(srcdir) && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$(top_srcdir)/src --go_out=$$oldpwd/tmp $(benchmarks_protoc_inputs_proto2_message2) )
	oldpwd=`pwd` && ( cd $(srcdir) && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$(top_srcdir)/src --go_out=$$oldpwd/tmp $(benchmarks_protoc_inputs_proto2_message3) )
	oldpwd=`pwd` && ( cd $(srcdir) && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$(top_srcdir)/src --go_out=$$oldpwd/tmp $(benchmarks_protoc_inputs_proto2_message4) )
	touch go_protoc_middleman

go-benchmark: go_protoc_middleman
	@echo "Writing shortcut script go-benchmark..."
	@echo '#! /bin/bash' > go-benchmark
	@echo 'cd $(srcdir)/go' >> go-benchmark
	@echo 'all_data=""' >> go-benchmark
	@echo 'conf=()' >> go-benchmark
	@echo 'data_files=()' >> go-benchmark
	@echo 'for arg in $$@; do if [[ $${arg:0:1} == "-" ]]; then conf+=($$arg); else data_files+=("$$arg"); fi; done' >> go-benchmark
	@echo 'go test -bench=. $${conf[*]} -- $${data_files[*]}' >> go-benchmark
	@echo 'cd ..' >> go-benchmark
	@chmod +x go-benchmark

go: go_protoc_middleman go-benchmark
	./go-benchmark $(all_data)

$(cpp_no_group_benchmarks_protoc_outputs): cpp_no_group_protoc_middleman
$(cpp_no_group_benchmarks_protoc_outputs_header): cpp_no_group_protoc_middleman
$(cpp_no_group_benchmarks_protoc_outputs_proto2): cpp_no_group_protoc_middleman
$(cpp_no_group_benchmarks_protoc_outputs_proto2_header): cpp_no_group_protoc_middleman

generate_cpp_no_group_benchmark_code:
	cp $(srcdir)/cpp/cpp_benchmark.cc gogo/cpp_no_group/cpp_benchmark.cc
	sed -i -e "s/\#include \"datasets/\#include \"gogo\/cpp_no_group\/datasets/g" gogo/cpp_no_group/cpp_benchmark.cc
	sed -i -e "s/\#include \"benchmarks.pb.h/\#include \"gogo\/cpp_no_group\/benchmarks.pb.h/g" gogo/cpp_no_group/cpp_benchmark.cc
	touch generate_cpp_no_group_benchmark_code
# Explicit deps because BUILT_SOURCES are only done before a "make all/check"
# so a direct "make test_cpp" could fail if parallel enough.
# See: https://www.gnu.org/software/automake/manual/html_node/Built-Sources-Example.html#Recording-Dependencies-manually
gogo/cpp_no_group/cpp_no_group_benchmark-cpp_benchmark.$(OBJEXT): $(cpp_no_group_benchmarks_protoc_outputs) $(cpp_no_group_benchmarks_protoc_outputs_proto2) $(cpp_no_group_benchmarks_protoc_outputs_header) \
	$(cpp_no_group_benchmarks_protoc_outputs_proto2_header) $(top_srcdir)/third_party/benchmark/src/libbenchmark.a generate_cpp_no_group_benchmark_code
gogo/cpp_no_group/cpp_benchmark.cc: generate_cpp_no_group_benchmark_code

cpp_no_group: cpp_no_group_protoc_middleman generate_gogo_data cpp-no-group-benchmark
	./cpp-no-group-benchmark $(gogo_data)

gogo_proto_middleman: protoc-gen-gogoproto
	mkdir -p "tmp/gogo_proto"
	oldpwd=`pwd` && ( cd $(srcdir) && $$oldpwd/../src/protoc$(EXEEXT) -I$(srcdir) -I$(top_srcdir) --plugin=protoc-gen-gogoproto --gogoproto_out=$$oldpwd/tmp/gogo_proto $(benchmarks_protoc_inputs) $(benchmarks_protoc_inputs_benchmark_wrapper) $(benchmarks_protoc_inputs_proto2) )
	touch gogo_proto_middleman

generate_gogo_data: protoc_middleman protoc_middleman2 gogo-data-scrubber
	mkdir -p `dirname $(gogo_data)`
	./gogo-data-scrubber $(all_data) $(gogo_data)
	touch generate_gogo_data

make_tmp_dir_gogo:
	mkdir -p tmp/go_no_group/benchmark_code
	mkdir -p tmp/gogofast/benchmark_code
	mkdir -p tmp/gogofaster/benchmark_code
	mkdir -p tmp/gogoslick/benchmark_code
	touch make_tmp_dir_gogo

go_no_group_protoc_middleman: make_tmp_dir_gogo $(top_srcdir)/src/protoc$(EXEEXT) gogo_proto_middleman $(well_known_type_protoc_inputs)
	oldpwd=`pwd` && ( cd $(srcdir)/tmp/gogo_proto && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$$oldpwd/$(top_srcdir)/src --go_out=$$oldpwd/tmp/go_no_group $(benchmarks_protoc_inputs) )
	oldpwd=`pwd` && ( cd $(srcdir)/tmp/gogo_proto && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$$oldpwd/$(top_srcdir)/src --go_out=$$oldpwd/tmp/go_no_group $(benchmarks_protoc_inputs_benchmark_wrapper) )
	oldpwd=`pwd` && ( cd $(srcdir)/tmp/gogo_proto && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$$oldpwd/$(top_srcdir)/src --go_out=$$oldpwd/tmp/go_no_group $(benchmarks_protoc_inputs_proto2_message1) )
	oldpwd=`pwd` && ( cd $(srcdir)/tmp/gogo_proto && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$$oldpwd/$(top_srcdir)/src --go_out=$$oldpwd/tmp/go_no_group $(benchmarks_protoc_inputs_proto2_message2) )
	oldpwd=`pwd` && ( cd $(srcdir)/tmp/gogo_proto && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$$oldpwd/$(top_srcdir)/src --go_out=$$oldpwd/tmp/go_no_group $(benchmarks_protoc_inputs_proto2_message3) )
	oldpwd=`pwd` && ( cd $(srcdir)/tmp/gogo_proto && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$$oldpwd/$(top_srcdir)/src --go_out=$$oldpwd/tmp/go_no_group $(benchmarks_protoc_inputs_proto2_message4) )
	touch go_no_group_protoc_middleman

cpp_no_group_protoc_middleman: make_tmp_dir_gogo $(top_srcdir)/src/protoc$(EXEEXT) gogo_proto_middleman $(well_known_type_protoc_inputs)
	oldpwd=`pwd` && ( cd $(srcdir)/tmp/gogo_proto && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$$oldpwd/$(top_srcdir)/src --cpp_out=$$oldpwd/gogo/cpp_no_group $(benchmarks_protoc_inputs) )
	oldpwd=`pwd` && ( cd $(srcdir)/tmp/gogo_proto && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$$oldpwd/$(top_srcdir)/src --cpp_out=$$oldpwd/gogo/cpp_no_group $(benchmarks_protoc_inputs_benchmark_wrapper) )
	oldpwd=`pwd` && ( cd $(srcdir)/tmp/gogo_proto && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$$oldpwd/$(top_srcdir)/src --cpp_out=$$oldpwd/gogo/cpp_no_group $(benchmarks_protoc_inputs_proto2_message1) )
	oldpwd=`pwd` && ( cd $(srcdir)/tmp/gogo_proto && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$$oldpwd/$(top_srcdir)/src --cpp_out=$$oldpwd/gogo/cpp_no_group $(benchmarks_protoc_inputs_proto2_message2) )
	oldpwd=`pwd` && ( cd $(srcdir)/tmp/gogo_proto && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$$oldpwd/$(top_srcdir)/src --cpp_out=$$oldpwd/gogo/cpp_no_group $(benchmarks_protoc_inputs_proto2_message3) )
	oldpwd=`pwd` && ( cd $(srcdir)/tmp/gogo_proto && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$$oldpwd/$(top_srcdir)/src --cpp_out=$$oldpwd/gogo/cpp_no_group $(benchmarks_protoc_inputs_proto2_message4) )
	touch cpp_no_group_protoc_middleman

gogofast_protoc_middleman: make_tmp_dir_gogo $(top_srcdir)/src/protoc$(EXEEXT) gogo_proto_middleman $(well_known_type_protoc_inputs)
	oldpwd=`pwd` && ( cd $(srcdir)/tmp/gogo_proto && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$$oldpwd/$(top_srcdir)/src --gogofast_out=$$oldpwd/tmp/gogofast $(benchmarks_protoc_inputs) )
	oldpwd=`pwd` && ( cd $(srcdir)/tmp/gogo_proto && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$$oldpwd/$(top_srcdir)/src --gogofast_out=$$oldpwd/tmp/gogofast $(benchmarks_protoc_inputs_benchmark_wrapper) )
	oldpwd=`pwd` && ( cd $(srcdir)/tmp/gogo_proto && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$$oldpwd/$(top_srcdir)/src --gogofast_out=$$oldpwd/tmp/gogofast $(benchmarks_protoc_inputs_proto2_message1) )
	oldpwd=`pwd` && ( cd $(srcdir)/tmp/gogo_proto && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$$oldpwd/$(top_srcdir)/src --gogofast_out=$$oldpwd/tmp/gogofast $(benchmarks_protoc_inputs_proto2_message2) )
	oldpwd=`pwd` && ( cd $(srcdir)/tmp/gogo_proto && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$$oldpwd/$(top_srcdir)/src --gogofast_out=$$oldpwd/tmp/gogofast $(benchmarks_protoc_inputs_proto2_message3) )
	oldpwd=`pwd` && ( cd $(srcdir)/tmp/gogo_proto && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$$oldpwd/$(top_srcdir)/src --gogofast_out=$$oldpwd/tmp/gogofast $(benchmarks_protoc_inputs_proto2_message4) )
	touch gogofast_protoc_middleman

gogofaster_protoc_middleman: make_tmp_dir_gogo $(top_srcdir)/src/protoc$(EXEEXT) gogo_proto_middleman $(well_known_type_protoc_inputs)
	oldpwd=`pwd` && ( cd $(srcdir)/tmp/gogo_proto && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$$oldpwd/$(top_srcdir)/src --gogofaster_out=$$oldpwd/tmp/gogofaster $(benchmarks_protoc_inputs) )
	oldpwd=`pwd` && ( cd $(srcdir)/tmp/gogo_proto && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$$oldpwd/$(top_srcdir)/src --gogofaster_out=$$oldpwd/tmp/gogofaster $(benchmarks_protoc_inputs_benchmark_wrapper) )
	oldpwd=`pwd` && ( cd $(srcdir)/tmp/gogo_proto && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$$oldpwd/$(top_srcdir)/src --gogofaster_out=$$oldpwd/tmp/gogofaster $(benchmarks_protoc_inputs_proto2_message1) )
	oldpwd=`pwd` && ( cd $(srcdir)/tmp/gogo_proto && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$$oldpwd/$(top_srcdir)/src --gogofaster_out=$$oldpwd/tmp/gogofaster $(benchmarks_protoc_inputs_proto2_message2) )
	oldpwd=`pwd` && ( cd $(srcdir)/tmp/gogo_proto && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$$oldpwd/$(top_srcdir)/src --gogofaster_out=$$oldpwd/tmp/gogofaster $(benchmarks_protoc_inputs_proto2_message3) )
	oldpwd=`pwd` && ( cd $(srcdir)/tmp/gogo_proto && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$$oldpwd/$(top_srcdir)/src --gogofaster_out=$$oldpwd/tmp/gogofaster $(benchmarks_protoc_inputs_proto2_message4) )
	touch gogofaster_protoc_middleman

gogoslick_protoc_middleman: make_tmp_dir_gogo $(top_srcdir)/src/protoc$(EXEEXT) gogo_proto_middleman $(well_known_type_protoc_inputs)
	oldpwd=`pwd` && ( cd $(srcdir)/tmp/gogo_proto && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$$oldpwd/$(top_srcdir)/src --gogoslick_out=$$oldpwd/tmp/gogoslick $(benchmarks_protoc_inputs) )
	oldpwd=`pwd` && ( cd $(srcdir)/tmp/gogo_proto && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$$oldpwd/$(top_srcdir)/src --gogoslick_out=$$oldpwd/tmp/gogoslick $(benchmarks_protoc_inputs_benchmark_wrapper) )
	oldpwd=`pwd` && ( cd $(srcdir)/tmp/gogo_proto && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$$oldpwd/$(top_srcdir)/src --gogoslick_out=$$oldpwd/tmp/gogoslick $(benchmarks_protoc_inputs_proto2_message1) )
	oldpwd=`pwd` && ( cd $(srcdir)/tmp/gogo_proto && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$$oldpwd/$(top_srcdir)/src --gogoslick_out=$$oldpwd/tmp/gogoslick $(benchmarks_protoc_inputs_proto2_message2) )
	oldpwd=`pwd` && ( cd $(srcdir)/tmp/gogo_proto && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$$oldpwd/$(top_srcdir)/src --gogoslick_out=$$oldpwd/tmp/gogoslick $(benchmarks_protoc_inputs_proto2_message3) )
	oldpwd=`pwd` && ( cd $(srcdir)/tmp/gogo_proto && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$$oldpwd/$(top_srcdir)/src --gogoslick_out=$$oldpwd/tmp/gogoslick $(benchmarks_protoc_inputs_proto2_message4) )
	touch gogoslick_protoc_middleman

generate-gogo-benchmark-code:
	@echo '#! /bin/bash' > generate-gogo-benchmark-code
	@echo 'cp $(srcdir)/go/go_benchmark_test.go tmp/$$1/benchmark_code/$$1_benchmark1_test.go' >> generate-gogo-benchmark-code
	@echo 'sed -i -e "s/\.\.\/tmp/../g" tmp/$$1/benchmark_code/$$1_benchmark1_test.go' >> generate-gogo-benchmark-code
	@echo 'sed -i -e "s/b\.Run(\"\(.*\)\"/b.Run(\"\1\_$$1\"/g" tmp/$$1/benchmark_code/$$1_benchmark1_test.go' >> generate-gogo-benchmark-code
	@echo 'if [[ $$2 == 1 ]]; then sed -i -e "s/github\.com\/golang/github.com\/gogo/g" tmp/$$1/benchmark_code/$$1_benchmark1_test.go; fi ' >> generate-gogo-benchmark-code
	@chmod +x generate-gogo-benchmark-code

generate_all_gogo_benchmark_code: generate-gogo-benchmark-code make_tmp_dir_gogo
	./generate-gogo-benchmark-code go_no_group 0
	./generate-gogo-benchmark-code gogofast 1
	./generate-gogo-benchmark-code gogofaster 1
	./generate-gogo-benchmark-code gogoslick 1

gogo-benchmark:
	@echo "Writing shortcut script gogo-benchmark..."
	@echo '#! /bin/bash' > gogo-benchmark
	@echo 'cd tmp/$$1/benchmark_code' >> gogo-benchmark
	@echo 'shift' >> gogo-benchmark
	@echo 'all_data=""' >> gogo-benchmark
	@echo 'for data_file in $$@; do all_data="$$all_data ../../../$$data_file"; done' >> gogo-benchmark
	@echo 'go test -bench=. -- $$all_data' >> gogo-benchmark
	@echo 'cd ../..' >> gogo-benchmark
	@chmod +x gogo-benchmark

go_no_group: go_no_group_protoc_middleman generate_gogo_data generate_all_gogo_benchmark_code gogo-benchmark
	./gogo-benchmark go_no_group $(gogo_data)

gogofast: gogofast_protoc_middleman generate_gogo_data gogo-benchmark generate_all_gogo_benchmark_code
	./gogo-benchmark gogofast $(gogo_data)

gogofaster: gogofaster_protoc_middleman  generate_gogo_data gogo-benchmark generate_all_gogo_benchmark_code
	./gogo-benchmark gogofaster $(gogo_data)

gogoslick: gogoslick_protoc_middleman  generate_gogo_data gogo-benchmark generate_all_gogo_benchmark_code
	./gogo-benchmark gogoslick $(gogo_data)
util/gogo_data_scrubber-gogo_data_scrubber.$(OBJEXT): $(benchmarks_protoc_outputs) $(benchmarks_protoc_outputs_proto2) $(benchmarks_protoc_outputs_header) $(benchmarks_protoc_outputs_proto2_header)
util/proto3_data_stripper-proto3_data_stripper.$(OBJEXT): $(benchmarks_protoc_outputs) $(benchmarks_protoc_outputs_proto2) $(benchmarks_protoc_outputs_header) $(benchmarks_protoc_outputs_proto2_header)

############ UTIL RULES END ############

############ PROTO3 PREPARATION BEGIN #############

proto3_proto_middleman: protoc-gen-proto2_to_proto3
	mkdir -p "tmp/proto3_proto"
	oldpwd=`pwd` && ( cd $(srcdir) && $$oldpwd/../src/protoc$(EXEEXT) -I$(srcdir) -I$(top_srcdir) --plugin=protoc-gen-proto2_to_proto3 --proto2_to_proto3_out=$$oldpwd/tmp/proto3_proto $(benchmarks_protoc_inputs) $(benchmarks_protoc_inputs_benchmark_wrapper) $(benchmarks_protoc_inputs_proto2) )
	touch proto3_proto_middleman

generate_proto3_data: protoc_middleman protoc_middleman2 proto3-data-stripper
	mkdir -p `dirname $(proto3_data)`
	./proto3-data-stripper $(all_data) $(proto3_data)
	touch generate_proto3_data

############ PROTO3 PREPARATION END #############

############ PHP RULES BEGIN #################

proto3_middleman_php: proto3_proto_middleman
	mkdir -p "tmp/php"
	oldpwd=`pwd` && ( cd tmp/proto3_proto && $$oldpwd/../src/protoc$(EXEEXT) -I$(srcdir) -I$(top_srcdir) --php_out=$$oldpwd/tmp/php $(benchmarks_protoc_inputs) $(benchmarks_protoc_inputs_benchmark_wrapper) $(benchmarks_protoc_inputs_proto2) )
	touch proto3_middleman_php

php-benchmark: proto3_middleman_php generate_proto3_data
	mkdir -p "tmp/php/Google/Protobuf/Benchmark" && cp php/PhpBenchmark.php "tmp/php/Google/Protobuf/Benchmark"
	cp php/autoload.php "tmp/php"
	@echo "Writing shortcut script php-benchmark..."
	@echo '#! /bin/bash' > php-benchmark
	@echo 'export PROTOBUF_PHP_SRCDIR="$$(cd $(top_srcdir) && pwd)/php/src"' >> php-benchmark
	@echo 'cd tmp/php' >> php-benchmark
	@echo 'export CURRENT_DIR=$$(pwd)' >> php-benchmark
	@echo 'php -d auto_prepend_file="autoload.php" -d include_path="$$(pwd)" Google/Protobuf/Benchmark/PhpBenchmark.php $$@' >> php-benchmark
	@echo 'cd ../..' >> php-benchmark
	@chmod +x php-benchmark

php: php-benchmark proto3_middleman_php
	./php-benchmark --behavior_prefix="php" $(proto3_data)

php_c_extension:
	cd $(top_srcdir)/php/ext/google/protobuf && phpize && ./configure CFLAGS='-O3' && make -j8

php-c-benchmark: proto3_middleman_php generate_proto3_data php_c_extension php_c_extension
	mkdir -p "tmp/php/Google/Protobuf/Benchmark" && cp php/PhpBenchmark.php "tmp/php/Google/Protobuf/Benchmark"
	cp php/autoload.php "tmp/php"
	@echo "Writing shortcut script php-c-benchmark..."
	@echo '#! /bin/bash' > php-c-benchmark
	@echo 'export PROTOBUF_PHP_SRCDIR="$$(cd $(top_srcdir) && pwd)/php/src"' >> php-c-benchmark
	@echo 'export PROTOBUF_PHP_EXTDIR="$$PROTOBUF_PHP_SRCDIR/../ext/google/protobuf/modules"' >> php-c-benchmark
	@echo 'cd tmp/php' >> php-c-benchmark
	@echo 'export CURRENT_DIR=$$(pwd)' >> php-c-benchmark
	@echo 'php -d auto_prepend_file="autoload.php" -d include_path="$$(pwd)" -d extension="$$PROTOBUF_PHP_EXTDIR/protobuf.so" Google/Protobuf/Benchmark/PhpBenchmark.php $$@' >> php-c-benchmark
	@echo 'cd ../..' >> php-c-benchmark
	@chmod +x php-c-benchmark

php_c: php-c-benchmark proto3_middleman_php
	./php-c-benchmark --behavior_prefix="php_c" $(proto3_data)

############ PHP RULES END #################

############ protobuf.js RULE BEGIN #############

pbjs_preparation:
	mkdir -p tmp/protobuf.js
	cd tmp/protobuf.js && git clone https://github.com/dcodeIO/protobuf.js.git && \
			cd protobuf.js && npm install && npm run build
	cd tmp/protobuf.js && npm install benchmark
	cp protobuf.js/* tmp/protobuf.js
	cp js/benchmark_suite.js tmp/protobuf.js
	touch pbjs_preparation

pbjs_middleman: pbjs_preparation
	export OLDDIR=$$(pwd) && cd tmp/protobuf.js && node generate_pbjs_files.js --target static-module --include_path=$$OLDDIR -o generated_bundle_code.js $(benchmarks_protoc_inputs) $(benchmarks_protoc_inputs_benchmark_wrapper) $(benchmarks_protoc_inputs_proto2)
	touch pbjs_middleman

pbjs-benchmark: pbjs_middleman
	@echo '#! /bin/bash' > pbjs-benchmark
	@echo 'cd tmp/protobuf.js' >> pbjs-benchmark
	@echo 'sed -i "s/protobufjs/.\/protobuf.js/g" generated_bundle_code.js' >> pbjs-benchmark
	@echo 'env NODE_PATH=".:./node_modules:$$NODE_PATH" node protobufjs_benchmark.js $$@' >> pbjs-benchmark
	@chmod +x pbjs-benchmark

pbjs: pbjs-benchmark
	./pbjs-benchmark $(all_data)

############ protobuf.js RULE END #############

############ JS RULE BEGIN #############

js_preparation:
	mkdir -p tmp/js
	oldpwd=$$(pwd) && cd $(top_srcdir)/js && npm install && npm test
	cd tmp/js && npm install benchmark
	cp js/* tmp/js
	touch js_preparation

js_middleman: js_preparation
	oldpwd=`pwd` && ( cd $(srcdir) && $$oldpwd/../src/protoc$(EXEEXT) -I. -I$(top_srcdir)/src --js_out=import_style=commonjs,binary:$$oldpwd/tmp/js $(benchmarks_protoc_inputs) $(benchmarks_protoc_inputs_benchmark_wrapper) $(benchmarks_protoc_inputs_proto2))
	touch js_middleman

js-benchmark: js_middleman
	@echo '#! /bin/bash' > js-benchmark
	@echo 'export TOP_JS_SRCDIR=$$(cd $(top_srcdir)/js && pwd)' >> js-benchmark
	@echo 'cd tmp/js' >> js-benchmark
	@echo 'env NODE_PATH="$$TOP_JS_SRCDIR:.:./node_modules:$$NODE_PATH" node --max-old-space-size=4096 js_benchmark.js $$@' >> js-benchmark
	@chmod +x js-benchmark

js: js-benchmark
	./js-benchmark $(all_data)

clean-local:
	-rm -rf tmp/*

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
