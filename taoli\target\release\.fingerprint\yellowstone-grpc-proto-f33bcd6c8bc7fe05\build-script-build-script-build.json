{"rustc": 3926191382657067107, "features": "[\"convert\", \"plugin\", \"tonic\", \"tonic-compression\"]", "declared_features": "[\"convert\", \"default\", \"plugin\", \"plugin-bench\", \"tonic\", \"tonic-compression\"]", "target": 5408242616063297496, "profile": 2501962466256191590, "path": 14772005206377215148, "deps": [[3565596177278269483, "protobuf_src", false, 9863572294375000085], [6095238760671403940, "tonic_build", false, 12946508225107821490], [13625485746686963219, "anyhow", false, 14615585878658685960]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\yellowstone-grpc-proto-f33bcd6c8bc7fe05\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}