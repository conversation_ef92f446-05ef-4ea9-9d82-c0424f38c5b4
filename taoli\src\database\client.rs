/// ClickHouse数据库客户端
use std::sync::OnceLock;
use log::{info, error};
use crate::database::models::{PumpCreateEvent, PumpTradeRecord, BonkTradeRecord, PumpCompleteEvent, BonkCreateEvent};

static DATABASE_CLIENT: OnceLock<DatabaseClient> = OnceLock::new();

pub struct DatabaseClient {
    clickhouse_url: String,
    username: String,
    password: String,
    database: String,
}

impl DatabaseClient {
    pub fn new(url: String, username: String, password: String, database: String) -> Self {
        Self {
            clickhouse_url: url,
            username,
            password,
            database,
        }
    }

    /// 初始化数据库和表结构
    pub async fn init_database_schema(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        info!("正在初始化数据库和表结构...");

        // 1. 创建数据库
        let create_db_sql = format!("CREATE DATABASE IF NOT EXISTS {}", self.database);
        if let Err(e) = self.execute_sql(&create_db_sql).await {
            error!("创建数据库失败: {}", e);
            // 继续执行，可能数据库已存在
        } else {
            info!("数据库 {} 创建成功或已存在", self.database);
        }

        // 2. 创建pump_create_events表
        let create_pump_create_table = format!(
            "CREATE TABLE IF NOT EXISTS {}.pump_create_events (
                signature String,
                timestamp String,
                name String,
                symbol String,
                uri String,
                mint String,
                bonding_curve String,
                user String,
                creator String,
                virtual_token_reserves UInt64,
                virtual_sol_reserves UInt64,
                real_token_reserves UInt64,
                token_total_supply UInt64,
                inserted_at DateTime DEFAULT now()
            ) ENGINE = MergeTree()
            ORDER BY (timestamp, mint)
            SETTINGS index_granularity = 8192",
            self.database
        );

        if let Err(e) = self.execute_sql(&create_pump_create_table).await {
            error!("创建pump_create_events表失败: {}", e);
        } else {
            info!("pump_create_events表创建成功或已存在");
        }









        info!("数据库和表结构初始化完成");
        Ok(())
    }

    /// 为特定代币创建交易记录表
    pub async fn create_token_trade_table(&self, mint_address: &str) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // 验证mint地址格式
        if mint_address.len() < 32 {
            return Err("无效的mint地址".into());
        }

        // 直接使用token地址作为表名
        let table_name = mint_address;

        let create_table_sql = format!(
            "CREATE TABLE IF NOT EXISTS {}.{} (
                id String DEFAULT generateUUIDv4(),
                sequence_id UInt64,
                signature String,
                timestamp String,
                trade_type String,
                trader_address String,
                token_amount UInt64,
                sol_amount Float64,
                price Float64,
                virtual_token_reserves UInt64,
                virtual_sol_reserves UInt64,
                real_token_reserves UInt64,
                real_sol_reserves UInt64,
                creator_address String,
                creator_vault_address String,
                is_complete Bool,
                inserted_at DateTime DEFAULT now()
            ) ENGINE = MergeTree()
            ORDER BY (sequence_id)
            SETTINGS index_granularity = 8192",
            self.database, table_name
        );

        if let Err(e) = self.execute_sql(&create_table_sql).await {
            error!("创建代币交易表{}失败: {}", table_name, e);
            return Err(e);
        }

        Ok(())
    }

    /// 插入代币交易记录到专属表（带序列号）
    pub async fn insert_token_trade_with_sequence(&self, mint_address: &str, trade_record: &PumpTradeRecord, sequence_id: u64) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // 首先确保该代币的交易表存在
        self.create_token_trade_table(mint_address).await?;

        // 直接使用token地址作为表名
        let table_name = mint_address;

        let sql = format!(
            "INSERT INTO {}.{} (sequence_id, signature, timestamp, trade_type, trader_address, token_amount, sol_amount, price, virtual_token_reserves, virtual_sol_reserves, real_token_reserves, real_sol_reserves, creator_address, creator_vault_address, is_complete) VALUES ({}, '{}', '{}', '{}', '{}', {}, {}, {}, {}, {}, {}, {}, '{}', '{}', {})",
            self.database,
            table_name,
            sequence_id,
            trade_record.signature,
            trade_record.timestamp.format("%Y-%m-%d %H:%M:%S%.3f"),
            trade_record.trade_type.replace("'", "''"),
            trade_record.signer_address,
            trade_record.token_amount,
            trade_record.sol_cost,
            trade_record.current_price,
            trade_record.virtual_token_reserves,
            trade_record.virtual_sol_reserves,
            trade_record.real_token_reserves,
            trade_record.real_sol_reserves,
            trade_record.creator_address,
            trade_record.creator_vault_address,
            trade_record.is_complete
        );

        self.execute_sql(&sql).await?;
        Ok(())
    }

    /// 插入Pump创建事件
    pub async fn insert_pump_create_event(&self, event: &PumpCreateEvent) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // 使用标准INSERT语法，每个字段明确指定类型
        let sql = format!(
            "INSERT INTO {}.pump_create_events (signature, timestamp, name, symbol, uri, mint, bonding_curve, user, creator, virtual_token_reserves, virtual_sol_reserves, real_token_reserves, token_total_supply) VALUES ('{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', {}, {}, {}, {})",
            self.database,
            event.signature,
            event.timestamp.format("%Y-%m-%d %H:%M:%S%.3f"),
            event.name.replace("'", "''"),
            event.symbol.replace("'", "''"),
            event.uri.replace("'", "''"),
            event.mint,
            event.bonding_curve,
            event.user,
            event.creator,
            event.virtual_token_reserves,
            event.virtual_sol_reserves,
            event.real_token_reserves,
            event.token_total_supply
        );

        self.execute_sql(&sql).await?;
        Ok(())
    }







    /// 执行SQL命令
    async fn execute_sql(&self, sql: &str) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        self.execute_sql_with_database(sql, Some(&self.database)).await
    }

    /// 执行SQL命令，可选择是否指定数据库
    async fn execute_sql_with_database(&self, sql: &str, database: Option<&str>) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        use std::process::Command;

        // Windows下docker的完整路径
        let docker_path = if cfg!(windows) {
            "C:\\Program Files\\Docker\\Docker\\resources\\bin\\docker.exe"
        } else {
            "docker"
        };

        let mut args = vec![
            "exec", "clickhouse-server", "clickhouse-client",
            "--user", &self.username,
            "--password", &self.password,
        ];

        // 只有在指定数据库且不是创建数据库语句时才添加--database参数
        if let Some(db) = database {
            if !sql.to_uppercase().contains("CREATE DATABASE") {
                args.extend_from_slice(&["--database", db]);
            }
        }

        args.extend_from_slice(&["--query", sql]);

        let output = Command::new(docker_path)
            .args(&args)
            .output()?;

        if !output.status.success() {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            error!("ClickHouse执行失败: {}", error_msg);
            return Err(format!("ClickHouse执行失败: {}", error_msg).into());
        }

        Ok(())
    }
}

/// 初始化数据库客户端
pub async fn init_database_client(url: String, username: String, password: String, database: String) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    let client = DatabaseClient::new(url, username, password, database);

    // 自动初始化数据库和表结构
    if let Err(e) = client.init_database_schema().await {
        error!("数据库表结构初始化失败: {}", e);
        error!("系统将继续运行，但可能无法正常写入数据");
    }

    DATABASE_CLIENT.set(client).map_err(|_| "数据库客户端已经初始化")?;
    info!("数据库客户端初始化成功");
    Ok(())
}

/// 获取数据库客户端
pub fn get_database_client() -> Option<&'static DatabaseClient> {
    DATABASE_CLIENT.get()
}

/// 插入Pump创建事件的便捷函数
pub async fn insert_pump_create_event(event: PumpCreateEvent) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    if let Some(client) = get_database_client() {
        client.insert_pump_create_event(&event).await
    } else {
        error!("数据库客户端未初始化");
        Ok(()) // 不阻止系统运行
    }
}



/// 插入代币交易记录到专属表的便捷函数（带序列号）
pub async fn insert_token_trade_with_sequence(mint_address: String, trade_record: PumpTradeRecord, sequence_id: u64) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    if let Some(client) = get_database_client() {
        client.insert_token_trade_with_sequence(&mint_address, &trade_record, sequence_id).await
    } else {
        error!("数据库客户端未初始化");
        Ok(()) // 不阻止系统运行
    }
}

/// 插入代币交易记录到专属表的便捷函数（向后兼容）
pub async fn insert_token_trade(mint_address: String, trade_record: PumpTradeRecord) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    // 使用默认序列号0来保持向后兼容
    insert_token_trade_with_sequence(mint_address, trade_record, 0).await
}
