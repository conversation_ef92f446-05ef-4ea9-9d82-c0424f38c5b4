running: "sh" "-c" "exec \"$0\" \"$@\"" "C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\protobuf-src-1.1.0+21.5\\protobuf\\configure" "--prefix=D:\\personal\\Desktop\\aibot\\taoli\\target\\release\\build\\protobuf-src-35150cc661cf27c2\\out\\install" "--prefix=/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install" "--srcdir=/c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf" "--disable-shared" "--enable-static" "--disable-maintainer-mode"
checking whether to enable maintainer-specific portions of Makefiles... no
checking build system type... x86_64-pc-mingw64
checking host system type... x86_64-pc-mingw64
checking target system type... x86_64-pc-mingw64
checking for a BSD-compatible install... /usr/bin/install -c
checking whether build environment is sane... yes
checking for a race-free mkdir -p... /usr/bin/mkdir -p
checking for gawk... gawk
checking whether make sets $(MAKE)... yes
checking whether make supports nested variables... yes
checking whether UID '197108' is supported by ustar format... yes
checking whether GID '197121' is supported by ustar format... yes
checking how to create a ustar tar archive... gnutar
checking whether make supports nested variables... (cached) yes
checking for gcc... gcc.exe
checking whether the C compiler works... yes
checking for C compiler default output file name... a.exe
checking for suffix of executables... .exe
checking whether we are cross compiling... no
checking for suffix of object files... o
checking whether the compiler supports GNU C... yes
checking whether gcc.exe accepts -g... yes
checking for gcc.exe option to enable C11 features... none needed
checking whether gcc.exe understands -c and -o together... yes
checking whether make supports the include directive... yes (GNU style)
checking dependency style of gcc.exe... gcc3
checking whether the compiler supports GNU C++... yes
checking whether g++.exe accepts -g... yes
checking for g++.exe option to enable C++11 features... none needed
checking dependency style of g++.exe... gcc3
checking how to run the C preprocessor... gcc.exe -E
checking for gcc... gcc
checking whether the compiler supports GNU C... (cached) yes
checking whether gcc accepts -g... yes
checking for gcc option to enable C11 features... (cached) none needed
checking whether gcc understands -c and -o together... (cached) yes
checking dependency style of gcc... (cached) gcc3
checking how to run the C preprocessor... gcc -E
checking how to run the C++ preprocessor... g++.exe -E
checking for g++... g++
checking whether the compiler supports GNU C++... (cached) yes
checking whether g++ accepts -g... yes
checking for g++ option to enable C++11 features... (cached) none needed
checking dependency style of g++... (cached) gcc3
checking how to run the C++ preprocessor... g++ -E
checking for stdio.h... yes
checking for stdlib.h... yes
checking for string.h... yes
checking for inttypes.h... yes
checking for stdint.h... yes
checking for strings.h... yes
checking for sys/stat.h... yes
checking for sys/types.h... yes
checking for unistd.h... yes
checking for wchar.h... yes
checking for minix/config.h... no
checking whether it is safe to define __EXTENSIONS__... yes
checking whether _XOPEN_SOURCE should be defined... no
checking for ar... ar
checking the archiver (ar) interface... ar
checking C++ compiler flags...... use user-supplied: -O0 -ffunction-sections -fdata-sections -m64
checking for g++.exe options needed to detect all undeclared functions... none needed
checking whether __SUNPRO_CC is declared... no
checking how to print strings... printf
checking for a sed that does not truncate output... /usr/bin/sed
checking for grep that handles long lines and -e... /usr/bin/grep
checking for egrep... /usr/bin/grep -E
checking for fgrep... /usr/bin/grep -F
checking for ld used by gcc.exe... C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe
checking if the linker (C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe) is GNU ld... yes
checking for BSD- or MS-compatible name lister (nm)... /mingw64/bin/nm -B
checking the name lister (/mingw64/bin/nm -B) interface... BSD nm
checking whether ln -s works... no, using cp -pR
checking the maximum length of command line arguments... 8192
checking how to convert x86_64-pc-mingw64 file names to x86_64-pc-mingw64 format... func_convert_file_msys_to_w32
checking how to convert x86_64-pc-mingw64 file names to toolchain format... func_convert_file_msys_to_w32
checking for C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe option to reload object files... -r
checking for file... file
checking for objdump... objdump
checking how to recognize dependent libraries... file_magic ^x86 archive import|^x86 DLL
checking for dlltool... dlltool
checking how to associate runtime and link libraries... func_cygming_dll_for_implib
checking for archiver @FILE support... @
checking for strip... strip
checking for ranlib... ranlib
checking command to parse /mingw64/bin/nm -B output from gcc.exe object... ok
checking for sysroot... no
checking for a working dd... /usr/bin/dd
checking how to truncate binary pipes... /usr/bin/dd bs=4096 count=1
checking for mt... no
checking if : is a manifest tool... no
checking for dlfcn.h... no
checking for objdir... .libs
checking if gcc.exe supports -fno-rtti -fno-exceptions... no
checking for gcc.exe option to produce PIC... -DDLL_EXPORT -DPIC
checking if gcc.exe PIC flag -DDLL_EXPORT -DPIC works... yes
checking if gcc.exe static flag -static works... yes
checking if gcc.exe supports -c -o file.o... yes
checking if gcc.exe supports -c -o file.o... (cached) yes
checking whether the gcc.exe linker (C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe) supports shared libraries... yes
checking dynamic linker characteristics... Win32 ld.exe
checking how to hardcode library paths into programs... immediate
checking whether stripping libraries is possible... yes
checking if libtool supports shared libraries... yes
checking whether to build shared libraries... no
checking whether to build static libraries... yes
checking how to run the C++ preprocessor... g++.exe -E
checking for ld used by g++.exe... C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe
checking if the linker (C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe) is GNU ld... yes
checking whether the g++.exe linker (C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe) supports shared libraries... yes
checking for g++.exe option to produce PIC... -DDLL_EXPORT -DPIC
checking if g++.exe PIC flag -DDLL_EXPORT -DPIC works... yes
checking if g++.exe static flag -static works... yes
checking if g++.exe supports -c -o file.o... yes
checking if g++.exe supports -c -o file.o... (cached) yes
checking whether the g++.exe linker (C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe) supports shared libraries... yes
checking dynamic linker characteristics... Win32 ld.exe
checking how to hardcode library paths into programs... immediate
checking whether the linker supports version scripts... yes
checking for egrep... (cached) /usr/bin/grep -E
checking for fcntl.h... yes
checking for inttypes.h... (cached) yes
checking for limits.h... yes
checking for stdlib.h... (cached) yes
checking for unistd.h... (cached) yes
checking for working memcmp... yes
checking for working strtod... yes
checking for ftruncate... yes
checking for memset... yes
checking for mkdir... yes
checking for strchr... yes
checking for strerror... yes
checking for strtol... yes
checking zlib version... ok (******* or later)
checking for library containing zlibVersion... -lz
checking whether g++.exe supports C++11 features by default... yes
checking whether -latomic is needed... no
checking whether gcc.exe is Clang... no
checking whether pthreads work with "-pthread" and "-lpthread"... yes
checking for joinable pthread attribute... PTHREAD_CREATE_JOINABLE
checking whether more special flags are required for pthreads... no
checking for PTHREAD_PRIO_INHERIT... yes
checking the location of hash_map... <unordered_map>
checking whether -llog is needed... no
checking that generated files are newer than configure... done
configure: creating ./config.status
config.status: creating Makefile
config.status: creating src/Makefile
config.status: creating benchmarks/Makefile
config.status: creating conformance/Makefile
config.status: creating protobuf.pc
config.status: creating protobuf-lite.pc
config.status: creating config.h
config.status: executing depfiles commands
config.status: executing libtool commands
=== configuring in third_party/googletest (/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/build/third_party/googletest)
configure: running /bin/sh /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/third_party/googletest/configure --disable-option-checking '--prefix=/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install'  '--disable-shared' '--enable-static' '--disable-maintainer-mode' 'CC=gcc.exe' 'CFLAGS=-O0 -ffunction-sections -fdata-sections -m64' 'CXX=g++.exe' 'CXXFLAGS=-O0 -ffunction-sections -fdata-sections -m64' --cache-file=/dev/null --srcdir=/c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/third_party/googletest
checking for a BSD-compatible install... /usr/bin/install -c
checking whether build environment is sane... yes
checking for a race-free mkdir -p... /usr/bin/mkdir -p
checking for gawk... gawk
checking whether make sets $(MAKE)... yes
checking whether make supports nested variables... yes
checking that generated files are newer than configure... done
configure: creating ./config.status
config.status: creating Makefile
=== configuring in googletest (/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/build/third_party/googletest/googletest)
configure: running /bin/sh /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/third_party/googletest/googletest/configure --disable-option-checking '--prefix=/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install'  '--disable-shared' '--enable-static' '--disable-maintainer-mode' 'CC=gcc.exe' 'CFLAGS=-O0 -ffunction-sections -fdata-sections -m64' 'CXX=g++.exe' 'CXXFLAGS=-O0 -ffunction-sections -fdata-sections -m64' --cache-file=/dev/null --srcdir=/c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/third_party/googletest/googletest
checking for a BSD-compatible install... /usr/bin/install -c
checking whether build environment is sane... yes
checking for a race-free mkdir -p... /usr/bin/mkdir -p
checking for gawk... gawk
checking whether make sets $(MAKE)... yes
checking whether make supports nested variables... yes
checking for gcc... gcc.exe
checking whether the C compiler works... yes
checking for C compiler default output file name... a.exe
checking for suffix of executables... .exe
checking whether we are cross compiling... no
checking for suffix of object files... o
checking whether the compiler supports GNU C... yes
checking whether gcc.exe accepts -g... yes
checking for gcc.exe option to enable C11 features... none needed
checking whether gcc.exe understands -c and -o together... yes
checking whether make supports the include directive... yes (GNU style)
checking dependency style of gcc.exe... gcc3
checking whether the compiler supports GNU C++... yes
checking whether g++.exe accepts -g... yes
checking for g++.exe option to enable C++11 features... none needed
checking dependency style of g++.exe... gcc3
checking build system type... x86_64-pc-mingw64
checking host system type... x86_64-pc-mingw64
checking how to print strings... printf
checking for a sed that does not truncate output... /usr/bin/sed
checking for grep that handles long lines and -e... /usr/bin/grep
checking for egrep... /usr/bin/grep -E
checking for fgrep... /usr/bin/grep -F
checking for ld used by gcc.exe... C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe
checking if the linker (C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe) is GNU ld... yes
checking for BSD- or MS-compatible name lister (nm)... /mingw64/bin/nm -B
checking the name lister (/mingw64/bin/nm -B) interface... BSD nm
checking whether ln -s works... no, using cp -pR
checking the maximum length of command line arguments... 8192
checking how to convert x86_64-pc-mingw64 file names to x86_64-pc-mingw64 format... func_convert_file_msys_to_w32
checking how to convert x86_64-pc-mingw64 file names to toolchain format... func_convert_file_msys_to_w32
checking for C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe option to reload object files... -r
checking for file... file
checking for objdump... objdump
checking how to recognize dependent libraries... file_magic ^x86 archive import|^x86 DLL
checking for dlltool... dlltool
checking how to associate runtime and link libraries... func_cygming_dll_for_implib
checking for ar... ar
checking for archiver @FILE support... @
checking for strip... strip
checking for ranlib... ranlib
checking command to parse /mingw64/bin/nm -B output from gcc.exe object... ok
checking for sysroot... no
checking for a working dd... /usr/bin/dd
checking how to truncate binary pipes... /usr/bin/dd bs=4096 count=1
checking for mt... no
checking if : is a manifest tool... no
checking for stdio.h... yes
checking for stdlib.h... yes
checking for string.h... yes
checking for inttypes.h... yes
checking for stdint.h... yes
checking for strings.h... yes
checking for sys/stat.h... yes
checking for sys/types.h... yes
checking for unistd.h... yes
checking for dlfcn.h... no
checking for objdir... .libs
checking if gcc.exe supports -fno-rtti -fno-exceptions... no
checking for gcc.exe option to produce PIC... -DDLL_EXPORT -DPIC
checking if gcc.exe PIC flag -DDLL_EXPORT -DPIC works... yes
checking if gcc.exe static flag -static works... yes
checking if gcc.exe supports -c -o file.o... yes
checking if gcc.exe supports -c -o file.o... (cached) yes
checking whether the gcc.exe linker (C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe) supports shared libraries... yes
checking dynamic linker characteristics... Win32 ld.exe
checking how to hardcode library paths into programs... immediate
checking whether stripping libraries is possible... yes
checking if libtool supports shared libraries... yes
checking whether to build shared libraries... no
checking whether to build static libraries... yes
checking how to run the C++ preprocessor... g++.exe -E
checking for ld used by g++.exe... C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe
checking if the linker (C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe) is GNU ld... yes
checking whether the g++.exe linker (C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe) supports shared libraries... yes
checking for g++.exe option to produce PIC... -DDLL_EXPORT -DPIC
checking if g++.exe PIC flag -DDLL_EXPORT -DPIC works... yes
checking if g++.exe static flag -static works... yes
checking if g++.exe supports -c -o file.o... yes
checking if g++.exe supports -c -o file.o... (cached) yes
checking whether the g++.exe linker (C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe) supports shared libraries... yes
checking dynamic linker characteristics... Win32 ld.exe
checking how to hardcode library paths into programs... immediate
checking for python... /e/Python/python
checking for the pthreads library -lpthreads... no
checking whether pthreads work without any flags... yes
checking for joinable pthread attribute... PTHREAD_CREATE_JOINABLE
checking if more special flags are required for pthreads... no
checking whether to check for GCC pthread/shared inconsistencies... no
checking that generated files are newer than configure... done
configure: creating ./config.status
config.status: creating Makefile
config.status: creating scripts/gtest-config
config.status: creating build-aux/config.h
config.status: executing depfiles commands
config.status: executing libtool commands
=== configuring in googlemock (/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/build/third_party/googletest/googlemock)
configure: running /bin/sh /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/third_party/googletest/googlemock/configure --disable-option-checking '--prefix=/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install'  '--disable-shared' '--enable-static' '--disable-maintainer-mode' 'CC=gcc.exe' 'CFLAGS=-O0 -ffunction-sections -fdata-sections -m64' 'CXX=g++.exe' 'CXXFLAGS=-O0 -ffunction-sections -fdata-sections -m64' --cache-file=/dev/null --srcdir=/c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/third_party/googletest/googlemock
checking for a BSD-compatible install... /usr/bin/install -c
checking whether build environment is sane... yes
checking for a race-free mkdir -p... /usr/bin/mkdir -p
checking for gawk... gawk
checking whether make sets $(MAKE)... yes
checking whether make supports nested variables... yes
checking for gcc... gcc.exe
checking whether the C compiler works... yes
checking for C compiler default output file name... a.exe
checking for suffix of executables... .exe
checking whether we are cross compiling... no
checking for suffix of object files... o
checking whether the compiler supports GNU C... yes
checking whether gcc.exe accepts -g... yes
checking for gcc.exe option to enable C11 features... none needed
checking whether gcc.exe understands -c and -o together... yes
checking whether make supports the include directive... yes (GNU style)
checking dependency style of gcc.exe... gcc3
checking whether the compiler supports GNU C++... yes
checking whether g++.exe accepts -g... yes
checking for g++.exe option to enable C++11 features... none needed
checking dependency style of g++.exe... gcc3
checking build system type... x86_64-pc-mingw64
checking host system type... x86_64-pc-mingw64
checking how to print strings... printf
checking for a sed that does not truncate output... /usr/bin/sed
checking for grep that handles long lines and -e... /usr/bin/grep
checking for egrep... /usr/bin/grep -E
checking for fgrep... /usr/bin/grep -F
checking for ld used by gcc.exe... C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe
checking if the linker (C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe) is GNU ld... yes
checking for BSD- or MS-compatible name lister (nm)... /mingw64/bin/nm -B
checking the name lister (/mingw64/bin/nm -B) interface... BSD nm
checking whether ln -s works... no, using cp -pR
checking the maximum length of command line arguments... 8192
checking how to convert x86_64-pc-mingw64 file names to x86_64-pc-mingw64 format... func_convert_file_msys_to_w32
checking how to convert x86_64-pc-mingw64 file names to toolchain format... func_convert_file_msys_to_w32
checking for C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe option to reload object files... -r
checking for file... file
checking for objdump... objdump
checking how to recognize dependent libraries... file_magic ^x86 archive import|^x86 DLL
checking for dlltool... dlltool
checking how to associate runtime and link libraries... func_cygming_dll_for_implib
checking for ar... ar
checking for archiver @FILE support... @
checking for strip... strip
checking for ranlib... ranlib
checking command to parse /mingw64/bin/nm -B output from gcc.exe object... ok
checking for sysroot... no
checking for a working dd... /usr/bin/dd
checking how to truncate binary pipes... /usr/bin/dd bs=4096 count=1
checking for mt... no
checking if : is a manifest tool... no
checking for stdio.h... yes
checking for stdlib.h... yes
checking for string.h... yes
checking for inttypes.h... yes
checking for stdint.h... yes
checking for strings.h... yes
checking for sys/stat.h... yes
checking for sys/types.h... yes
checking for unistd.h... yes
checking for dlfcn.h... no
checking for objdir... .libs
checking if gcc.exe supports -fno-rtti -fno-exceptions... no
checking for gcc.exe option to produce PIC... -DDLL_EXPORT -DPIC
checking if gcc.exe PIC flag -DDLL_EXPORT -DPIC works... yes
checking if gcc.exe static flag -static works... yes
checking if gcc.exe supports -c -o file.o... yes
checking if gcc.exe supports -c -o file.o... (cached) yes
checking whether the gcc.exe linker (C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe) supports shared libraries... yes
checking dynamic linker characteristics... Win32 ld.exe
checking how to hardcode library paths into programs... immediate
checking whether stripping libraries is possible... yes
checking if libtool supports shared libraries... yes
checking whether to build shared libraries... no
checking whether to build static libraries... yes
checking how to run the C++ preprocessor... g++.exe -E
checking for ld used by g++.exe... C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe
checking if the linker (C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe) is GNU ld... yes
checking whether the g++.exe linker (C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe) supports shared libraries... yes
checking for g++.exe option to produce PIC... -DDLL_EXPORT -DPIC
checking if g++.exe PIC flag -DDLL_EXPORT -DPIC works... yes
checking if g++.exe static flag -static works... yes
checking if g++.exe supports -c -o file.o... yes
checking if g++.exe supports -c -o file.o... (cached) yes
checking whether the g++.exe linker (C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe) supports shared libraries... yes
checking dynamic linker characteristics... Win32 ld.exe
checking how to hardcode library paths into programs... immediate
checking for python... /e/Python/python
checking for the pthreads library -lpthreads... no
checking whether pthreads work without any flags... yes
checking for joinable pthread attribute... PTHREAD_CREATE_JOINABLE
checking if more special flags are required for pthreads... no
checking whether to check for GCC pthread/shared inconsistencies... no
checking for gtest-config... no
checking that generated files are newer than configure... done
configure: creating ./config.status
config.status: creating Makefile
config.status: creating scripts/gmock-config
config.status: creating build-aux/config.h
config.status: executing depfiles commands
config.status: executing libtool commands
running: "sh" "-c" "exec \"$0\" \"$@\"" "make" "install" "-j20"
Making install in .
make[1]: 进入目录“/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/build”
make[2]: 进入目录“/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/build”
make[2]: 对“install-exec-am”无需做任何事。
 /usr/bin/mkdir -p '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/lib/pkgconfig'
 /usr/bin/install -c -m 644 protobuf.pc protobuf-lite.pc '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/lib/pkgconfig'
make[2]: 离开目录“/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/build”
make[1]: 离开目录“/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/build”
Making install in src
make[1]: 进入目录“/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/build/src”
  CXX      google/protobuf/wire_format_lite.lo
  CXX      google/protobuf/any.lo
  CXX      google/protobuf/any.pb.lo
  CXX      google/protobuf/api.pb.lo
  CXX      google/protobuf/compiler/importer.lo
  CXX      google/protobuf/compiler/parser.lo
  CXX      google/protobuf/descriptor.lo
  CXX      google/protobuf/descriptor.pb.lo
  CXX      google/protobuf/descriptor_database.lo
  CXX      google/protobuf/duration.pb.lo
  CXX      google/protobuf/dynamic_message.lo
  CXX      google/protobuf/empty.pb.lo
  CXX      google/protobuf/extension_set_heavy.lo
  CXX      google/protobuf/field_mask.pb.lo
  CXX      google/protobuf/generated_message_bases.lo
  CXX      google/protobuf/generated_message_reflection.lo
  CXX      google/protobuf/generated_message_tctable_full.lo
  CXX      google/protobuf/io/gzip_stream.lo
  CXX      google/protobuf/io/printer.lo
  CXX      google/protobuf/io/tokenizer.lo
  CXX      google/protobuf/map_field.lo
  CXX      google/protobuf/message.lo
  CXX      google/protobuf/reflection_ops.lo
  CXX      google/protobuf/service.lo
  CXX      google/protobuf/source_context.pb.lo
  CXX      google/protobuf/struct.pb.lo
  CXX      google/protobuf/stubs/substitute.lo
  CXX      google/protobuf/text_format.lo
  CXX      google/protobuf/timestamp.pb.lo
  CXX      google/protobuf/type.pb.lo
  CXX      google/protobuf/unknown_field_set.lo
  CXX      google/protobuf/util/json_util.lo
  CXX      google/protobuf/util/message_differencer.lo
  CXX      google/protobuf/util/time_util.lo
  CXX      google/protobuf/util/type_resolver_util.lo
  CXX      google/protobuf/wire_format.lo
  CXX      google/protobuf/wrappers.pb.lo
  CXX      google/protobuf/compiler/code_generator.lo
  CXX      google/protobuf/compiler/command_line_interface.lo
  CXX      google/protobuf/compiler/plugin.lo
  CXX      google/protobuf/compiler/plugin.pb.lo
  CXX      google/protobuf/compiler/subprocess.lo
  CXX      google/protobuf/compiler/zip_writer.lo
  CXX      google/protobuf/compiler/main.o
  CXX      google/protobuf/any_lite.lo
  CXX      google/protobuf/arena.lo
  CXX      google/protobuf/arenastring.lo
  CXX      google/protobuf/arenaz_sampler.lo
  CXX      google/protobuf/extension_set.lo
  CXX      google/protobuf/generated_enum_util.lo
  CXX      google/protobuf/generated_message_tctable_lite.lo
  CXX      google/protobuf/generated_message_util.lo
  CXX      google/protobuf/implicit_weak_message.lo
  CXX      google/protobuf/inlined_string_field.lo
  CXX      google/protobuf/io/coded_stream.lo
  CXX      google/protobuf/io/io_win32.lo
  CXX      google/protobuf/io/strtod.lo
  CXX      google/protobuf/io/zero_copy_stream.lo
  CXX      google/protobuf/io/zero_copy_stream_impl.lo
  CXX      google/protobuf/io/zero_copy_stream_impl_lite.lo
  CXX      google/protobuf/map.lo
  CXX      google/protobuf/message_lite.lo
  CXX      google/protobuf/parse_context.lo
  CXX      google/protobuf/repeated_field.lo
  CXX      google/protobuf/repeated_ptr_field.lo
  CXX      google/protobuf/stubs/bytestream.lo
  CXX      google/protobuf/stubs/common.lo
  CXX      google/protobuf/stubs/int128.lo
  CXX      google/protobuf/stubs/status.lo
  CXX      google/protobuf/stubs/statusor.lo
  CXX      google/protobuf/stubs/stringpiece.lo
  CXX      google/protobuf/stubs/stringprintf.lo
  CXX      google/protobuf/stubs/structurally_valid.lo
  CXX      google/protobuf/stubs/strutil.lo
  CXX      google/protobuf/stubs/time.lo
  CXX      google/protobuf/util/delimited_message_util.lo
  CXX      google/protobuf/util/field_comparator.lo
  CXX      google/protobuf/util/field_mask_util.lo
  CXX      google/protobuf/util/internal/datapiece.lo
  CXX      google/protobuf/util/internal/default_value_objectwriter.lo
  CXX      google/protobuf/util/internal/error_listener.lo
  CXX      google/protobuf/util/internal/field_mask_utility.lo
  CXX      google/protobuf/util/internal/json_escaping.lo
  CXX      google/protobuf/util/internal/json_objectwriter.lo
  CXX      google/protobuf/util/internal/json_stream_parser.lo
  CXX      google/protobuf/util/internal/object_writer.lo
  CXX      google/protobuf/util/internal/proto_writer.lo
  CXX      google/protobuf/util/internal/protostream_objectsource.lo
  CXX      google/protobuf/util/internal/protostream_objectwriter.lo
  CXX      google/protobuf/util/internal/type_info.lo
  CXX      google/protobuf/util/internal/utility.lo
  CXX      google/protobuf/compiler/cpp/enum.lo
  CXX      google/protobuf/compiler/cpp/enum_field.lo
  CXX      google/protobuf/compiler/cpp/extension.lo
  CXX      google/protobuf/compiler/cpp/field.lo
  CXX      google/protobuf/compiler/cpp/file.lo
  CXX      google/protobuf/compiler/cpp/generator.lo
  CXX      google/protobuf/compiler/cpp/helpers.lo
  CXX      google/protobuf/compiler/cpp/map_field.lo
  CXX      google/protobuf/compiler/cpp/message.lo
  CXX      google/protobuf/compiler/cpp/message_field.lo
  CXX      google/protobuf/compiler/cpp/padding_optimizer.lo
  CXX      google/protobuf/compiler/cpp/parse_function_generator.lo
  CXX      google/protobuf/compiler/cpp/primitive_field.lo
  CXX      google/protobuf/compiler/cpp/service.lo
  CXX      google/protobuf/compiler/cpp/string_field.lo
  CXX      google/protobuf/compiler/csharp/csharp_doc_comment.lo
  CXX      google/protobuf/compiler/csharp/csharp_enum.lo
  CXX      google/protobuf/compiler/csharp/csharp_enum_field.lo
  CXX      google/protobuf/compiler/csharp/csharp_field_base.lo
  CXX      google/protobuf/compiler/csharp/csharp_generator.lo
  CXX      google/protobuf/compiler/csharp/csharp_helpers.lo
  CXX      google/protobuf/compiler/csharp/csharp_map_field.lo
  CXX      google/protobuf/compiler/csharp/csharp_message.lo
  CXX      google/protobuf/compiler/csharp/csharp_message_field.lo
  CXX      google/protobuf/compiler/csharp/csharp_primitive_field.lo
  CXX      google/protobuf/compiler/csharp/csharp_reflection_class.lo
  CXX      google/protobuf/compiler/csharp/csharp_repeated_enum_field.lo
  CXX      google/protobuf/compiler/csharp/csharp_repeated_message_field.lo
  CXX      google/protobuf/compiler/csharp/csharp_repeated_primitive_field.lo
  CXX      google/protobuf/compiler/csharp/csharp_source_generator_base.lo
  CXX      google/protobuf/compiler/csharp/csharp_wrapper_field.lo
  CXX      google/protobuf/compiler/java/context.lo
  CXX      google/protobuf/compiler/java/doc_comment.lo
  CXX      google/protobuf/compiler/java/enum.lo
  CXX      google/protobuf/compiler/java/enum_field.lo
  CXX      google/protobuf/compiler/java/enum_field_lite.lo
  CXX      google/protobuf/compiler/java/enum_lite.lo
  CXX      google/protobuf/compiler/java/extension.lo
  CXX      google/protobuf/compiler/java/extension_lite.lo
  CXX      google/protobuf/compiler/java/field.lo
  CXX      google/protobuf/compiler/java/file.lo
  CXX      google/protobuf/compiler/java/generator.lo
  CXX      google/protobuf/compiler/java/generator_factory.lo
  CXX      google/protobuf/compiler/java/helpers.lo
  CXX      google/protobuf/compiler/java/kotlin_generator.lo
  CXX      google/protobuf/compiler/java/map_field.lo
  CXX      google/protobuf/compiler/java/map_field_lite.lo
  CXX      google/protobuf/compiler/java/message.lo
  CXX      google/protobuf/compiler/java/message_builder.lo
  CXX      google/protobuf/compiler/java/message_builder_lite.lo
  CXX      google/protobuf/compiler/java/message_field.lo
  CXX      google/protobuf/compiler/java/message_field_lite.lo
  CXX      google/protobuf/compiler/java/message_lite.lo
  CXX      google/protobuf/compiler/java/name_resolver.lo
  CXX      google/protobuf/compiler/java/primitive_field.lo
  CXX      google/protobuf/compiler/java/primitive_field_lite.lo
  CXX      google/protobuf/compiler/java/service.lo
  CXX      google/protobuf/compiler/java/shared_code_generator.lo
  CXX      google/protobuf/compiler/java/string_field.lo
  CXX      google/protobuf/compiler/java/string_field_lite.lo
  CXX      google/protobuf/compiler/objectivec/objectivec_enum.lo
  CXX      google/protobuf/compiler/objectivec/objectivec_enum_field.lo
  CXX      google/protobuf/compiler/objectivec/objectivec_extension.lo
  CXX      google/protobuf/compiler/objectivec/objectivec_field.lo
  CXX      google/protobuf/compiler/objectivec/objectivec_file.lo
  CXX      google/protobuf/compiler/objectivec/objectivec_generator.lo
  CXX      google/protobuf/compiler/objectivec/objectivec_helpers.lo
  CXX      google/protobuf/compiler/objectivec/objectivec_map_field.lo
  CXX      google/protobuf/compiler/objectivec/objectivec_message.lo
  CXX      google/protobuf/compiler/objectivec/objectivec_message_field.lo
  CXX      google/protobuf/compiler/objectivec/objectivec_oneof.lo
  CXX      google/protobuf/compiler/objectivec/objectivec_primitive_field.lo
  CXX      google/protobuf/compiler/php/php_generator.lo
  CXX      google/protobuf/compiler/python/generator.lo
  CXX      google/protobuf/compiler/python/helpers.lo
  CXX      google/protobuf/compiler/python/pyi_generator.lo
  CXX      google/protobuf/compiler/ruby/ruby_generator.lo
  CXXLD    libprotobuf-lite.la
  CXXLD    libprotobuf.la
copying selected object files to avoid basename conflicts...
copying selected object files to avoid basename conflicts...
  CXXLD    libprotoc.la
copying selected object files to avoid basename conflicts...
  CXXLD    protoc.exe
make[2]: 进入目录“/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/build/src”
 /usr/bin/mkdir -p '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/lib'
 /usr/bin/mkdir -p '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/include'
 /bin/sh ../libtool   --mode=install /usr/bin/install -c   libprotobuf-lite.la libprotobuf.la libprotoc.la '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/lib'
 /usr/bin/mkdir -p '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/include'
 /usr/bin/mkdir -p '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/include/google/protobuf'
 /usr/bin/install -c -m 644  /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/any.proto /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/api.proto /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/descriptor.proto /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/duration.proto /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/empty.proto /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/field_mask.proto /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/source_context.proto /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/struct.proto /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/timestamp.proto /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/type.proto /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/wrappers.proto '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/include/google/protobuf'
 /usr/bin/mkdir -p '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/include/google/protobuf'
libtool: install: /usr/bin/install -c .libs/libprotobuf-lite.lai /d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/lib/libprotobuf-lite.la
 /usr/bin/install -c -m 644  /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/any.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/any.pb.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/api.pb.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/arena.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/arena_impl.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/arenastring.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/arenaz_sampler.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/descriptor.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/descriptor.pb.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/descriptor_database.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/duration.pb.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/dynamic_message.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/empty.pb.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/endian.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/explicitly_constructed.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/extension_set.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/extension_set_inl.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/field_access_listener.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/field_mask.pb.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/generated_enum_reflection.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/generated_enum_util.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/generated_message_bases.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/generated_message_reflection.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/generated_message_tctable_decl.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/generated_message_tctable_impl.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/generated_message_util.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/has_bits.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/implicit_weak_message.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/inlined_string_field.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/map.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/map_entry.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/map_entry_lite.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/map_field.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/map_field_inl.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/map_field_lite.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/map_type_handler.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/message.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/message_lite.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/metadata.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/metadata_lite.h '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/include/google/protobuf'
 /usr/bin/mkdir -p '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/include/google/protobuf/compiler'
 /usr/bin/install -c -m 644  /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/compiler/plugin.proto '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/include/google/protobuf/compiler'
libtool: install: /usr/bin/install -c .libs/libprotobuf.lai /d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/lib/libprotobuf.la
 /usr/bin/mkdir -p '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/include/google/protobuf/compiler/java'
 /usr/bin/install -c -m 644  /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/compiler/java/generator.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/compiler/java/java_generator.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/compiler/java/kotlin_generator.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/compiler/java/names.h '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/include/google/protobuf/compiler/java'
 /usr/bin/mkdir -p '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/include/google/protobuf/compiler/cpp'
libtool: install: /usr/bin/install -c .libs/libprotoc.lai /d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/lib/libprotoc.la
 /usr/bin/install -c -m 644  /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/compiler/cpp/cpp_generator.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/compiler/cpp/file.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/compiler/cpp/generator.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/compiler/cpp/helpers.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/compiler/cpp/names.h '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/include/google/protobuf/compiler/cpp'
 /usr/bin/mkdir -p '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/include/google/protobuf/compiler/python'
libtool: install: /usr/bin/install -c .libs/libprotobuf-lite.a /d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/lib/libprotobuf-lite.a
 /usr/bin/install -c -m 644  /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/compiler/python/generator.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/compiler/python/pyi_generator.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/compiler/python/python_generator.h '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/include/google/protobuf/compiler/python'
 /usr/bin/mkdir -p '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/include/google/protobuf'
 /usr/bin/install -c -m 644  /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/parse_context.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/port.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/port_def.inc /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/port_undef.inc /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/reflection.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/reflection_internal.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/reflection_ops.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/repeated_field.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/repeated_ptr_field.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/service.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/source_context.pb.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/struct.pb.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/text_format.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/timestamp.pb.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/type.pb.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/unknown_field_set.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/wire_format.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/wire_format_lite.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/wrappers.pb.h '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/include/google/protobuf'
libtool: install: chmod 644 /d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/lib/libprotobuf-lite.a
 /usr/bin/mkdir -p '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/include/google/protobuf/stubs'
 /usr/bin/install -c -m 644  /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/stubs/bytestream.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/stubs/callback.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/stubs/casts.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/stubs/common.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/stubs/hash.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/stubs/logging.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/stubs/macros.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/stubs/map_util.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/stubs/mutex.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/stubs/once.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/stubs/platform_macros.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/stubs/port.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/stubs/status.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/stubs/stl_util.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/stubs/stringpiece.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/stubs/strutil.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/stubs/template_util.h '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/include/google/protobuf/stubs'
libtool: install: ranlib /d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/lib/libprotobuf-lite.a
 /usr/bin/mkdir -p '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/include/google/protobuf/util'
 /usr/bin/install -c -m 644  /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/util/delimited_message_util.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/util/field_comparator.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/util/field_mask_util.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/util/json_util.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/util/message_differencer.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/util/time_util.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/util/type_resolver.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/util/type_resolver_util.h '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/include/google/protobuf/util'
 /usr/bin/mkdir -p '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/include/google/protobuf/compiler/php'
 /usr/bin/install -c -m 644  /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/compiler/php/php_generator.h '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/include/google/protobuf/compiler/php'
libtool: install: /usr/bin/install -c .libs/libprotobuf.a /d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/lib/libprotobuf.a
 /usr/bin/mkdir -p '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/include/google/protobuf/compiler'
 /usr/bin/install -c -m 644  /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/compiler/code_generator.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/compiler/command_line_interface.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/compiler/importer.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/compiler/parser.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/compiler/plugin.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/compiler/plugin.pb.h '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/include/google/protobuf/compiler'
 /usr/bin/mkdir -p '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/include/google/protobuf/compiler/ruby'
libtool: install: chmod 644 /d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/lib/libprotobuf.a
 /usr/bin/install -c -m 644  /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/compiler/ruby/ruby_generator.h '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/include/google/protobuf/compiler/ruby'
 /usr/bin/mkdir -p '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/include/google/protobuf/io'
 /usr/bin/install -c -m 644  /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/io/coded_stream.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/io/gzip_stream.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/io/io_win32.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/io/printer.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/io/strtod.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/io/tokenizer.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/io/zero_copy_stream.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/io/zero_copy_stream_impl.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/io/zero_copy_stream_impl_lite.h '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/include/google/protobuf/io'
libtool: install: ranlib /d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/lib/libprotobuf.a
 /usr/bin/mkdir -p '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/include/google/protobuf/compiler/csharp'
 /usr/bin/install -c -m 644  /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/compiler/csharp/csharp_doc_comment.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/compiler/csharp/csharp_generator.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/compiler/csharp/csharp_names.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/compiler/csharp/csharp_options.h '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/include/google/protobuf/compiler/csharp'
 /usr/bin/mkdir -p '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/include/google/protobuf/compiler/objectivec'
 /usr/bin/install -c -m 644  /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/compiler/objectivec/objectivec_generator.h /c/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/protobuf-src-1.1.0+21.5/protobuf/src/google/protobuf/compiler/objectivec/objectivec_helpers.h '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/include/google/protobuf/compiler/objectivec'
libtool: install: /usr/bin/install -c .libs/libprotoc.a /d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/lib/libprotoc.a
libtool: install: chmod 644 /d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/lib/libprotoc.a
libtool: install: ranlib /d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/lib/libprotoc.a
 /usr/bin/mkdir -p '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/bin'
  /bin/sh ../libtool   --mode=install /usr/bin/install -c protoc.exe '/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/bin'
libtool: install: /usr/bin/install -c protoc.exe /d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/bin/protoc.exe
make[2]: 离开目录“/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/build/src”
make[1]: 离开目录“/d/personal/Desktop/aibot/taoli/target/release/build/protobuf-src-35150cc661cf27c2/out/install/build/src”
cargo:root=D:\personal\Desktop\aibot\taoli\target\release\build\protobuf-src-35150cc661cf27c2\out\install
cargo:rustc-env=INSTALL_DIR=D:\personal\Desktop\aibot\taoli\target\release\build\protobuf-src-35150cc661cf27c2\out\install
cargo:CXXBRIDGE_DIR0=D:\personal\Desktop\aibot\taoli\target\release\build\protobuf-src-35150cc661cf27c2\out\install/include
